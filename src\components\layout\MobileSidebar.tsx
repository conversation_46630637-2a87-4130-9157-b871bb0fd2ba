import { useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  FileText,
  DollarSign,
  BarChart3,
  Settings,
  X,
  Calendar,
  FileCheck,
  ChevronRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface MobileSidebarProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

/**
 * Enhanced mobile sidebar with improved animations and touch support
 */
const MobileSidebar = ({ open, setOpen }: MobileSidebarProps) => {
  const location = useLocation();
  const sidebarRef = useRef<HTMLDivElement>(null);
  const currentPath = location.pathname;

  // Navigation items
  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'General Ledger', href: '/general-ledger', icon: FileText },
    { name: 'Accounts Payable', href: '/accounts-payable', icon: DollarSign },
    { name: 'Accounts Receivable', href: '/accounts-receivable', icon: Calendar },
    { name: 'Budgets', href: '/budgets', icon: FileCheck },
    { name: 'Reports', href: '/reports', icon: BarChart3 },
    { name: 'Settings', href: '/settings', icon: Settings },
  ];

  // Close sidebar
  const closeSidebar = () => {
    setOpen(false);
  };

  // Handle touch swipe to close sidebar
  useEffect(() => {
    const sidebar = sidebarRef.current;
    if (!sidebar) return;

    let startX: number;
    let currentX: number;

    const handleTouchStart = (e: TouchEvent) => {
      startX = e.touches[0].clientX;
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!open) return;
      currentX = e.touches[0].clientX;
      const diffX = currentX - startX;

      // Only allow swiping left to close
      if (diffX < 0) {
        // Apply transform directly for smooth movement
        sidebar.style.transform = `translateX(${diffX}px)`;
      }
    };

    const handleTouchEnd = () => {
      if (!open) return;

      // Reset transform
      sidebar.style.transform = '';

      // If swiped far enough, close the sidebar
      if (currentX && startX - currentX > 80) {
        closeSidebar();
      }
    };

    sidebar.addEventListener('touchstart', handleTouchStart);
    sidebar.addEventListener('touchmove', handleTouchMove);
    sidebar.addEventListener('touchend', handleTouchEnd);

    return () => {
      sidebar.removeEventListener('touchstart', handleTouchStart);
      sidebar.removeEventListener('touchmove', handleTouchMove);
      sidebar.removeEventListener('touchend', handleTouchEnd);
    };
  }, [open, setOpen]);

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (open && sidebarRef.current && !sidebarRef.current.contains(e.target as Node)) {
        closeSidebar();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [open, setOpen]);

  // Handle escape key to close
  useEffect(() => {
    const handleEscKey = (e: KeyboardEvent) => {
      if (open && e.key === 'Escape') {
        closeSidebar();
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [open, setOpen]);

  return (
    <>
      {/* Overlay */}
      <div
        className={cn(
          "fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-300",
          open ? "opacity-100" : "opacity-0 pointer-events-none"
        )}
        onClick={closeSidebar}
        aria-hidden="true"
      />

      {/* Sidebar */}
      <div
        ref={sidebarRef}
        className={cn(
          "fixed top-0 left-0 z-50 h-full w-[280px] bg-sidebar shadow-xl transition-transform duration-300 ease-in-out",
          open ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex items-center justify-between h-16 px-4 border-b border-sidebar-border">
          <div className="text-xl font-semibold text-sidebar-foreground">
            Kaya <span className="text-secondary">Finance</span>
          </div>
          <Button variant="ghost" size="icon" onClick={closeSidebar} className="text-sidebar-foreground">
            <X className="h-5 w-5" />
          </Button>
        </div>

        <nav className="mt-4 px-2">
          <ul className="space-y-1">
            {navigation.map((item) => (
              <li key={item.name}>
                <Link
                  to={item.href}
                  className={cn(
                    "flex items-center justify-between rounded-md px-3 py-3 text-sm transition-all",
                    currentPath === item.href
                      ? "bg-sidebar-primary text-sidebar-primary-foreground font-medium"
                      : "text-sidebar-foreground hover:bg-sidebar-accent"
                  )}
                  onClick={closeSidebar}
                >
                  <div className="flex items-center gap-3">
                    <item.icon className="h-5 w-5" />
                    <span>{item.name}</span>
                  </div>
                  <ChevronRight className={cn(
                    "h-4 w-4 transition-opacity",
                    currentPath === item.href ? "opacity-100" : "opacity-0"
                  )} />
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        <div className="absolute bottom-8 left-0 right-0 px-4">
          <div className="rounded-lg bg-sidebar-accent p-4">
            <h4 className="text-sm font-medium mb-2 text-sidebar-foreground">Need help?</h4>
            <p className="text-xs text-sidebar-muted mb-3">
              Contact support for assistance with your account.
            </p>
            <Button variant="default" size="sm" className="w-full">
              Contact Support
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default MobileSidebar;
