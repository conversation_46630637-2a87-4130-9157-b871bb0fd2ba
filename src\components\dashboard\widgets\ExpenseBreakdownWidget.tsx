import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>A<PERSON><PERSON>, CartesianGrid } from 'recharts';
import { TypedDashboardWidget, ExpenseBreakdownSettings } from '@/types/dashboard';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';

interface ExpenseBreakdownWidgetProps {
  widget: TypedDashboardWidget;
}

interface ExpenseCategory {
  name: string;
  value: number;
  percentage: number;
  color: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#A4DE6C', '#D0ED57', '#F56954', '#8DD1E1'];

export function ExpenseBreakdownWidget({ widget }: ExpenseBreakdownWidgetProps): JSX.Element {
  const settings = widget.widget_settings as ExpenseBreakdownSettings;
  const { currentCompanyId } = useAuth();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<ExpenseCategory[]>([]);
  const [error, setError] = useState<string | null>(null);

  const chartType = settings.chartType || 'pie';
  const showLegend = settings.showLegend !== false;
  const showPercentages = settings.showPercentages !== false;
  const categoryLimit = settings.categoryLimit || 5;
  const period = settings.period || 'monthly';
  const useFiscalYear = settings.useFiscalYear !== false;

  useEffect(() => {
    const fetchExpenseData = async () => {
      if (!currentCompanyId) return;

      try {
        setLoading(true);
        setError(null);

        // Get the date range based on the selected period
        const now = new Date();
        let startDate = new Date();

        if (period === 'monthly') {
          startDate.setMonth(now.getMonth() - 1);
        } else if (period === 'quarterly') {
          startDate.setMonth(now.getMonth() - 3);
        } else if (period === 'yearly') {
          startDate.setFullYear(now.getFullYear() - 1);
        }

        // If using fiscal year, adjust the date range
        if (useFiscalYear) {
          // Fetch company fiscal year settings
          const { data: company, error: companyError } = await supabase
            .from('companies')
            .select('fiscal_year_start')
            .eq('id', currentCompanyId)
            .single();

          if (companyError) {
            console.error('Error fetching fiscal year settings:', companyError);
          } else if (company?.fiscal_year_start) {
            const fiscalYearStart = new Date(company.fiscal_year_start);
            const currentYear = now.getFullYear();

            // Create a date for this year's fiscal start
            const thisYearFiscalStart = new Date(
              currentYear,
              fiscalYearStart.getMonth(),
              fiscalYearStart.getDate()
            );

            // If current date is before this year's fiscal start, use last year's fiscal start
            if (now < thisYearFiscalStart) {
              startDate = new Date(
                currentYear - 1,
                fiscalYearStart.getMonth(),
                fiscalYearStart.getDate()
              );
            } else {
              startDate = thisYearFiscalStart;
            }
          }
        }

        // Format dates for the query
        const startDateStr = startDate.toISOString().split('T')[0];
        const endDateStr = now.toISOString().split('T')[0];

        // First try to use the RPC if it exists
        try {
          const { data: expenseData, error: expenseError } = await supabase
            .rpc('get_expense_breakdown_by_category', {
              p_company_id: currentCompanyId,
              p_start_date: startDateStr,
              p_end_date: endDateStr,
              p_limit: categoryLimit
            });

          if (expenseError) {
            throw expenseError;
          }

          if (expenseData && expenseData.length > 0) {
            // Calculate total for percentages
            const total = expenseData.reduce((sum, item) => sum + item.amount, 0);

            // Format the data
            const formattedData = expenseData.map((item, index) => ({
              name: item.category,
              value: item.amount,
              percentage: Math.round((item.amount / total) * 100),
              color: COLORS[index % COLORS.length]
            }));

            setData(formattedData);
            return;
          }
        } catch (rpcError) {
          console.log('RPC not available, falling back to direct query');
        }

        // If RPC fails or returns no data, use direct query
        // Get expense transactions from the database
        const { data: transactions, error: transactionsError } = await supabase
          .from('transactions')
          .select(`
            id,
            transaction_date,
            transaction_items (
              id,
              debit,
              credit,
              account_id,
              accounts (
                id,
                name,
                account_type_id,
                account_types (
                  id,
                  name,
                  normal_balance
                )
              )
            )
          `)
          .eq('company_id', currentCompanyId)
          .gte('transaction_date', startDateStr)
          .lte('transaction_date', endDateStr);

        if (transactionsError) {
          throw transactionsError;
        }

        if (!transactions || transactions.length === 0) {
          setData([]);
          return;
        }

        // Group expenses by category
        const expensesByCategory = new Map<string, number>();

        // Process transactions to extract expense data
        transactions.forEach(transaction => {
          if (!transaction.transaction_items) return;

          transaction.transaction_items.forEach(item => {
            if (!item.accounts || !item.accounts.account_types) return;

            const accountType = item.accounts.account_types.name.toLowerCase();

            // Only process expense accounts
            if (accountType.includes('expense')) {
              const categoryName = item.accounts.name;
              const amount = item.debit || 0; // Expenses are typically debits

              // Add to the category total
              const currentTotal = expensesByCategory.get(categoryName) || 0;
              expensesByCategory.set(categoryName, currentTotal + amount);
            }
          });
        });

        // Convert to array and sort by amount (descending)
        const expensesArray = Array.from(expensesByCategory.entries())
          .map(([name, value]) => ({ name, value }))
          .sort((a, b) => b.value - a.value)
          .slice(0, categoryLimit);

        // Calculate total for percentages
        const total = expensesArray.reduce((sum, item) => sum + item.value, 0);

        // Format the data with percentages and colors
        const formattedData = expensesArray.map((item, index) => ({
          name: item.name,
          value: item.value,
          percentage: Math.round((item.value / total) * 100),
          color: COLORS[index % COLORS.length]
        }));

        setData(formattedData);
      } catch (err: any) {
        console.error('Error fetching expense data:', err);
        setError(err.message || 'Failed to load expense data');
      } finally {
        setLoading(false);
      }
    };

    fetchExpenseData();
  }, [currentCompanyId, period, categoryLimit, useFiscalYear]);

  // Custom tooltip for the charts
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const item = payload[0].payload;
      return (
        <div className="bg-background p-2 border rounded shadow-sm">
          <p className="font-medium">{item.name}</p>
          <p className="text-sm">
            {new Intl.NumberFormat('en-UG', {
              style: 'currency',
              currency: 'UGX',
              maximumFractionDigits: 0,
            }).format(item.value)}
            {showPercentages && ` (${item.percentage}%)`}
          </p>
        </div>
      );
    }
    return null;
  };

  // Format for the pie chart labels
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }: any) => {
    if (!showPercentages) return null;

    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="#fff"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize={12}
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Skeleton className="h-[80%] w-[80%] rounded-md" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center text-center p-4">
        <p className="text-sm text-muted-foreground">
          Error loading expense data. Please try again later.
        </p>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="h-full flex items-center justify-center text-center p-4">
        <p className="text-sm text-muted-foreground">
          No expense data available for the selected period.
        </p>
      </div>
    );
  }

  return (
    <div className="h-full">
      <ResponsiveContainer width="100%" height="100%">
        {chartType === 'pie' ? (
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={renderCustomizedLabel}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            {showLegend && (
              <Legend
                layout="vertical"
                verticalAlign="middle"
                align="right"
                formatter={(value, entry, index) => (
                  <span className="text-xs">
                    {value} {showPercentages && `(${data[index].percentage}%)`}
                  </span>
                )}
              />
            )}
          </PieChart>
        ) : (
          <BarChart
            data={data}
            layout="vertical"
            margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
            <XAxis type="number" />
            <YAxis
              type="category"
              dataKey="name"
              tick={{ fontSize: 12 }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar dataKey="value" fill="#8884d8">
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Bar>
          </BarChart>
        )}
      </ResponsiveContainer>
    </div>
  );
}
