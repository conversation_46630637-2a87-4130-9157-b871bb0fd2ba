-- Migration to enhance transaction approval workflow

-- Add status field to transactions table if it doesn't exist
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS status approval_status DEFAULT 'pending';

-- Add approval_level field to transactions table
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS approval_level INTEGER DEFAULT 0;

-- Add approval_threshold field to transactions table
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS approval_threshold INTEGER DEFAULT 0;

-- Create transaction approvals table
CREATE TABLE IF NOT EXISTS transaction_approvals (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  transaction_id UUID NOT NULL REFERENCES transactions(id) ON DELETE CASCADE,
  approved_by UUID NOT NULL REFERENCES auth.users(id),
  approval_level INTEGER NOT NULL DEFAULT 1,
  approved_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for efficient querying
CREATE INDEX idx_transaction_approvals_transaction ON transaction_approvals(transaction_id);

-- Create transaction approval thresholds table
CREATE TABLE IF NOT EXISTS transaction_approval_thresholds (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  min_amount DECIMAL(15, 2) NOT NULL,
  max_amount DECIMAL(15, 2),
  required_approvals INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT check_min_max CHECK (min_amount <= max_amount OR max_amount IS NULL)
);

-- Create index for efficient querying
CREATE INDEX idx_transaction_approval_thresholds_company ON transaction_approval_thresholds(company_id);

-- Create transaction approval notifications table
CREATE TABLE IF NOT EXISTS transaction_approval_notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  transaction_id UUID NOT NULL REFERENCES transactions(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  is_read BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for efficient querying
CREATE INDEX idx_transaction_approval_notifications_user ON transaction_approval_notifications(user_id, is_read);

-- Insert default approval thresholds for each company
INSERT INTO transaction_approval_thresholds (company_id, min_amount, max_amount, required_approvals)
SELECT 
  id as company_id, 
  0 as min_amount, 
  1000000 as max_amount, 
  1 as required_approvals
FROM companies
WHERE NOT EXISTS (
  SELECT 1 FROM transaction_approval_thresholds 
  WHERE company_id = companies.id AND min_amount = 0
);

INSERT INTO transaction_approval_thresholds (company_id, min_amount, max_amount, required_approvals)
SELECT 
  id as company_id, 
  1000000 as min_amount, 
  10000000 as max_amount, 
  2 as required_approvals
FROM companies
WHERE NOT EXISTS (
  SELECT 1 FROM transaction_approval_thresholds 
  WHERE company_id = companies.id AND min_amount = 1000000
);

INSERT INTO transaction_approval_thresholds (company_id, min_amount, max_amount, required_approvals)
SELECT 
  id as company_id, 
  10000000 as min_amount, 
  NULL as max_amount, 
  3 as required_approvals
FROM companies
WHERE NOT EXISTS (
  SELECT 1 FROM transaction_approval_thresholds 
  WHERE company_id = companies.id AND min_amount = 10000000
);

-- Function to determine required approvals based on transaction amount
CREATE OR REPLACE FUNCTION get_required_approvals(p_company_id UUID, p_amount DECIMAL)
RETURNS INTEGER AS $$
DECLARE
  v_required_approvals INTEGER;
BEGIN
  SELECT required_approvals INTO v_required_approvals
  FROM transaction_approval_thresholds
  WHERE company_id = p_company_id
    AND p_amount >= min_amount
    AND (max_amount IS NULL OR p_amount < max_amount)
  ORDER BY min_amount DESC
  LIMIT 1;
  
  RETURN COALESCE(v_required_approvals, 1);
END;
$$ LANGUAGE plpgsql;

-- Function to update transaction status based on approvals
CREATE OR REPLACE FUNCTION update_transaction_approval_status()
RETURNS TRIGGER AS $$
DECLARE
  v_transaction_record RECORD;
  v_approval_count INTEGER;
BEGIN
  -- Get the transaction record
  SELECT * INTO v_transaction_record 
  FROM transactions 
  WHERE id = NEW.transaction_id;
  
  -- Count the number of approvals
  SELECT COUNT(*) INTO v_approval_count
  FROM transaction_approvals
  WHERE transaction_id = NEW.transaction_id;
  
  -- Update the transaction approval level
  UPDATE transactions
  SET approval_level = v_approval_count
  WHERE id = NEW.transaction_id;
  
  -- If we have enough approvals, update the status to approved
  IF v_approval_count >= v_transaction_record.approval_threshold THEN
    UPDATE transactions
    SET status = 'approved'
    WHERE id = NEW.transaction_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update transaction status on new approval
CREATE TRIGGER update_transaction_status_on_approval
AFTER INSERT ON transaction_approvals
FOR EACH ROW
EXECUTE FUNCTION update_transaction_approval_status();

-- Function to set approval threshold on transaction creation
CREATE OR REPLACE FUNCTION set_transaction_approval_threshold()
RETURNS TRIGGER AS $$
DECLARE
  v_total_amount DECIMAL;
  v_required_approvals INTEGER;
BEGIN
  -- Calculate the total amount of the transaction
  SELECT COALESCE(SUM(debit), 0) INTO v_total_amount
  FROM transaction_items
  WHERE transaction_id = NEW.id;
  
  -- Get the required approvals based on amount
  v_required_approvals := get_required_approvals(NEW.company_id, v_total_amount);
  
  -- Update the transaction with the approval threshold
  UPDATE transactions
  SET approval_threshold = v_required_approvals
  WHERE id = NEW.id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to set approval threshold on transaction creation
CREATE TRIGGER set_transaction_approval_threshold_on_creation
AFTER INSERT ON transactions
FOR EACH ROW
EXECUTE FUNCTION set_transaction_approval_threshold();
