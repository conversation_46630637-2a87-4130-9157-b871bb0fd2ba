import { format } from "date-fns";
import { supabase } from "@/integrations/supabase/client";
import { IncomeExpenseSummary, ProfitAndLossData } from "./types";

/**
 * Fetch profit and loss data for a company within a date range
 *
 * @param companyId ID of the company
 * @param startDate Start date for the report
 * @param endDate End date for the report
 * @returns Profit and loss data or null if there was an error
 */
export const fetchProfitAndLoss = async (
  companyId: string,
  startDate: Date,
  endDate: Date
): Promise<ProfitAndLossData | null> => {
  try {
    // Format dates for query
    const formattedStartDate = format(startDate, 'yyyy-MM-dd');
    const formattedEndDate = format(endDate, 'yyyy-MM-dd');

    // Get revenue accounts
    const { data: revenueAccounts, error: revenueError } = await supabase
      .rpc('get_account_activity', {
        p_company_id: companyId,
        p_category: 'Revenue',
        p_start_date: formattedStartDate,
        p_end_date: formattedEndDate
      });

    if (revenueError) throw revenueError;

    // Get expense accounts
    const { data: expenseAccounts, error: expenseError } = await supabase
      .rpc('get_account_activity', {
        p_company_id: companyId,
        p_category: 'Expenses',
        p_start_date: formattedStartDate,
        p_end_date: formattedEndDate
      });

    if (expenseError) throw expenseError;

    // Process and return data
    const revenue: IncomeExpenseSummary = {
      total: revenueAccounts?.reduce((sum, account) => sum + account.total_amount, 0) || 0,
      items: revenueAccounts?.map(account => ({
        name: account.account_name,
        amount: account.total_amount,
        account_id: account.account_id
      })) || []
    };

    const expenses: IncomeExpenseSummary = {
      total: expenseAccounts?.reduce((sum, account) => sum + account.total_amount, 0) || 0,
      items: expenseAccounts?.map(account => ({
        name: account.account_name,
        amount: account.total_amount,
        account_id: account.account_id
      })) || []
    };

    const netIncome = revenue.total - expenses.total;

    return {
      revenue,
      expenses,
      netIncome,
      startDate: formattedStartDate,
      endDate: formattedEndDate
    };
  } catch (error) {
    console.error('Error fetching profit and loss data:', error);
    return null;
  }
};
