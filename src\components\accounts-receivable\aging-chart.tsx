/**
 * Aging Chart Component
 * 
 * This component provides visual representations of aging data using charts.
 */

import { useEffect, useRef } from 'react';
import { AgingReportSummary } from '@/services/aging-report-service';
import { Card, CardContent } from '@/components/ui/card';

// Import Chart.js dynamically to avoid SSR issues
let Chart: any;
if (typeof window !== 'undefined') {
  import('chart.js').then((module) => {
    const { Chart: ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement } = module;
    ChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement);
    Chart = ChartJS;
  });
}

interface AgingChartProps {
  agingData: AgingReportSummary;
  formatCurrency: (amount: number) => string;
}

export function AgingChart({ agingData, formatCurrency }: AgingChartProps): JSX.Element {
  const pieChartRef = useRef<HTMLCanvasElement>(null);
  const barChartRef = useRef<HTMLCanvasElement>(null);
  const pieChartInstance = useRef<any>(null);
  const barChartInstance = useRef<any>(null);

  useEffect(() => {
    if (!Chart || !pieChartRef.current || !barChartRef.current) return;

    // Destroy existing chart instances
    if (pieChartInstance.current) {
      pieChartInstance.current.destroy();
    }
    if (barChartInstance.current) {
      barChartInstance.current.destroy();
    }

    // Prepare data for pie chart
    const pieData = {
      labels: ['Current', '1-30 Days', '31-60 Days', '61-90 Days', 'Over 90 Days'],
      datasets: [
        {
          data: [
            agingData.current_amount,
            agingData.days_1_30_amount,
            agingData.days_31_60_amount,
            agingData.days_61_90_amount,
            agingData.over_90_days_amount
          ],
          backgroundColor: [
            'rgba(34, 197, 94, 0.7)',  // green
            'rgba(234, 179, 8, 0.7)',   // yellow
            'rgba(249, 115, 22, 0.7)',  // orange
            'rgba(239, 68, 68, 0.7)',   // red
            'rgba(185, 28, 28, 0.7)'    // dark red
          ],
          borderColor: [
            'rgba(34, 197, 94, 1)',
            'rgba(234, 179, 8, 1)',
            'rgba(249, 115, 22, 1)',
            'rgba(239, 68, 68, 1)',
            'rgba(185, 28, 28, 1)'
          ],
          borderWidth: 1
        }
      ]
    };

    // Create pie chart
    pieChartInstance.current = new Chart(pieChartRef.current, {
      type: 'pie',
      data: pieData,
      options: {
        responsive: true,
        plugins: {
          legend: {
            position: 'right',
          },
          tooltip: {
            callbacks: {
              label: function(context: any) {
                const label = context.label || '';
                const value = context.raw || 0;
                return `${label}: ${formatCurrency(value)}`;
              }
            }
          }
        }
      }
    });

    // Prepare data for bar chart
    const barData = {
      labels: ['Current', '1-30 Days', '31-60 Days', '61-90 Days', 'Over 90 Days'],
      datasets: [
        {
          label: 'Amount',
          data: [
            agingData.current_amount,
            agingData.days_1_30_amount,
            agingData.days_31_60_amount,
            agingData.days_61_90_amount,
            agingData.over_90_days_amount
          ],
          backgroundColor: [
            'rgba(34, 197, 94, 0.7)',
            'rgba(234, 179, 8, 0.7)',
            'rgba(249, 115, 22, 0.7)',
            'rgba(239, 68, 68, 0.7)',
            'rgba(185, 28, 28, 0.7)'
          ],
          borderColor: [
            'rgba(34, 197, 94, 1)',
            'rgba(234, 179, 8, 1)',
            'rgba(249, 115, 22, 1)',
            'rgba(239, 68, 68, 1)',
            'rgba(185, 28, 28, 1)'
          ],
          borderWidth: 1
        }
      ]
    };

    // Create bar chart
    barChartInstance.current = new Chart(barChartRef.current, {
      type: 'bar',
      data: barData,
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return formatCurrency(Number(value));
              }
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: function(context: any) {
                const label = context.dataset.label || '';
                const value = context.raw || 0;
                return `${label}: ${formatCurrency(value)}`;
              }
            }
          }
        }
      }
    });

    // Cleanup on unmount
    return () => {
      if (pieChartInstance.current) {
        pieChartInstance.current.destroy();
      }
      if (barChartInstance.current) {
        barChartInstance.current.destroy();
      }
    };
  }, [agingData, formatCurrency]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardContent className="pt-6">
          <h3 className="text-lg font-medium mb-4">Aging Distribution</h3>
          <div className="h-64">
            <canvas ref={pieChartRef} />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="pt-6">
          <h3 className="text-lg font-medium mb-4">Aging by Period</h3>
          <div className="h-64">
            <canvas ref={barChartRef} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
