
import { User } from '@supabase/supabase-js';
import {
  Profile,
  UserRole,
  Company,
  Account,
  Transaction,
  Vendor,
  Customer,
  Invoice,
  BillStatus,
  InvoiceStatus,
  BankTransactionType,
  AccountCategory,
  AuthState
} from './index';

// Re-export types from the central type definitions
export type {
  Profile,
  UserRole,
  Company,
  Account,
  Transaction,
  Vendor,
  Customer,
  Invoice,
  BillStatus,
  InvoiceStatus,
  BankTransactionType,
  AccountCategory,
  AuthState
};

// Legacy types that might still be in use but should be migrated
export type UserRoleRecord = Record<string, UserRole>;
export type JournalEntry = Record<string, unknown>;
export type TransactionStatus = "draft" | "pending" | "approved" | "rejected" | "posted";
export type InvoiceItem = Record<string, unknown>;
export type Bill = Record<string, unknown>;
export type BillItem = Record<string, unknown>;
export type TaxRate = Record<string, unknown>;
export type BankAccount = Record<string, unknown>;
export type BankTransaction = Record<string, unknown>;
export type AccountBalance = Record<string, unknown>;
export type AccountType = "debit" | "credit";
