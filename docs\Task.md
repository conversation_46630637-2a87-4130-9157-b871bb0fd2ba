# MVP Enhancement Tasks

# Guidelines

Frontend (/src):
- Components: <PERSON><PERSON>ase (InvoiceForm.tsx)
- Hooks: useCamelCase (useAuthVerify.ts)
- Utils: kebab-case (date-utils.ts)
- Pages: PascalCase (/SettingsPage.tsx)


### Phase 1: Core Type Definitions
- [✅] **Create** `src/types/index.ts` with standardized type exports
- [✅] **Update** `src/types/auth.ts` to use Supabase-generated types
- [✅] **Refactor** `src/models/accountTypes.ts` to import from the new central types
- [✅] **Create** utility types (e.g. `Id<T>`, `PartialRecord<K, T>`) for common operations

### Phase 2: Validation Schemas
- [✅] **Audit** all existing Zod schemas and list the ones to update
- [✅] **Align** each schema’s types & constraints to the database definitions
- [✅] **Enforce** default values and `enum` constraints in the Zod definitions
- [✅] **Write** form-validation tests to verify the updated schemas

### Phase 3: Services and API Calls
- [✅] **Update** invoice service functions to accept/return the new standardized types
- [✅] **Update** report service functions to use standardized types
- [✅] **Refactor** any remaining service modules (e.g. customers, bills, budgets)
- [✅] **Create** or update tests for each API call, asserting correct type checks

### Phase 4: Hooks and Components
- [✅] **Refactor** customer-related hooks & components to use new types
- [✅] **Refactor** invoice-related hooks & components to use new types
- [✅] **Refactor** account-related hooks & components to use new types
- [✅] **Refactor** transaction-related hooks & components to use new types

### Phase 5: Context and State Management
- [✅] **Update** `AuthContext` (and related hooks) to consume standardized types
- [✅] **Update** other context providers (CompanyContext, ThemeContext, etc.)
- [✅] **Perform** full smoke test of the app to ensure context/state flows remain intact

---

## Potential Challenges & Mitigations

- **Breaking Changes**
  - *Challenge:* Refactors may break existing functionality
  - *Mitigation:* Work incrementally, validate each PR with full test suite

- **Type Complexity**
  - *Challenge:* Supabase types with nested relations can be verbose
  - *Mitigation:* Introduce concise type aliases and helper generics

- **Runtime Type Checking**
  - *Challenge:* TypeScript types vanish at runtime
  - *Mitigation:* Add runtime guards (e.g. `assertIs<T>()`) for critical data paths
