
import { supabase } from '@/integrations/supabase/client';
import { Company } from '@/types/index';
import { useToast } from './use-toast';
import { CompanyFormValues } from '@/validations/company-schema';

export function useCompanyManagement() {
  const { toast } = useToast();

  // Fetch the company
  const fetchCompany = async (): Promise<Company | null> => {
    try {
      const { data, error } = await supabase
        .from('companies')
        .select('*')
        .limit(1)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned - no company exists yet
          return null;
        }

        console.error('Error fetching company:', error);
        return null;
      }

      return data as Company;
    } catch (error) {
      console.error('Unexpected error fetching company:', error);
      return null;
    }
  };

  // Fetch companies associated with a user
  const fetchUserCompanies = async (userId: string): Promise<Company[]> => {
    try {
      console.log("Fetching companies for user:", userId);

      // Add retry logic for network issues
      const fetchWithRetry = async (retries = 3, delay = 1000) => {
        for (let attempt = 1; attempt <= retries; attempt++) {
          try {
            // Get companies the user has access to via user_companies table
            const { data: userCompanies, error: userCompaniesError } = await supabase
              .from('user_companies')
              .select('company_id')
              .eq('user_id', userId);

            if (userCompaniesError) {
              if (userCompaniesError.message?.includes('Failed to fetch') && attempt < retries) {
                console.warn(`Network error on attempt ${attempt}/${retries}, retrying in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
                continue;
              }
              console.error('Error fetching user companies:', userCompaniesError);
              return { data: [], error: userCompaniesError };
            }

            return { data: userCompanies, error: null };
          } catch (err) {
            if (attempt < retries) {
              console.warn(`Unexpected error on attempt ${attempt}/${retries}, retrying in ${delay}ms...`, err);
              await new Promise(resolve => setTimeout(resolve, delay));
            } else {
              throw err;
            }
          }
        }
        return { data: [], error: new Error('Max retries reached') };
      };

      const { data: userCompanies, error: userCompaniesError } = await fetchWithRetry();

      if (userCompaniesError) {
        console.error('Error fetching user companies after retries:', userCompaniesError);
        return [];
      }

      if (userCompanies.length === 0) {
        console.log("No company associations found for user");

        // Try to fetch any company in the system
        try {
          console.log("Checking if any companies exist in the system");
          const { data: anyCompany, error: companyError } = await supabase
            .from('companies')
            .select('*')
            .limit(1);

          if (companyError) {
            console.error("Error checking for companies:", companyError);
          } else if (anyCompany && anyCompany.length > 0) {
            console.log("Companies exist but user is not associated with any");

            // Automatically associate user with the first company as a viewer
            try {
              console.log("Attempting to associate user with first company");
              const { error: associationError } = await supabase
                .from('user_companies')
                .insert({
                  user_id: userId,
                  company_id: anyCompany[0].id,
                  role: 'viewer',
                  is_default: true,
                  invited_at: new Date().toISOString(),
                  accepted_at: new Date().toISOString()
                });

              if (associationError) {
                console.error("Error associating user with company:", associationError);
              } else {
                console.log("User associated with company, returning company");
                return anyCompany as Company[];
              }
            } catch (assocError) {
              console.error("Error in company association:", assocError);
            }
          } else {
            console.log("No companies exist in the system");
          }
        } catch (checkError) {
          console.error("Error checking companies:", checkError);
        }

        return [];
      }

      console.log(`Found ${userCompanies.length} company associations for user`);

      // Get the company details with retry logic
      const companyIds = userCompanies.map(uc => uc.company_id);

      const fetchCompaniesWithRetry = async (retries = 3, delay = 1000) => {
        for (let attempt = 1; attempt <= retries; attempt++) {
          try {
            const { data, error } = await supabase
              .from('companies')
              .select('*')
              .in('id', companyIds);

            if (error) {
              if (error.message?.includes('Failed to fetch') && attempt < retries) {
                console.warn(`Network error fetching companies on attempt ${attempt}/${retries}, retrying in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
                continue;
              }
              console.error('Error fetching companies:', error);
              return { data: [], error };
            }

            return { data, error: null };
          } catch (err) {
            if (attempt < retries) {
              console.warn(`Unexpected error fetching companies on attempt ${attempt}/${retries}, retrying in ${delay}ms...`, err);
              await new Promise(resolve => setTimeout(resolve, delay));
            } else {
              throw err;
            }
          }
        }
        return { data: [], error: new Error('Max retries reached') };
      };

      const { data, error } = await fetchCompaniesWithRetry();

      if (error) {
        console.error('Error fetching companies after retries:', error);
        return [];
      }

      console.log(`Retrieved ${data?.length} companies`);
      return data as Company[];
    } catch (error) {
      console.error('Unexpected error fetching companies:', error);
      return [];
    }
  };

  // Create a new company
  const createCompany = async (companyData: CompanyFormValues, userId: string): Promise<Company | null> => {
    try {
      const { data, error } = await supabase
        .from('companies')
        .insert({
          ...companyData,
          owner_id: userId,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Assign owner as admin
      const { error: roleError } = await supabase
        .from('user_roles')
        .insert({
          user_id: userId,
          company_id: data.id,
          role: 'admin'
        });

      if (roleError) {
        console.error('Error assigning admin role:', roleError);
        toast({
          title: "Warning",
          description: "Company created but error assigning admin role"
        });
      }

      toast({
        title: "Success",
        description: "Company created successfully"
      });

      return data as Company;
    } catch (error) {
      console.error('Error creating company:', error);
      const errorMessage = error instanceof Error
        ? error.message
        : "Failed to create company";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return null;
    }
  };

  // Update company details
  const updateCompany = async (companyId: string, companyData: CompanyFormValues): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('companies')
        .update(companyData)
        .eq('id', companyId);

      if (error) {
        throw error;
      }

      toast({
        title: "Success",
        description: "Company updated successfully"
      });

      return true;
    } catch (error) {
      console.error('Error updating company:', error);
      const errorMessage = error instanceof Error
        ? error.message
        : "Failed to update company";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  };

  return {
    fetchCompany,
    fetchUserCompanies,
    createCompany,
    updateCompany
  };
}
