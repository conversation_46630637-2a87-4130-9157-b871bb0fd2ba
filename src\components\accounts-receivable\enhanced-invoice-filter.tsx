import { useState } from 'react';
import { format } from 'date-fns';
import { CalendarIcon, Search, Filter, X } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetFooter,
  SheetClose,
} from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

import { useEnhancedInvoices, InvoiceFilterOptions } from '@/hooks/use-enhanced-invoices';
import { useCustomers } from '@/hooks/use-customers';

/**
 * Enhanced invoice filter component
 */
export function EnhancedInvoiceFilter(): JSX.Element {
  const { filterOptions, setFilterOptions } = useEnhancedInvoices();
  const { customers } = useCustomers();

  // Local state for filter form
  const [localFilters, setLocalFilters] = useState<InvoiceFilterOptions>(filterOptions);

  // Handle search input
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const searchTerm = e.target.value;
    setFilterOptions(prev => ({ ...prev, searchTerm }));
  };

  // Handle status filter change
  const handleStatusChange = (status: string): void => {
    setFilterOptions(prev => ({
      ...prev,
      status: status as InvoiceFilterOptions['status']
    }));
  };

  // Handle date range change
  const handleDateRangeChange = (dateRange: string): void => {
    setLocalFilters(prev => ({
      ...prev,
      dateRange: dateRange as InvoiceFilterOptions['dateRange'],
      // Reset custom dates if not using custom range
      ...(dateRange !== 'custom' && { startDate: undefined, endDate: undefined })
    }));
  };

  // Apply filters
  const applyFilters = (): void => {
    setFilterOptions(localFilters);
  };

  // Reset filters
  const resetFilters = (): void => {
    const defaultFilters: InvoiceFilterOptions = {
      status: 'all',
      dateRange: 'all',
    };
    setLocalFilters(defaultFilters);
    setFilterOptions(defaultFilters);
  };

  // Count active filters
  const countActiveFilters = (): number => {
    let count = 0;
    if (filterOptions.status && filterOptions.status !== 'all') count++;
    if (filterOptions.dateRange && filterOptions.dateRange !== 'all') count++;
    if (filterOptions.customerId) count++;
    if (filterOptions.minAmount !== undefined || filterOptions.maxAmount !== undefined) count++;
    return count;
  };

  // Get active filter badges
  const getActiveFilterBadges = (): JSX.Element[] => {
    const badges = [];

    if (filterOptions.status && filterOptions.status !== 'all') {
      badges.push(
        <Badge key="status" variant="secondary" className="gap-1">
          Status: {filterOptions.status.charAt(0).toUpperCase() + filterOptions.status.slice(1)}
          <X
            className="h-3 w-3 cursor-pointer"
            onClick={() => setFilterOptions(prev => ({ ...prev, status: 'all' }))}
          />
        </Badge>
      );
    }

    if (filterOptions.dateRange && filterOptions.dateRange !== 'all') {
      let dateLabel = '';
      switch (filterOptions.dateRange) {
        case '30days':
          dateLabel = 'Last 30 days';
          break;
        case '90days':
          dateLabel = 'Last 90 days';
          break;
        case 'thisYear':
          dateLabel = 'This year';
          break;
        case 'custom':
          if (filterOptions.startDate && filterOptions.endDate) {
            dateLabel = `${format(filterOptions.startDate, 'MMM d, yyyy')} - ${format(filterOptions.endDate, 'MMM d, yyyy')}`;
          } else {
            dateLabel = 'Custom range';
          }
          break;
      }

      badges.push(
        <Badge key="date" variant="secondary" className="gap-1">
          Date: {dateLabel}
          <X
            className="h-3 w-3 cursor-pointer"
            onClick={() => setFilterOptions(prev => ({ ...prev, dateRange: 'all' }))}
          />
        </Badge>
      );
    }

    if (filterOptions.customerId) {
      const customerName = customers?.find(c => c.id === filterOptions.customerId)?.name || 'Unknown';
      badges.push(
        <Badge key="customer" variant="secondary" className="gap-1">
          Customer: {customerName}
          <X
            className="h-3 w-3 cursor-pointer"
            onClick={() => setFilterOptions(prev => ({ ...prev, customerId: undefined }))}
          />
        </Badge>
      );
    }

    if (filterOptions.minAmount !== undefined || filterOptions.maxAmount !== undefined) {
      let amountLabel = '';
      if (filterOptions.minAmount !== undefined && filterOptions.maxAmount !== undefined) {
        amountLabel = `${filterOptions.minAmount} - ${filterOptions.maxAmount}`;
      } else if (filterOptions.minAmount !== undefined) {
        amountLabel = `Min: ${filterOptions.minAmount}`;
      } else if (filterOptions.maxAmount !== undefined) {
        amountLabel = `Max: ${filterOptions.maxAmount}`;
      }

      badges.push(
        <Badge key="amount" variant="secondary" className="gap-1">
          Amount: {amountLabel}
          <X
            className="h-3 w-3 cursor-pointer"
            onClick={() => setFilterOptions(prev => ({
              ...prev,
              minAmount: undefined,
              maxAmount: undefined
            }))}
          />
        </Badge>
      );
    }

    return badges;
  };

  return (
    <div className="flex flex-col space-y-4">
      <div className="flex flex-col sm:flex-row gap-2 sm:items-center sm:justify-between">
        <div className="relative w-full sm:w-80">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search invoices..."
            className="pl-8"
            value={filterOptions.searchTerm || ''}
            onChange={handleSearch}
          />
        </div>

        <div className="flex flex-wrap gap-2">
          <Select
            value={filterOptions.status || 'all'}
            onValueChange={handleStatusChange}
          >
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="sent">Sent</SelectItem>
              <SelectItem value="paid">Paid</SelectItem>
              <SelectItem value="overdue">Overdue</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>

          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="gap-2">
                <Filter className="h-4 w-4" />
                Filters
                {countActiveFilters() > 0 && (
                  <Badge variant="secondary" className="ml-1 h-5 w-5 rounded-full p-0 text-xs">
                    {countActiveFilters()}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>Filter Invoices</SheetTitle>
                <SheetDescription>
                  Apply filters to narrow down your invoice list
                </SheetDescription>
              </SheetHeader>

              <div className="py-6 space-y-6">
                <div className="space-y-2">
                  <Label>Status</Label>
                  <Select
                    value={localFilters.status || 'all'}
                    onValueChange={(value) => setLocalFilters(prev => ({
                      ...prev,
                      status: value as InvoiceFilterOptions['status']
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="sent">Sent</SelectItem>
                      <SelectItem value="paid">Paid</SelectItem>
                      <SelectItem value="overdue">Overdue</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Date Range</Label>
                  <Select
                    value={localFilters.dateRange || 'all'}
                    onValueChange={handleDateRangeChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select date range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="30days">Last 30 Days</SelectItem>
                      <SelectItem value="90days">Last 90 Days</SelectItem>
                      <SelectItem value="thisYear">This Year</SelectItem>
                      <SelectItem value="custom">Custom Range</SelectItem>
                    </SelectContent>
                  </Select>

                  {localFilters.dateRange === 'custom' && (
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      <div className="space-y-1">
                        <Label className="text-xs">Start Date</Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full justify-start text-left font-normal",
                                !localFilters.startDate && "text-muted-foreground"
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {localFilters.startDate ? (
                                format(localFilters.startDate, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar
                              mode="single"
                              selected={localFilters.startDate}
                              onSelect={(date) => setLocalFilters(prev => ({
                                ...prev,
                                startDate: date || undefined
                              }))}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs">End Date</Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full justify-start text-left font-normal",
                                !localFilters.endDate && "text-muted-foreground"
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {localFilters.endDate ? (
                                format(localFilters.endDate, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar
                              mode="single"
                              selected={localFilters.endDate}
                              onSelect={(date) => setLocalFilters(prev => ({
                                ...prev,
                                endDate: date || undefined
                              }))}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Customer</Label>
                  <Select
                    value={localFilters.customerId || ''}
                    onValueChange={(value) => setLocalFilters(prev => ({
                      ...prev,
                      customerId: value || undefined
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Customers" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Customers</SelectItem>
                      {customers?.map((customer) => (
                        <SelectItem key={customer.id} value={customer.id}>
                          {customer.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Amount Range</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <Label className="text-xs">Min Amount</Label>
                      <Input
                        type="number"
                        placeholder="0"
                        value={localFilters.minAmount || ''}
                        onChange={(e) => setLocalFilters(prev => ({
                          ...prev,
                          minAmount: e.target.value ? Number(e.target.value) : undefined
                        }))}
                      />
                    </div>
                    <div className="space-y-1">
                      <Label className="text-xs">Max Amount</Label>
                      <Input
                        type="number"
                        placeholder="Any"
                        value={localFilters.maxAmount || ''}
                        onChange={(e) => setLocalFilters(prev => ({
                          ...prev,
                          maxAmount: e.target.value ? Number(e.target.value) : undefined
                        }))}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <SheetFooter>
                <Button variant="outline" onClick={resetFilters}>
                  Reset Filters
                </Button>
                <SheetClose asChild>
                  <Button onClick={applyFilters}>Apply Filters</Button>
                </SheetClose>
              </SheetFooter>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* Active filters */}
      {countActiveFilters() > 0 && (
        <div className="flex flex-wrap gap-2 items-center">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          {getActiveFilterBadges()}
          <Button
            variant="ghost"
            size="sm"
            className="h-7 px-2 text-xs"
            onClick={resetFilters}
          >
            Clear all
          </Button>
        </div>
      )}
    </div>
  );
}
