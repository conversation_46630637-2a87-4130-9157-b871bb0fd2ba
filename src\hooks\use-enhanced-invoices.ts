/**
 * Enhanced Invoice Hook
 *
 * This hook provides comprehensive invoice management functionality
 * with React Query for data fetching, caching, and optimistic updates.
 */

import { useState, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { InvoiceFormValues } from '@/validations/invoice-schema';
import { createInvoice } from '@/services/invoice-service';
import {
  updateInvoiceStatus,
  sendInvoiceEmail,
  recordInvoicePayment,
  checkOverdueInvoices
} from '@/services/invoice-workflow-service';
import { showSuccessToast, showErrorToast } from '@/utils/toast-utils';
import { useRetryMutation } from './use-retry-mutation';
import { Tables, Enums } from '@/types';

// Type for invoice with related data
export interface InvoiceWithRelations extends Tables<'invoices'> {
  customers: Pick<Tables<'customers'>, 'id' | 'name' | 'email' | 'phone' | 'address' | 'city' | 'country'>;
  invoice_items: Array<Tables<'invoice_items'>>;
  invoice_payments?: Array<Tables<'invoice_payments'>>;
  invoice_status_history?: Array<{
    id: string;
    invoice_id: string;
    from_status: string;
    to_status: string;
    changed_by: string;
    changed_at: string;
    notes: string | null;
  }>;
  _isOptimistic?: boolean;
};

// Type for invoice filter options
export interface InvoiceFilterOptions {
  status?: "draft" | "sent" | "paid" | "overdue" | "cancelled" | 'all';
  dateRange?: 'all' | '30days' | '90days' | 'thisYear' | 'custom';
  startDate?: Date;
  endDate?: Date;
  customerId?: string;
  minAmount?: number;
  maxAmount?: number;
  searchTerm?: string;
}

// Type for invoice sort options
export interface InvoiceSortOptions {
  field: 'issue_date' | 'due_date' | 'amount' | 'status' | 'invoice_number';
  direction: 'asc' | 'desc';
}

/**
 * Enhanced invoice management hook with React Query
 * @returns Object with invoice data and management functions
 */
export const useEnhancedInvoices = (): {
  invoices: InvoiceWithRelations[] | undefined;
  selectedInvoice: InvoiceWithRelations | null | undefined;
  filterOptions: InvoiceFilterOptions;
  sortOptions: InvoiceSortOptions;
  isLoadingInvoices: boolean;
  isLoadingInvoice: boolean;
  isCreatingInvoice: boolean;
  isUpdatingStatus: boolean;
  isSendingEmail: boolean;
  isRecordingPayment: boolean;
  isDeletingInvoice: boolean;
  invoicesError: Error | null;
  selectedInvoiceError: Error | null;
  setSelectedInvoiceId: (id: string | null) => void;
  setFilterOptions: React.Dispatch<React.SetStateAction<InvoiceFilterOptions>>;
  setSortOptions: React.Dispatch<React.SetStateAction<InvoiceSortOptions>>;
  createInvoice: (data: { formData: InvoiceFormValues; customerId: string }) => void;
  updateStatus: (data: { invoiceId: string; status: Enums['invoice_status'] }) => void;
  sendEmail: (invoiceId: string) => void;
  recordPayment: (data: {
    invoiceId: string;
    paymentData: {
      amount: number;
      paymentDate: Date;
      paymentMethod: string;
      reference?: string;
      notes?: string;
    };
  }) => void;
  deleteInvoice: (invoiceId: string) => void;
  checkForOverdueInvoices: () => Promise<void>;
  refetchInvoices: () => Promise<any>;
  refetchSelectedInvoice: () => Promise<any>;
} => {
  const { currentCompanyId, user, roles } = useAuth();
  const queryClient = useQueryClient();
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);
  const [filterOptions, setFilterOptions] = useState<InvoiceFilterOptions>({
    status: 'all',
    dateRange: 'all',
  });
  const [sortOptions, setSortOptions] = useState<InvoiceSortOptions>({
    field: 'issue_date',
    direction: 'desc',
  });

  // Get user role for permission checks
  const userRole = roles?.[0] || 'viewer';

  /**
   * Build query for invoices based on filter options
   */
  const buildInvoiceQuery = useCallback((query: ReturnType<typeof supabase.from>) => {
    if (!currentCompanyId) return query;

    // Apply company filter
    query = query.eq('company_id', currentCompanyId);

    // Apply status filter
    if (filterOptions.status && filterOptions.status !== 'all') {
      query = query.eq('status', filterOptions.status);
    }

    // Apply date range filter
    if (filterOptions.dateRange && filterOptions.dateRange !== 'all') {
      if (filterOptions.dateRange === 'custom' && filterOptions.startDate && filterOptions.endDate) {
        query = query
          .gte('issue_date', filterOptions.startDate.toISOString().split('T')[0])
          .lte('issue_date', filterOptions.endDate.toISOString().split('T')[0]);
      } else {
        const today = new Date();
        let startDate: Date;

        switch (filterOptions.dateRange) {
          case '30days':
            startDate = new Date(today);
            startDate.setDate(today.getDate() - 30);
            query = query.gte('issue_date', startDate.toISOString().split('T')[0]);
            break;
          case '90days':
            startDate = new Date(today);
            startDate.setDate(today.getDate() - 90);
            query = query.gte('issue_date', startDate.toISOString().split('T')[0]);
            break;
          case 'thisYear':
            startDate = new Date(today.getFullYear(), 0, 1);
            query = query.gte('issue_date', startDate.toISOString().split('T')[0]);
            break;
        }
      }
    }

    // Apply customer filter
    if (filterOptions.customerId) {
      query = query.eq('customer_id', filterOptions.customerId);
    }

    // Apply amount range filter
    if (filterOptions.minAmount !== undefined) {
      query = query.gte('total_amount', filterOptions.minAmount);
    }
    if (filterOptions.maxAmount !== undefined) {
      query = query.lte('total_amount', filterOptions.maxAmount);
    }

    // Apply search term filter (on invoice number)
    if (filterOptions.searchTerm) {
      query = query.ilike('invoice_number', `%${filterOptions.searchTerm}%`);
    }

    // Apply sorting
    query = query.order(sortOptions.field, { ascending: sortOptions.direction === 'asc' });

    return query;
  }, [currentCompanyId, filterOptions, sortOptions]);

  /**
   * Query for fetching all invoices with filtering and sorting
   */
  const {
    data: invoices,
    isLoading: isLoadingInvoices,
    error: invoicesError,
    refetch: refetchInvoices,
  } = useQuery({
    queryKey: ['invoices', currentCompanyId, filterOptions, sortOptions],
    queryFn: async () => {
      if (!currentCompanyId) return [];

      let query = supabase
        .from('invoices')
        .select(`
          *,
          customers (
            id, name, email
          )
        `);

      query = buildInvoiceQuery(query);

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    },
    enabled: !!currentCompanyId,
  });

  /**
   * Query for fetching a single invoice with all related data
   */
  const {
    data: selectedInvoice,
    isLoading: isLoadingInvoice,
    error: selectedInvoiceError,
    refetch: refetchSelectedInvoice,
  } = useQuery({
    queryKey: ['invoice', selectedInvoiceId],
    queryFn: async () => {
      if (!selectedInvoiceId) return null;

      const { data, error } = await supabase
        .from('invoices')
        .select(`
          *,
          customers (
            id, name, email, phone, address, city, country
          ),
          invoice_items (
            id, description, quantity, unit_price, tax_rate, amount
          ),
          invoice_payments (
            id, amount, payment_date, payment_method, reference, notes
          ),
          invoice_status_history (
            id, from_status, to_status, changed_by, changed_at, notes
          )
        `)
        .eq('id', selectedInvoiceId)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!selectedInvoiceId,
  });

  /**
   * Mutation for creating a new invoice with optimistic updates
   */
  const createInvoiceMutation = useRetryMutation(
    async (invoiceData: {
      formData: InvoiceFormValues;
      customerId: string;
    }) => {
      if (!currentCompanyId || !user) throw new Error("Authentication required");

      return await createInvoice(
        invoiceData.formData,
        currentCompanyId,
        invoiceData.customerId,
        user.id
      );
    },
    {
      onMutate: async (newInvoiceData) => {
        // Cancel any outgoing refetches
        await queryClient.cancelQueries({ queryKey: ['invoices'] });

        // Snapshot the previous value
        const previousInvoices = queryClient.getQueryData(['invoices', currentCompanyId, filterOptions, sortOptions]);

        // Optimistically update to the new value
        if (previousInvoices) {
          queryClient.setQueryData(
            ['invoices', currentCompanyId, filterOptions, sortOptions],
            (old: InvoiceWithRelations[] | undefined) => {
              const optimisticInvoice: InvoiceWithRelations = {
                id: 'temp-id-' + Date.now(),
                invoice_number: 'DRAFT-' + Date.now(),
                company_id: currentCompanyId || '',
                customer_id: newInvoiceData.customerId,
                status: 'draft',
                issue_date: new Date().toISOString(),
                due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                amount: 0,
                tax_amount: 0,
                total_amount: 0,
                created_by: user?.id || '',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                currency: 'UGX',
                exchange_rate: 1,
                customers: {
                  id: newInvoiceData.customerId,
                  name: newInvoiceData.formData.customer,
                  email: newInvoiceData.formData.email || null,
                  phone: null,
                  address: null,
                  city: null,
                  country: null
                },
                invoice_items: [],
                // Add optimistic flag for UI handling
                _isOptimistic: true,
              };

              return old ? [optimisticInvoice, ...old] : [optimisticInvoice];
            }
          );
        }

        return { previousInvoices };
      },
      onSuccess: (newInvoice) => {
        // Invalidate and refetch
        queryClient.invalidateQueries({ queryKey: ['invoices'] });

        showSuccessToast(
          'Invoice Created',
          `Invoice #${newInvoice?.invoice_number} has been created successfully.`
        );
      },
      onError: (error, _, context) => {
        // Rollback to the previous value
        if (context?.previousInvoices) {
          queryClient.setQueryData(
            ['invoices', currentCompanyId, filterOptions, sortOptions],
            context.previousInvoices
          );
        }

        showErrorToast(
          'Failed to Create Invoice',
          error instanceof Error ? error.message : 'An unexpected error occurred'
        );
      },
    }
  );

  /**
   * Mutation for updating invoice status with optimistic updates
   */
  const updateStatusMutation = useRetryMutation(
    async ({
      invoiceId,
      status
    }: {
      invoiceId: string;
      status: "draft" | "sent" | "paid" | "overdue" | "cancelled"
    }) => {
      if (!user) throw new Error("Authentication required");

      const result = await updateInvoiceStatus(invoiceId, status, user.id, userRole);
      if (!result.success) throw new Error("Failed to update status");
      return result.invoice;
    },
    {
      onMutate: async ({ invoiceId, status }) => {
        // Cancel any outgoing refetches
        await queryClient.cancelQueries({ queryKey: ['invoices'] });
        if (selectedInvoiceId === invoiceId) {
          await queryClient.cancelQueries({ queryKey: ['invoice', invoiceId] });
        }

        // Snapshot the previous values
        const previousInvoices = queryClient.getQueryData(['invoices', currentCompanyId, filterOptions, sortOptions]);
        const previousInvoice = queryClient.getQueryData(['invoice', invoiceId]);

        // Optimistically update invoices list
        if (previousInvoices) {
          queryClient.setQueryData(
            ['invoices', currentCompanyId, filterOptions, sortOptions],
            (old: InvoiceWithRelations[] | undefined) => {
              if (!old) return old;
              return old.map((invoice) => {
                if (invoice.id === invoiceId) {
                  return {
                    ...invoice,
                    status,
                    updated_at: new Date().toISOString(),
                  };
                }
                return invoice;
              });
            }
          );
        }

        // Optimistically update selected invoice
        if (previousInvoice && selectedInvoiceId === invoiceId) {
          queryClient.setQueryData(
            ['invoice', invoiceId],
            (old: InvoiceWithRelations | null | undefined) => {
              if (!old) return old;
              return {
                ...old,
                status,
                updated_at: new Date().toISOString(),
              };
            }
          );
        }

        return { previousInvoices, previousInvoice };
      },
      onSuccess: () => {
        // Invalidate and refetch
        queryClient.invalidateQueries({ queryKey: ['invoices'] });
        if (selectedInvoiceId) {
          queryClient.invalidateQueries({ queryKey: ['invoice', selectedInvoiceId] });
        }
      },
      onError: (error, variables, context) => {
        // Rollback to the previous values
        if (context?.previousInvoices) {
          queryClient.setQueryData(
            ['invoices', currentCompanyId, filterOptions, sortOptions],
            context.previousInvoices
          );
        }

        if (context?.previousInvoice && selectedInvoiceId === variables.invoiceId) {
          queryClient.setQueryData(
            ['invoice', variables.invoiceId],
            context.previousInvoice
          );
        }

        showErrorToast(
          'Failed to Update Status',
          error instanceof Error ? error.message : 'An unexpected error occurred'
        );
      },
    }
  );

  /**
   * Mutation for sending invoice email
   */
  const sendInvoiceEmailMutation = useRetryMutation(
    async (invoiceId: string) => {
      if (!user) throw new Error("Authentication required");

      const success = await sendInvoiceEmail(invoiceId, user.id);
      if (!success) throw new Error("Failed to send invoice");
      return success;
    },
    {
      onSuccess: (_, invoiceId) => {
        queryClient.invalidateQueries({ queryKey: ['invoices'] });
        if (selectedInvoiceId === invoiceId) {
          queryClient.invalidateQueries({ queryKey: ['invoice', invoiceId] });
        }
      },
    }
  );

  /**
   * Mutation for recording invoice payment
   */
  const recordPaymentMutation = useRetryMutation(
    async ({
      invoiceId,
      paymentData
    }: {
      invoiceId: string;
      paymentData: {
        amount: number;
        paymentDate: Date;
        paymentMethod: string;
        reference?: string;
        notes?: string;
      };
    }) => {
      if (!user) throw new Error("Authentication required");

      const success = await recordInvoicePayment(invoiceId, paymentData, user.id);
      if (!success) throw new Error("Failed to record payment");
      return success;
    },
    {
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['invoices'] });
        if (selectedInvoiceId === variables.invoiceId) {
          queryClient.invalidateQueries({ queryKey: ['invoice', variables.invoiceId] });
        }
      },
    }
  );

  /**
   * Mutation for deleting an invoice
   */
  const deleteInvoiceMutation = useRetryMutation(
    async (invoiceId: string) => {
      // First delete invoice items
      const { error: itemsError } = await supabase
        .from('invoice_items')
        .delete()
        .eq('invoice_id', invoiceId);

      if (itemsError) throw itemsError;

      // Then delete invoice payments
      const { error: paymentsError } = await supabase
        .from('invoice_payments')
        .delete()
        .eq('invoice_id', invoiceId);

      if (paymentsError) throw paymentsError;

      // Then delete invoice status history
      const { error: historyError } = await supabase
        .from('invoice_status_history')
        .delete()
        .eq('invoice_id', invoiceId);

      if (historyError) throw historyError;

      // Finally delete the invoice
      const { error } = await supabase
        .from('invoices')
        .delete()
        .eq('id', invoiceId);

      if (error) throw error;
      return true;
    },
    {
      onMutate: async (invoiceId) => {
        // Cancel any outgoing refetches
        await queryClient.cancelQueries({ queryKey: ['invoices'] });

        // Snapshot the previous value
        const previousInvoices = queryClient.getQueryData(['invoices', currentCompanyId, filterOptions, sortOptions]);

        // Optimistically update to the new value
        if (previousInvoices) {
          queryClient.setQueryData(
            ['invoices', currentCompanyId, filterOptions, sortOptions],
            (old: InvoiceWithRelations[] | undefined) =>
              old ? old.filter((invoice) => invoice.id !== invoiceId) : []
          );
        }

        // If the deleted invoice is selected, clear selection
        if (selectedInvoiceId === invoiceId) {
          setSelectedInvoiceId(null);
        }

        return { previousInvoices };
      },
      onSuccess: () => {
        showSuccessToast(
          'Invoice Deleted',
          'The invoice has been deleted successfully.'
        );
      },
      onError: (error, _, context) => {
        // Rollback to the previous value
        if (context?.previousInvoices) {
          queryClient.setQueryData(
            ['invoices', currentCompanyId, filterOptions, sortOptions],
            context.previousInvoices
          );
        }

        showErrorToast(
          'Failed to Delete Invoice',
          error instanceof Error ? error.message : 'An unexpected error occurred'
        );
      },
    }
  );

  /**
   * Check for overdue invoices
   */
  const checkForOverdueInvoices = useCallback(async (): Promise<void> => {
    if (!currentCompanyId) return;

    try {
      const updatedCount = await checkOverdueInvoices(currentCompanyId);

      if (updatedCount > 0) {
        queryClient.invalidateQueries({ queryKey: ['invoices'] });
        showSuccessToast(
          'Overdue Invoices Updated',
          `${updatedCount} invoice${updatedCount === 1 ? '' : 's'} marked as overdue.`
        );
      }
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : 'An unexpected error occurred';
      console.error('Error checking overdue invoices:', errorMessage);
    }
  }, [currentCompanyId, queryClient]);

  return {
    // Data
    invoices,
    selectedInvoice,
    filterOptions,
    sortOptions,

    // Loading states
    isLoadingInvoices,
    isLoadingInvoice,
    isCreatingInvoice: createInvoiceMutation.isPending,
    isUpdatingStatus: updateStatusMutation.isPending,
    isSendingEmail: sendInvoiceEmailMutation.isPending,
    isRecordingPayment: recordPaymentMutation.isPending,
    isDeletingInvoice: deleteInvoiceMutation.isPending,

    // Errors
    invoicesError,
    selectedInvoiceError,

    // Actions
    setSelectedInvoiceId,
    setFilterOptions,
    setSortOptions,
    createInvoice: createInvoiceMutation.mutate,
    updateStatus: updateStatusMutation.mutate,
    sendEmail: sendInvoiceEmailMutation.mutate,
    recordPayment: recordPaymentMutation.mutate,
    deleteInvoice: deleteInvoiceMutation.mutate,
    checkForOverdueInvoices,
    refetchInvoices,
    refetchSelectedInvoice,
  };
};
