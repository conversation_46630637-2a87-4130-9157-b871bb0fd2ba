import React, { useState, useEffect } from 'react';
import { TypedDashboardWidget, FinancialOverviewSettings } from '@/types/dashboard';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowUp, ArrowDown, Minus } from 'lucide-react';
import { useCompanyStats } from '@/hooks/useCompanyStats';

interface FinancialOverviewWidgetProps {
  widget: TypedDashboardWidget;
}

export function FinancialOverviewWidget({ widget }: FinancialOverviewWidgetProps): JSX.Element {
  const settings = widget.widget_settings as FinancialOverviewSettings;
  const { stats, isLoading } = useCompanyStats();
  
  const showRevenue = settings.showRevenue !== false;
  const showExpenses = settings.showExpenses !== false;
  const showProfit = settings.showProfit !== false;
  const showTrend = settings.showTrend !== false;
  const period = settings.period || 'monthly';
  const useFiscalYear = settings.useFiscalYear !== false;

  // Format currency for display
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Get trend indicator component
  const getTrendIndicator = (value: number) => {
    if (value === 0) {
      return (
        <div className="flex items-center text-muted-foreground">
          <Minus className="h-3 w-3 mr-1" />
          <span>No change</span>
        </div>
      );
    } else if (value > 0) {
      return (
        <div className="flex items-center text-green-600">
          <ArrowUp className="h-3 w-3 mr-1" />
          <span>+{value}%</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center text-red-600">
          <ArrowDown className="h-3 w-3 mr-1" />
          <span>{value}%</span>
        </div>
      );
    }
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Skeleton className="h-[80%] w-[80%] rounded-md" />
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col justify-between">
      <div className="space-y-4">
        {showRevenue && (
          <div>
            <div className="flex justify-between items-center mb-1">
              <h3 className="text-sm font-medium">Revenue</h3>
              {showTrend && getTrendIndicator(stats.revenueChangePercent)}
            </div>
            <p className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</p>
          </div>
        )}
        
        {showExpenses && (
          <div>
            <div className="flex justify-between items-center mb-1">
              <h3 className="text-sm font-medium">Expenses</h3>
              {showTrend && getTrendIndicator(stats.expenseChangePercent)}
            </div>
            <p className="text-2xl font-bold">{formatCurrency(stats.totalExpenses)}</p>
          </div>
        )}
        
        {showProfit && (
          <div>
            <div className="flex justify-between items-center mb-1">
              <h3 className="text-sm font-medium">Profit</h3>
              {showTrend && getTrendIndicator(stats.profitChangePercent)}
            </div>
            <p className="text-2xl font-bold">{formatCurrency(stats.totalRevenue - stats.totalExpenses)}</p>
          </div>
        )}
      </div>
      
      <div className="mt-4 text-xs text-muted-foreground">
        <p>
          {period === 'monthly' && 'Monthly data'}
          {period === 'quarterly' && 'Quarterly data'}
          {period === 'yearly' && 'Yearly data'}
          {useFiscalYear ? ' (Fiscal Year)' : ' (Calendar Year)'}
        </p>
      </div>
    </div>
  );
}
