/**
 * Credit Note Dialog Component
 * 
 * This component provides a dialog for creating credit notes against existing invoices.
 */

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { CreditCardIcon } from 'lucide-react';
import { CreditNoteForm } from './credit-note-form';
import { InvoiceWithRelations } from '@/hooks/use-enhanced-invoices';

interface CreditNoteDialogProps {
  invoice: InvoiceWithRelations;
  onCreditNoteCreated?: () => void;
}

export function CreditNoteDialog({
  invoice,
  onCreditNoteCreated
}: CreditNoteDialogProps): JSX.Element {
  const [open, setOpen] = useState(false);

  const handleSuccess = () => {
    setOpen(false);
    if (onCreditNoteCreated) {
      onCreditNoteCreated();
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <CreditCardIcon className="mr-2 h-4 w-4" />
          Issue Credit Note
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Issue Credit Note</DialogTitle>
          <DialogDescription>
            Create a credit note for invoice #{invoice.invoice_number} for {invoice.customers.name}
          </DialogDescription>
        </DialogHeader>
        
        <CreditNoteForm 
          invoice={invoice} 
          onSuccess={handleSuccess}
          onCancel={() => setOpen(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
