
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { BalanceSheetData } from '@/services/report-service';
import { format } from 'date-fns';

interface BalanceSheetReportProps {
  data: BalanceSheetData;
  date: Date;
  formatCurrency: (amount: number) => string;
  isLoading?: boolean;
}

export const BalanceSheetReport = ({ 
  data, 
  date,
  formatCurrency, 
  isLoading = false 
}: BalanceSheetReportProps) => {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Balance Sheet</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-40 flex items-center justify-center">
            <p className="text-muted-foreground">Loading balance sheet data...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalAssets = data.assets.total;
  const totalLiabilitiesEquity = data.liabilities.total + data.equity.total;
  const formatDate = format(date, 'MMMM d, yyyy');
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Balance Sheet</CardTitle>
        <p className="text-sm text-muted-foreground">As of {formatDate}</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Assets Section */}
          <section>
            <h3 className="font-medium text-lg mb-2">Assets</h3>
            <div className="space-y-1 border-b pb-2">
              {data.assets.items.map((item, index) => (
                <div key={index} className="flex justify-between">
                  <span className="text-sm">{item.name}</span>
                  <span className="text-sm">{formatCurrency(item.amount)}</span>
                </div>
              ))}
            </div>
            <div className="flex justify-between py-2 font-medium">
              <span>Total Assets</span>
              <span>{formatCurrency(totalAssets)}</span>
            </div>
          </section>
          
          {/* Liabilities Section */}
          <section>
            <h3 className="font-medium text-lg mb-2">Liabilities</h3>
            <div className="space-y-1 border-b pb-2">
              {data.liabilities.items.map((item, index) => (
                <div key={index} className="flex justify-between">
                  <span className="text-sm">{item.name}</span>
                  <span className="text-sm">{formatCurrency(item.amount)}</span>
                </div>
              ))}
            </div>
            <div className="flex justify-between py-2 font-medium">
              <span>Total Liabilities</span>
              <span>{formatCurrency(data.liabilities.total)}</span>
            </div>
          </section>
          
          {/* Equity Section */}
          <section>
            <h3 className="font-medium text-lg mb-2">Equity</h3>
            <div className="space-y-1 border-b pb-2">
              {data.equity.items.map((item, index) => (
                <div key={index} className="flex justify-between">
                  <span className="text-sm">{item.name}</span>
                  <span className="text-sm">{formatCurrency(item.amount)}</span>
                </div>
              ))}
            </div>
            <div className="flex justify-between py-2 font-medium">
              <span>Total Equity</span>
              <span>{formatCurrency(data.equity.total)}</span>
            </div>
          </section>
          
          {/* Total Liabilities & Equity */}
          <section>
            <div className="flex justify-between py-2 font-bold text-lg border-t">
              <span>Total Liabilities & Equity</span>
              <span>{formatCurrency(totalLiabilitiesEquity)}</span>
            </div>
            
            {/* Check if balanced */}
            {Math.abs(totalAssets - totalLiabilitiesEquity) > 0.01 && (
              <div className="text-red-500 text-sm mt-2">
                Warning: Balance sheet is not balanced. Difference: {formatCurrency(totalAssets - totalLiabilitiesEquity)}
              </div>
            )}
          </section>
        </div>
      </CardContent>
    </Card>
  );
};
