
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

type SummaryMetrics = {
  totalBudgetRequests: number;
  approvedBudgets: number;
  pendingBudgets: number;
  totalApprovedAmount: number;
};

type BudgetSummaryCardsProps = {
  summaryMetrics: SummaryMetrics;
  formatCurrency: (amount: number) => string;
};

export const BudgetSummaryCards = ({ summaryMetrics, formatCurrency }: BudgetSummaryCardsProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-medium">Total Budget Requests</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold">{summaryMetrics.totalBudgetRequests}</span>
            <span className="ml-2 text-muted-foreground">requests</span>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-medium">Approved Requests</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold text-green-600">{summaryMetrics.approvedBudgets}</span>
            <span className="ml-2 text-muted-foreground">approved</span>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-medium">Pending Approval</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold text-amber-600">{summaryMetrics.pendingBudgets}</span>
            <span className="ml-2 text-muted-foreground">pending</span>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-medium">Total Approved Amount</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold text-primary">{formatCurrency(summaryMetrics.totalApprovedAmount)}</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
