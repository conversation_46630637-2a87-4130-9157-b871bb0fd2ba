import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { exportToCSV, formatDateForCSV, formatCurrencyForCSV } from '@/utils/csv-export-utils';
import { Transaction } from '@/models/accountTypes';
import { format } from 'date-fns';
import { useToast } from '@/hooks/use-toast';

interface ExportTransactionsButtonProps {
  transactions: Transaction[];
  isLoading?: boolean;
}

/**
 * Button component for exporting transactions to CSV
 */
export function ExportTransactionsButton({
  transactions,
  isLoading = false
}: ExportTransactionsButtonProps) {
  const [exporting, setExporting] = useState(false);
  const { toast } = useToast();

  // Define headers for the CSV file
  const headers = {
    id: 'Transaction ID',
    date: 'Date',
    description: 'Description',
    debitAccount: 'Debit Account',
    creditAccount: 'Credit Account',
    amount: 'Amount (UGX)',
    reference: 'Reference',
    status: 'Status'
  };

  const handleExport = async () => {
    try {
      setExporting(true);

      // Format transactions for CSV export
      const formattedTransactions = transactions.map(transaction => {
        return {
          ...transaction,
          date: formatDateForCSV(transaction.date),
          amount: formatCurrencyForCSV(transaction.amount)
        };
      });

      // Generate filename with current date
      const currentDate = format(new Date(), 'yyyy-MM-dd');
      const filename = `transaction-history-${currentDate}.csv`;

      // Export to CSV
      exportToCSV(formattedTransactions, filename, headers);

      // Show success message
      toast({
        title: 'Export Successful',
        description: `${transactions.length} transactions exported to ${filename}`,
        variant: 'default'
      });
    } catch (error) {
      console.error('Error exporting transactions:', error);
      toast({
        title: 'Export Failed',
        description: 'There was an error exporting the transactions. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setExporting(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleExport}
      disabled={isLoading || exporting || transactions.length === 0}
    >
      <Download className="mr-2 h-4 w-4" />
      {exporting ? 'Exporting...' : 'Export CSV'}
    </Button>
  );
}
