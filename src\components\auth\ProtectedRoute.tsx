
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { UserRole } from '@/types/auth';
import { usePermissions } from '@/hooks/usePermissions';

interface ProtectedRouteProps {
  requiredRoles?: UserRole[];
}

const ProtectedRoute = ({ requiredRoles }: ProtectedRouteProps): JSX.Element => {
  const { user, loading, roles } = useAuth();
  const { hasAnyRole } = usePermissions();
  const location = useLocation();

  console.log("ProtectedRoute - Auth state:", {
    isAuthenticated: !!user,
    loading,
    userRoles: roles,
    requiredRoles
  });

  if (loading) {
    console.log("ProtectedRoute - Still loading auth state");
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // If not authenticated, redirect to home page
  if (!user) {
    console.log("ProtectedRoute - User not authenticated, redirecting to home");
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  // If roles are required, check if user has any of them
  if (requiredRoles && requiredRoles.length > 0) {
    const hasRole = hasAnyRole(requiredRoles);
    console.log(`ProtectedRoute - Checking roles: required=${requiredRoles.join(',')}, hasRole=${hasRole}`);

    if (!hasRole) {
      console.log("ProtectedRoute - User doesn't have required roles, redirecting to unauthorized");
      return <Navigate to="/unauthorized" replace />;
    }
  }

  console.log("ProtectedRoute - Access granted");
  return <Outlet />;
};

export default ProtectedRoute;
