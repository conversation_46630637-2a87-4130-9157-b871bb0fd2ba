
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { useToast } from './use-toast';
import { Account, AccountWithBalance } from '@/types/index';

export const useAccounts = () => {
  const [accounts, setAccounts] = useState<AccountWithBalance[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentCompanyId } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    const fetchAccounts = async () => {
      if (!currentCompanyId) return;

      try {
        setIsLoading(true);

        // First get the accounts
        const { data: accountsData, error: accountsError } = await supabase
          .from('accounts')
          .select(`
            id,
            name,
            code,
            description,
            is_active,
            company_id,
            account_type_id,
            parent_account_id,
            account_types (
              id,
              name,
              normal_balance
            )
          `)
          .eq('company_id', currentCompanyId)
          .eq('is_active', true);

        if (accountsError) {
          throw accountsError;
        }

        if (accountsData && accountsData.length > 0) {
          // Get account balances
          const { data: balancesData, error: balancesError } = await supabase
            .rpc('get_account_balances', { company_id_param: currentCompanyId });

          if (balancesError) {
            throw balancesError;
          }

          // Create a map of account ID to balance
          const balanceMap = new Map<string, number>();
          if (balancesData) {
            balancesData.forEach((item: { account_id: string, balance: number }) => {
              balanceMap.set(item.account_id, item.balance);
            });
          }

          // Convert to our AccountWithBalance type
          const fetchedAccounts: AccountWithBalance[] = accountsData.map((account) => ({
            ...account,
            balance: balanceMap.get(account.id) || 0,
            isActive: account.is_active
          }));

          setAccounts(fetchedAccounts);
        } else {
          // If no accounts found in Supabase, use a notification
          toast({
            title: "No accounts found",
            description: "Please create chart of accounts to get started.",
          });
        }
      } catch (err: any) {
        console.error('Error fetching accounts:', err);
        toast({
          title: "Error fetching accounts",
          description: err.message,
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAccounts();
  }, [currentCompanyId, toast]);

  const createAccount = async (accountData: Partial<Account>) => {
    try {
      if (!currentCompanyId) {
        toast({
          title: "Error",
          description: "No company selected",
          variant: "destructive"
        });
        return null;
      }

      const { data, error } = await supabase
        .from('accounts')
        .insert({
          ...accountData,
          company_id: currentCompanyId,
          is_active: true
        })
        .select()
        .single();

      if (error) throw error;

      // Add the new account to the state with a zero balance
      const newAccountWithBalance: AccountWithBalance = {
        ...data,
        balance: 0,
        isActive: data.is_active
      };

      setAccounts(prev => [...prev, newAccountWithBalance]);

      toast({
        title: "Account Created",
        description: `Account ${data.name} has been created successfully.`
      });

      return newAccountWithBalance;
    } catch (err: any) {
      console.error('Error creating account:', err);
      toast({
        title: "Error creating account",
        description: err.message,
        variant: "destructive"
      });
      return null;
    }
  };

  const updateAccount = async (id: string, updates: Partial<Account>) => {
    try {
      const { error } = await supabase
        .from('accounts')
        .update(updates)
        .eq('id', id);

      if (error) throw error;

      // Update the account in the state
      setAccounts(prev =>
        prev.map(account =>
          account.id === id
            ? { ...account, ...updates, isActive: updates.is_active !== undefined ? updates.is_active : account.isActive }
            : account
        )
      );

      toast({
        title: "Account Updated",
        description: `Account has been updated successfully.`
      });

      return true;
    } catch (err: any) {
      console.error('Error updating account:', err);
      toast({
        title: "Error updating account",
        description: err.message,
        variant: "destructive"
      });
      return false;
    }
  };

  const deleteAccount = async (id: string) => {
    try {
      // Soft delete by setting is_active to false
      const { error } = await supabase
        .from('accounts')
        .update({ is_active: false })
        .eq('id', id);

      if (error) throw error;

      // Remove the account from the state
      setAccounts(prev => prev.filter(account => account.id !== id));

      toast({
        title: "Account Deleted",
        description: `Account has been deleted successfully.`
      });

      return true;
    } catch (err: any) {
      console.error('Error deleting account:', err);
      toast({
        title: "Error deleting account",
        description: err.message,
        variant: "destructive"
      });
      return false;
    }
  };

  return {
    accounts,
    isLoading,
    createAccount,
    updateAccount,
    deleteAccount
  };
};
