import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  FileText,
  DollarSign,
  BarChart3,
  Menu
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface BottomNavigationProps {
  toggleSidebar: () => void;
}

/**
 * Mobile bottom navigation bar with key navigation items
 */
const BottomNavigation = ({ toggleSidebar }: BottomNavigationProps): React.JSX.Element => {
  const location = useLocation();
  const currentPath = location.pathname;

  // Only include the most important navigation items for the bottom bar
  const navigation = [
    { name: 'Home', href: '/dashboard', icon: Home },
    { name: 'Ledger', href: '/general-ledger', icon: FileText },
    { name: 'Invoices', href: '/accounts-receivable', icon: DollarSign },
    { name: 'Reports', href: '/reports', icon: BarChart3 },
    { name: 'Menu', href: '#', icon: Menu, action: toggleSidebar },
  ];

  return (
    <div className="md:hidden fixed bottom-0 left-0 right-0 bg-background border-t border-border z-30">
      <div className="flex items-center justify-around h-16">
        {navigation.map((item) => (
          <div key={item.name} className="flex-1">
            {item.action ? (
              <button
                onClick={item.action}
                className={cn(
                  "flex flex-col items-center justify-center w-full h-full",
                  "text-muted-foreground hover:text-secondary transition-colors"
                )}
              >
                <item.icon className="h-5 w-5 mb-1" />
                <span className="text-xs">{item.name}</span>
              </button>
            ) : (
              <Link
                to={item.href}
                className={cn(
                  "flex flex-col items-center justify-center w-full h-full",
                  currentPath === item.href
                    ? "text-secondary font-medium"
                    : "text-muted-foreground hover:text-secondary",
                  "transition-colors"
                )}
              >
                <item.icon className="h-5 w-5 mb-1" />
                <span className="text-xs">{item.name}</span>
              </Link>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default BottomNavigation;
