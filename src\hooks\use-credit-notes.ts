/**
 * Hook for managing credit notes with React Query
 * 
 * This hook provides functionality to fetch, create, and update credit notes,
 * as well as manage the selected credit note state.
 */

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { 
  CreditNoteWithRelations, 
  CreditNoteFormValues, 
  createCreditNote, 
  updateCreditNoteStatus 
} from '@/services/credit-note-service';
import { showSuccessToast, showErrorToast } from '@/utils/toast-utils';

/**
 * Hook for managing credit notes
 * 
 * @returns Object containing credit note data and mutation functions
 */
export const useCreditNotes = () => {
  const { currentCompanyId, user } = useAuth();
  const queryClient = useQueryClient();
  const [selectedCreditNoteId, setSelectedCreditNoteId] = useState<string | null>(null);

  /**
   * Query to fetch all credit notes for the current company
   */
  const {
    data: creditNotes,
    isLoading: isLoadingCreditNotes,
    error: creditNotesError,
    refetch: refetchCreditNotes,
  } = useQuery<CreditNoteWithRelations[]>({
    queryKey: ['creditNotes', currentCompanyId],
    queryFn: async () => {
      if (!currentCompanyId) return [];

      const { data, error } = await supabase
        .from('credit_notes')
        .select(`
          *,
          customers (
            id, name, email
          ),
          invoices (
            id, invoice_number
          ),
          credit_note_items (
            id, description, quantity, unit_price, tax_rate, line_total
          )
        `)
        .eq('company_id', currentCompanyId)
        .order('issue_date', { ascending: false });

      if (error) throw error;
      return data as CreditNoteWithRelations[];
    },
    enabled: !!currentCompanyId,
  });

  /**
   * Query to fetch a single credit note by ID
   */
  const {
    data: selectedCreditNote,
    isLoading: isLoadingSelectedCreditNote,
    error: selectedCreditNoteError,
  } = useQuery<CreditNoteWithRelations>({
    queryKey: ['creditNote', selectedCreditNoteId],
    queryFn: async () => {
      if (!selectedCreditNoteId) throw new Error('No credit note selected');

      const { data, error } = await supabase
        .from('credit_notes')
        .select(`
          *,
          customers (
            id, name, email
          ),
          invoices (
            id, invoice_number
          ),
          credit_note_items (
            id, description, quantity, unit_price, tax_rate, line_total
          )
        `)
        .eq('id', selectedCreditNoteId)
        .single();

      if (error) throw error;
      return data as CreditNoteWithRelations;
    },
    enabled: !!selectedCreditNoteId,
  });

  /**
   * Mutation for creating a new credit note
   */
  const createCreditNoteMutation = useMutation({
    mutationFn: async (creditNoteData: {
      formData: CreditNoteFormValues;
      customerId: string;
    }) => {
      if (!currentCompanyId || !user) throw new Error("Authentication required");

      return await createCreditNote(
        creditNoteData.formData,
        currentCompanyId,
        creditNoteData.customerId,
        user.id
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['creditNotes'] });
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
    },
  });

  /**
   * Mutation for updating credit note status
   */
  const updateCreditNoteStatusMutation = useMutation({
    mutationFn: async (data: {
      creditNoteId: string;
      newStatus: 'draft' | 'issued' | 'approved' | 'rejected';
      notes?: string;
    }) => {
      if (!user) throw new Error("Authentication required");

      return await updateCreditNoteStatus(
        data.creditNoteId,
        data.newStatus,
        user.id,
        data.notes
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['creditNotes'] });
      queryClient.invalidateQueries({ queryKey: ['creditNote'] });
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
    },
  });

  /**
   * Issue a credit note (change status from draft to issued)
   */
  const issueCreditNote = async (creditNoteId: string) => {
    return updateCreditNoteStatusMutation.mutateAsync({
      creditNoteId,
      newStatus: 'issued',
      notes: 'Credit note issued'
    });
  };

  /**
   * Approve a credit note
   */
  const approveCreditNote = async (creditNoteId: string, notes?: string) => {
    return updateCreditNoteStatusMutation.mutateAsync({
      creditNoteId,
      newStatus: 'approved',
      notes
    });
  };

  /**
   * Reject a credit note
   */
  const rejectCreditNote = async (creditNoteId: string, notes: string) => {
    return updateCreditNoteStatusMutation.mutateAsync({
      creditNoteId,
      newStatus: 'rejected',
      notes
    });
  };

  return {
    creditNotes,
    isLoadingCreditNotes,
    creditNotesError,
    refetchCreditNotes,
    selectedCreditNote,
    isLoadingSelectedCreditNote,
    selectedCreditNoteError,
    selectedCreditNoteId,
    setSelectedCreditNoteId,
    createCreditNoteMutation,
    issueCreditNote,
    approveCreditNote,
    rejectCreditNote,
  };
};
