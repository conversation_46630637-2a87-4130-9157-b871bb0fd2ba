import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowRight } from 'lucide-react';
import { companySchema, CompanyFormValues } from '@/validations/company-schema';
import { FormValidationFeedback } from '@/components/ui/form-validation-feedback';
import { FormGrid, FormGridItem, FormGridFullWidth } from '@/components/ui/form-grid';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';

interface CompanySetupFormProps {
  /**
   * Callback function called when the form is successfully submitted
   */
  onComplete: (data: CompanyFormValues) => void;

  /**
   * Initial form data (if available)
   */
  initialData?: CompanyFormValues;
}

/**
 * Company setup form for the onboarding wizard
 */
export function CompanySetupForm({ onComplete, initialData }: CompanySetupFormProps) {
  const { toast } = useToast();
  const { user } = useAuth();

  // Initialize form with default values or initial data
  const form = useForm<CompanyFormValues>({
    resolver: zodResolver(companySchema),
    defaultValues: initialData || {
      name: '',
      tax_id: '',
      address: '',
      city: '',
      country: 'Uganda',
      postal_code: '',
      phone: '',
      email: '',
      website: '',
    },
  });

  // Handle form submission
  const onSubmit = async (data: CompanyFormValues) => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to create a company',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Create company in Supabase
      const { data: company, error } = await supabase
        .from('companies')
        .insert({
          name: data.name,
          tax_id: data.tax_id || null,
          address: data.address || null,
          city: data.city || null,
          country: data.country,
          postal_code: data.postal_code || null,
          phone: data.phone || null,
          email: data.email || null,
          website: data.website || null,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Assign admin role to user
      const { error: roleError } = await supabase
        .from('user_roles')
        .insert({
          user_id: user.id,
          company_id: company.id,
          role: 'admin',
        });

      if (roleError) {
        throw roleError;
      }

      // Call onComplete callback with form data
      onComplete(data);

    } catch (error: any) {
      console.error('Error creating company:', error);

      toast({
        title: 'Error',
        description: error.message || 'Failed to create company',
        variant: 'destructive',
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormGrid cols={1} colsMd={2} gap={4}>
          <FormGridFullWidth>
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company Name *</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter company name" {...field} />
                  </FormControl>
                  <FormMessage />
                  <FormValidationFeedback name="name" schema={companySchema} />
                </FormItem>
              )}
            />
          </FormGridFullWidth>

          <FormGridItem span={1} spanMd={1}>
            <FormField
              control={form.control}
              name="tax_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tax ID</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter tax ID" {...field} />
                  </FormControl>
                  <FormDescription>
                    Your company's tax identification number
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </FormGridItem>

          <FormGridItem span={1} spanMd={1}>
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input placeholder="+256 **********" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </FormGridItem>

          <FormGridFullWidth>
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter company address" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </FormGridFullWidth>

          <FormGridItem span={1} spanMd={1}>
            <FormField
              control={form.control}
              name="city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter city" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </FormGridItem>

          <FormGridItem span={1} spanMd={1}>
            <FormField
              control={form.control}
              name="country"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Country *</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select country" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Uganda">Uganda</SelectItem>
                      <SelectItem value="Kenya">Kenya</SelectItem>
                      <SelectItem value="Tanzania">Tanzania</SelectItem>
                      <SelectItem value="Rwanda">Rwanda</SelectItem>
                      <SelectItem value="Burundi">Burundi</SelectItem>
                      <SelectItem value="South Sudan">South Sudan</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </FormGridItem>

          <FormGridItem span={1} spanMd={1}>
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </FormGridItem>

          <FormGridItem span={1} spanMd={1}>
            <FormField
              control={form.control}
              name="website"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Website</FormLabel>
                  <FormControl>
                    <Input placeholder="https://example.com" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </FormGridItem>
        </FormGrid>

        <div className="flex justify-end pt-4">
          <Button type="submit" className="flex items-center gap-2">
            Continue
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </form>
    </Form>
  );
}
