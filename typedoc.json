{"entryPoints": ["src/hooks/usePermissions.ts", "src/hooks/use-invoices.ts", "src/hooks/useAccounts.ts", "src/hooks/useTransactions.ts", "src/hooks/useRoleManagement.ts", "src/hooks/useProfileManagement.ts", "src/hooks/use-reports.ts", "src/hooks/use-customers.ts"], "entryPointStrategy": "resolve", "out": "docs/api", "name": "Kaya Finance API Documentation", "includeVersion": true, "excludePrivate": true, "excludeExternals": true, "readme": "none", "plugin": ["typedoc-plugin-markdown"]}