
import React from 'react';
import { Button } from '@/components/ui/button';
import { PlusCircle, Upload, Download } from 'lucide-react';
import { ExportTransactionsButton } from './ExportTransactionsButton';
import { Transaction } from '@/models/accountTypes';

interface LedgerHeaderProps {
  onImportClick: () => void;
  onAddTransactionClick: () => void;
  transactions?: Transaction[];
  isLoading?: boolean;
}

const LedgerHeader: React.FC<LedgerHeaderProps> = ({
  onImportClick,
  onAddTransactionClick,
  transactions = [],
  isLoading = false
}): React.JSX.Element => {
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6 w-full">
      <div>
        <h1 className="page-title mb-1">General Ledger</h1>
        <p className="text-muted-foreground">Manage your chart of accounts and view transaction history</p>
      </div>
      <div className="flex flex-wrap gap-2">
        <ExportTransactionsButton
          transactions={transactions}
          isLoading={isLoading}
        />
        <Button onClick={onImportClick}>
          <Upload className="mr-2 h-4 w-4" />
          Import Data
        </Button>
        <Button onClick={onAddTransactionClick}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Add Transaction
        </Button>
      </div>
    </div>
  );
};

export default LedgerHeader;
