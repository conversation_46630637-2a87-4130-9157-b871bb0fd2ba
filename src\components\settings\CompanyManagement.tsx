
import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { Company } from '@/types/auth';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Building } from 'lucide-react';
import { usePermissions } from '@/hooks/usePermissions';

// Validation schema
const companySchema = z.object({
  name: z.string().min(2, { message: 'Company name must be at least 2 characters' }),
  tax_id: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  country: z.string().min(1, { message: 'Country is required' }),
  postal_code: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email({ message: 'Invalid email format' }).optional().or(z.string().length(0)),
  website: z.string().url({ message: 'Invalid URL format' }).optional().or(z.string().length(0)),
});

type CompanyFormValues = z.infer<typeof companySchema>;

export function CompanyManagement() {
  const { user } = useAuth();
  const [company, setCompany] = useState<Company | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { canManageCompany } = usePermissions();

  const form = useForm<CompanyFormValues>({
    resolver: zodResolver(companySchema),
    defaultValues: {
      name: '',
      tax_id: '',
      address: '',
      city: '',
      country: 'Uganda',
      postal_code: '',
      phone: '',
      email: '',
      website: '',
    },
  });

  // Fetch company data
  useEffect(() => {
    const fetchCompany = async () => {
      try {
        setLoading(true);
        
        const { data, error } = await supabase
          .from('companies')
          .select('*')
          .limit(1)
          .single();

        if (error) {
          console.error('Error fetching company:', error);
          toast({
            title: "Error",
            description: "Failed to load company information",
            variant: "destructive",
          });
          return;
        }

        if (data) {
          setCompany(data);
          
          // Update form values
          form.reset({
            name: data.name || '',
            tax_id: data.tax_id || '',
            address: data.address || '',
            city: data.city || '',
            country: data.country || 'Uganda',
            postal_code: data.postal_code || '',
            phone: data.phone || '',
            email: data.email || '',
            website: data.website || '',
          });
        }
      } catch (error) {
        console.error('Unexpected error:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCompany();
  }, []);

  // Handle form submission
  const onSubmit = async (values: CompanyFormValues) => {
    if (!canManageCompany()) {
      toast({
        title: "Permission Denied",
        description: "You don't have permission to update company details",
        variant: "destructive",
      });
      return;
    }

    try {
      if (company) {
        // Update existing company
        const { error } = await supabase
          .from('companies')
          .update(values)
          .eq('id', company.id);

        if (error) throw error;

        // Update local state
        setCompany({
          ...company,
          ...values
        });

        toast({
          title: "Company updated",
          description: "Company information has been updated successfully",
        });
      } else if (user) {
        // Create new company if none exists
        const { data, error } = await supabase
          .from('companies')
          .insert({
            ...values,
            owner_id: user.id,
          })
          .select()
          .single();

        if (error) throw error;

        setCompany(data);

        // Assign the current user as an admin
        const { error: roleError } = await supabase
          .from('user_roles')
          .insert({
            user_id: user.id,
            company_id: data.id,
            role: 'admin',
          });

        if (roleError) throw roleError;

        toast({
          title: "Company created",
          description: "Company has been created successfully",
        });
      }
    } catch (error: any) {
      console.error('Error saving company:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to save company details",
        variant: "destructive",
      });
    }
  };

  // Determine if user can edit details
  const canEdit = canManageCompany();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Company Information</CardTitle>
        <CardDescription>View and manage your company details</CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company Name</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="Acme Corporation" 
                          {...field} 
                          disabled={!canEdit} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="tax_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tax ID</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Tax ID" 
                            {...field} 
                            disabled={!canEdit} 
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="+256 xxx xxx xxx" 
                            {...field} 
                            disabled={!canEdit} 
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="<EMAIL>" 
                            {...field} 
                            disabled={!canEdit} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="website"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Website</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="https://example.com" 
                            {...field} 
                            disabled={!canEdit} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="123 Main St" 
                          {...field} 
                          disabled={!canEdit} 
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>City</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Kampala" 
                            {...field} 
                            disabled={!canEdit} 
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Uganda" 
                            {...field} 
                            disabled={!canEdit} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="postal_code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Postal Code</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Postal Code" 
                            {...field} 
                            disabled={!canEdit} 
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {canEdit && (
                <Button type="submit" className="mt-6">
                  {company ? 'Update Company' : 'Create Company'}
                </Button>
              )}

              {!company && !loading && (
                <div className="mt-4 p-4 bg-muted rounded-md">
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Building size={16} />
                    <span>No company found. Please create one using the form above.</span>
                  </div>
                </div>
              )}
            </form>
          </Form>
        )}
      </CardContent>
    </Card>
  );
}
