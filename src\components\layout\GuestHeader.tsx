import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Menu, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { LoginModal } from '@/components/auth/LoginModal';

interface GuestHeaderProps {
  // Add any props if needed
}

/**
 * Shared header component for guest pages with enhanced styling
 */
export function GuestHeader({}: GuestHeaderProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const toggleMobileMenu = (): void => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <header className="fixed top-0 left-0 right-0 bg-gradient-to-r from-cream-alt to-cream-light backdrop-blur-sm bg-opacity-90 border-b border-border/50 z-50 shadow-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Logo/Name with hover effect */}
          <div className="flex items-center">
            <Link 
              to="/" 
              className="flex items-center group"
              aria-label="Home"
            >
              <span className="text-2xl font-bold text-dark-blue transition-colors duration-300 group-hover:text-dark-blue/80">
                Kaya<span className="text-emerald-600">Finance</span>
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-4">
            <LoginModal
              trigger={
                <Button 
                  variant="outline" 
                  className="border-dark-blue text-dark-blue hover:bg-dark-blue/5 hover:border-dark-blue/80 transition-colors duration-200"
                >
                  Login
                </Button>
              }
            />
            <Button 
              asChild 
              className="bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-500 hover:to-emerald-400 text-white shadow-md hover:shadow-lg transition-all duration-300"
            >
              <a 
                href="mailto:<EMAIL>?subject=Get%20Started%20with%20Kaya%20Finance%20Flow"
                className="px-6 py-2"
              >
                Book a Demo
              </a>
            </Button>
          </nav>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={toggleMobileMenu} 
              aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
              className="text-dark-blue hover:bg-dark-blue/10"
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Menu with smooth animation */}
        <div className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${mobileMenuOpen ? 'max-h-40 py-4' : 'max-h-0 py-0'}`}>
          <div className="flex flex-col space-y-3 pt-2 border-t border-border/30">
            <LoginModal
              trigger={
                <Button 
                  variant="outline" 
                  className="w-full border-dark-blue text-dark-blue hover:bg-dark-blue/5"
                >
                  Login
                </Button>
              }
            />
            <Button 
              asChild 
              className="w-full bg-emerald-600 hover:bg-emerald-500 text-white"
            >
              <a href="mailto:<EMAIL>?subject=Get%20Started%20with%20Kaya%20Finance%20Flow">
                Book a Demo
              </a>
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}