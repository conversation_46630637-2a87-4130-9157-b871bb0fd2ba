import { PayeTaxBracket } from "./types";

/**
 * Define Uganda PAYE tax brackets
 */
export const ugandaPayeTaxBrackets: PayeTaxBracket[] = [
  { min: 0, max: 235000, rate: 0, description: "Not taxable" },
  { min: 235001, max: 335000, rate: 10, description: "10% of the amount exceeding UGX 235,000" },
  { min: 335001, max: 410000, rate: 20, description: "UGX 10,000 plus 20% of the amount exceeding UGX 335,000" },
  { min: 410001, max: 10000000, rate: 30, description: "UGX 25,000 plus 30% of the amount exceeding UGX 410,000" },
  { min: 10000001, max: null, rate: 40, description: "UGX 2,902,500 plus 40% of the amount exceeding UGX 10,000,000" }
];

/**
 * Calculate PAYE tax for a given salary in Uganda
 * 
 * @param salary Monthly salary in UGX
 * @returns Tax amount in UGX
 */
export const calculateUgandaPayeTax = (salary: number): number => {
  if (salary <= 235000) return 0;
  if (salary <= 335000) return (salary - 235000) * 0.1;
  if (salary <= 410000) return 10000 + (salary - 335000) * 0.2;
  if (salary <= 10000000) return 25000 + (salary - 410000) * 0.3;
  return 2902500 + (salary - 10000000) * 0.4;
};

/**
 * Calculate Local Service Tax for a given salary in Uganda
 * 
 * @param salary Monthly salary in UGX
 * @returns Tax amount in UGX
 */
export const calculateLocalServiceTax = (salary: number): number => {
  if (salary < 100000) return 0;
  if (salary < 200000) return 5000;
  if (salary < 300000) return 10000;
  if (salary < 400000) return 20000;
  if (salary < 500000) return 30000;
  if (salary < 600000) return 40000;
  if (salary < 700000) return 60000;
  if (salary < 800000) return 70000;
  if (salary < 900000) return 80000;
  if (salary < 1000000) return 90000;
  return 100000;
};
