
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { CircleDollarSign, ArrowDown, ArrowUp } from "lucide-react";
import { cn } from "@/lib/utils";
import { useRecentTransactions } from "@/hooks/useRecentTransactions";
import { Skeleton } from "@/components/ui/skeleton";

export const RecentTransactions = (): JSX.Element => {
  const { transactions, isLoading } = useRecentTransactions();

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat("en-UG", {
      style: "currency",
      currency: "UGX",
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      day: "numeric",
      month: "short",
    });
  };

  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Recent Transactions</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {Array(5).fill(0).map((_, i) => (
              <div key={i} className="flex items-start justify-between pb-4 border-b border-border last:border-0 last:pb-0">
                <div className="flex items-start space-x-3">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div>
                    <Skeleton className="h-4 w-32 mb-2" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                </div>
                <Skeleton className="h-4 w-20" />
              </div>
            ))}
          </div>
        ) : transactions.length === 0 ? (
          <div className="py-8 text-center text-muted-foreground">
            <CircleDollarSign className="mx-auto h-8 w-8 opacity-40 mb-2" />
            <p>No recent transactions found</p>
            <p className="text-sm mt-1">Transactions will appear here as they are recorded</p>
          </div>
        ) : (
          <div className="space-y-4">
            {transactions.map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-start justify-between pb-4 border-b border-border last:border-0 last:pb-0"
              >
                <div className="flex items-start space-x-3">
                  <div
                    className={cn(
                      "p-2 rounded-full mt-1",
                      transaction.type === "income"
                        ? "bg-green-100 text-green-600"
                        : "bg-red-100 text-red-600"
                    )}
                  >
                    {transaction.type === "income" ? (
                      <ArrowDown className="h-3 w-3" />
                    ) : (
                      <ArrowUp className="h-3 w-3" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-sm">{transaction.description}</p>
                    <p className="text-xs text-muted-foreground">
                      {transaction.category} - {formatDate(transaction.date)}
                    </p>
                  </div>
                </div>
                <div
                  className={cn(
                    "font-medium",
                    transaction.type === "income"
                      ? "text-green-600"
                      : "text-red-600"
                  )}
                >
                  {transaction.type === "income" ? "+" : "-"}
                  {formatCurrency(transaction.amount)}
                </div>
              </div>
            ))}
          </div>
        )}
        <div className="mt-4 text-center">
          <a
            href="/general-ledger"
            className="text-sm text-primary hover:underline"
          >
            View all transactions
          </a>
        </div>
      </CardContent>
    </Card>
  );
};
