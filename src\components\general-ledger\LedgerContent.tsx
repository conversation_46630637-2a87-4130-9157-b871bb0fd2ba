
import React from 'react';
import { TabsContent } from '@/components/ui/tabs';
import AccountsTable from './AccountsTable';
import TransactionsTable from './TransactionsTable';
import { RecurringTransactionsTable } from './RecurringTransactionsTable';
import { BatchTransactionsTable } from './BatchTransactionsTable';
import { AccountWithBalance, TransactionWithAccounts } from '@/types/index';
import { RecurringTransactionWithItems } from '@/types/recurring-transactions';
import { useRecurringTransactions } from '@/hooks/useRecurringTransactions';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { RecurringTransactionDialog } from './RecurringTransactionDialog';
import { TransactionApprovalDashboard } from './TransactionApprovalDashboard';

interface LedgerContentProps {
  activeTab: 'accounts' | 'transactions' | 'recurring' | 'batch' | 'approval';
  accounts: AccountWithBalance[];
  transactions: TransactionWithAccounts[];
  searchTerm: string;
  isLoading: boolean;
  onEditTransaction: (transaction: TransactionWithAccounts) => void;
  onDeleteTransaction: (transactionId: string) => void;
}

const LedgerContent: React.FC<LedgerContentProps> = ({
  activeTab,
  accounts,
  transactions,
  searchTerm,
  isLoading,
  onEditTransaction,
  onDeleteTransaction
}): React.JSX.Element => {
  const {
    recurringTransactions,
    isLoading: isLoadingRecurring,
    deleteRecurringTransaction,
    updateStatus,
    generateTransactionNow
  } = useRecurringTransactions();

  // Filter recurring transactions by search term
  const filteredRecurringTransactions = React.useMemo(() => {
    if (!searchTerm) return recurringTransactions;

    const lowerSearchTerm = searchTerm.toLowerCase();
    return recurringTransactions.filter(tx =>
      tx.name.toLowerCase().includes(lowerSearchTerm) ||
      tx.description?.toLowerCase().includes(lowerSearchTerm) ||
      tx.frequency.toLowerCase().includes(lowerSearchTerm)
    );
  }, [recurringTransactions, searchTerm]);

  return (
    <>
      <TabsContent value="accounts" className="mt-0">
        {isLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : (
          <AccountsTable
            accounts={accounts}
            searchTerm={searchTerm}
          />
        )}
      </TabsContent>

      <TabsContent value="transactions" className="mt-0">
        <TransactionsTable
          transactions={transactions}
          searchTerm={searchTerm}
          onEditTransaction={onEditTransaction}
          onDeleteTransaction={onDeleteTransaction}
        />
      </TabsContent>

      <TabsContent value="recurring" className="mt-0">
        <div className="flex justify-end mb-4">
          <RecurringTransactionDialog accounts={accounts} />
        </div>
        <RecurringTransactionsTable
          recurringTransactions={filteredRecurringTransactions}
          accounts={accounts}
          onDelete={deleteRecurringTransaction}
          onUpdateStatus={updateStatus}
          onGenerateNow={generateTransactionNow}
          isLoading={isLoadingRecurring}
        />
      </TabsContent>

      <TabsContent value="batch" className="mt-0">
        <BatchTransactionsTable accounts={accounts} />
      </TabsContent>

      <TabsContent value="approval" className="mt-0">
        <TransactionApprovalDashboard />
      </TabsContent>
    </>
  );
};

export default LedgerContent;
