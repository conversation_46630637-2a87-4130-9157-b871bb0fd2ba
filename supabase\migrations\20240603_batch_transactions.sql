-- Migration to add batch transaction functionality

-- Create transaction batches table
CREATE TABLE IF NOT EXISTS transaction_batches (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  transaction_date DATE NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create index for efficient querying
CREATE INDEX idx_transaction_batches_company ON transaction_batches(company_id);
CREATE INDEX idx_transaction_batches_status ON transaction_batches(status);

-- Add batch_id field to transactions table
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS batch_id UUID REFERENCES transaction_batches(id);

-- Create index for efficient querying
CREATE INDEX idx_transactions_batch_id ON transactions(batch_id);

-- Function to update batch status based on transaction statuses
CREATE OR REPLACE FUNCTION update_batch_status()
RETURNS TRIGGER AS $$
DECLARE
  v_batch_id UUID;
  v_pending_count INTEGER;
  v_approved_count INTEGER;
  v_rejected_count INTEGER;
  v_total_count INTEGER;
BEGIN
  -- Get the batch ID
  v_batch_id := NEW.batch_id;
  
  -- If no batch ID, do nothing
  IF v_batch_id IS NULL THEN
    RETURN NEW;
  END IF;
  
  -- Count transactions by status
  SELECT 
    COUNT(*) FILTER (WHERE status = 'pending'),
    COUNT(*) FILTER (WHERE status = 'approved'),
    COUNT(*) FILTER (WHERE status = 'rejected'),
    COUNT(*)
  INTO 
    v_pending_count,
    v_approved_count,
    v_rejected_count,
    v_total_count
  FROM transactions
  WHERE batch_id = v_batch_id;
  
  -- Update batch status based on transaction statuses
  IF v_rejected_count > 0 THEN
    -- If any transaction is rejected, the batch is rejected
    UPDATE transaction_batches
    SET status = 'rejected', updated_at = NOW()
    WHERE id = v_batch_id;
  ELSIF v_pending_count = 0 AND v_approved_count = v_total_count THEN
    -- If all transactions are approved, the batch is approved
    UPDATE transaction_batches
    SET status = 'approved', updated_at = NOW()
    WHERE id = v_batch_id;
  ELSE
    -- Otherwise, the batch is pending
    UPDATE transaction_batches
    SET status = 'pending', updated_at = NOW()
    WHERE id = v_batch_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update batch status when transaction status changes
CREATE TRIGGER update_batch_status_on_transaction_update
AFTER UPDATE OF status ON transactions
FOR EACH ROW
WHEN (OLD.status IS DISTINCT FROM NEW.status)
EXECUTE FUNCTION update_batch_status();

-- Function to post all transactions in a batch
CREATE OR REPLACE FUNCTION post_batch_transactions(p_batch_id UUID)
RETURNS INTEGER AS $$
DECLARE
  v_transaction_count INTEGER := 0;
BEGIN
  -- Update all pending transactions in the batch to approved
  UPDATE transactions
  SET status = 'approved'
  WHERE batch_id = p_batch_id AND status = 'pending';
  
  GET DIAGNOSTICS v_transaction_count = ROW_COUNT;
  
  -- Update the batch status
  UPDATE transaction_batches
  SET status = 'approved', updated_at = NOW()
  WHERE id = p_batch_id;
  
  RETURN v_transaction_count;
END;
$$ LANGUAGE plpgsql;

-- Function to reverse all transactions in a batch
CREATE OR REPLACE FUNCTION reverse_batch_transactions(p_batch_id UUID)
RETURNS INTEGER AS $$
DECLARE
  v_transaction_record RECORD;
  v_new_transaction_id UUID;
  v_item_record RECORD;
  v_transaction_count INTEGER := 0;
  v_batch_record RECORD;
BEGIN
  -- Get the batch record
  SELECT * INTO v_batch_record
  FROM transaction_batches
  WHERE id = p_batch_id;
  
  -- Create a reversal batch
  INSERT INTO transaction_batches (
    company_id,
    name,
    description,
    transaction_date,
    status,
    created_by
  ) VALUES (
    v_batch_record.company_id,
    'Reversal of ' || v_batch_record.name,
    'Automatic reversal of batch ' || v_batch_record.id,
    CURRENT_DATE,
    'pending',
    v_batch_record.created_by
  ) RETURNING id INTO v_new_transaction_id;
  
  -- For each transaction in the batch
  FOR v_transaction_record IN 
    SELECT * FROM transactions WHERE batch_id = p_batch_id
  LOOP
    v_transaction_count := v_transaction_count + 1;
    
    -- Create a reversal transaction
    INSERT INTO transactions (
      company_id,
      transaction_date,
      description,
      reference,
      created_by,
      batch_id,
      status
    ) VALUES (
      v_transaction_record.company_id,
      CURRENT_DATE,
      'Reversal of ' || v_transaction_record.description,
      'Reversal of ' || COALESCE(v_transaction_record.reference, v_transaction_record.id::text),
      v_transaction_record.created_by,
      v_new_transaction_id,
      'pending'
    ) RETURNING id INTO v_new_transaction_id;
    
    -- For each item in the transaction, create a reversal item with debits and credits swapped
    FOR v_item_record IN 
      SELECT * FROM transaction_items WHERE transaction_id = v_transaction_record.id
    LOOP
      INSERT INTO transaction_items (
        transaction_id,
        account_id,
        description,
        debit,
        credit
      ) VALUES (
        v_new_transaction_id,
        v_item_record.account_id,
        'Reversal of ' || COALESCE(v_item_record.description, v_transaction_record.description),
        v_item_record.credit,  -- Swap debit and credit
        v_item_record.debit    -- Swap debit and credit
      );
    END LOOP;
  END LOOP;
  
  RETURN v_transaction_count;
END;
$$ LANGUAGE plpgsql;
