
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { BalanceSheetData, ProfitAndLossData, CashFlowData } from '@/services/report-service';
import { BalanceSheetReport } from '@/components/reports/balance-sheet-report';
import { ProfitLossReport } from '@/components/reports/profit-loss-report';
import { CashFlowReport } from '@/components/reports/cash-flow-report';
import { FileText } from 'lucide-react';
import { DateRangeSelector } from './date-range-selector';

interface FinancialReportsTabProps {
  dateRange: string;
  startDate: Date;
  endDate: Date;
  setDateRange: (value: string) => void;
  setStartDate: (date: Date) => void;
  setEndDate: (date: Date) => void;
  balanceSheet: BalanceSheetData | null | undefined;
  isLoadingBalanceSheet: boolean;
  profitAndLoss: ProfitAndLossData | null | undefined;
  isLoadingProfitAndLoss: boolean;
  cashFlow: CashFlowData | null | undefined;
  isLoadingCashFlow: boolean;
  formatCurrency: (amount: number) => string;
}

export function FinancialReportsTab({
  dateRange,
  startDate,
  endDate,
  setDateRange,
  setStartDate,
  setEndDate,
  balanceSheet,
  isLoadingBalanceSheet,
  profitAndLoss,
  isLoadingProfitAndLoss,
  cashFlow,
  isLoadingCashFlow,
  formatCurrency
}: FinancialReportsTabProps) {
  return (
    <div className="space-y-8">
      <DateRangeSelector 
        dateRange={dateRange}
        startDate={startDate}
        endDate={endDate}
        setDateRange={setDateRange}
        setStartDate={setStartDate}
        setEndDate={setEndDate}
      />
      
      <div className="grid grid-cols-1 gap-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Balance Sheet */}
          {balanceSheet ? (
            <BalanceSheetReport 
              data={balanceSheet} 
              date={endDate}
              formatCurrency={formatCurrency} 
              isLoading={isLoadingBalanceSheet} 
            />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Balance Sheet</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-40 flex flex-col items-center justify-center gap-2">
                  <FileText className="h-8 w-8 text-muted-foreground" />
                  <p className="text-muted-foreground">No balance sheet data available</p>
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* Profit & Loss */}
          {profitAndLoss ? (
            <ProfitLossReport
              data={profitAndLoss}
              startDate={startDate}
              endDate={endDate}
              formatCurrency={formatCurrency}
              isLoading={isLoadingProfitAndLoss}
            />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Profit & Loss</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-40 flex flex-col items-center justify-center gap-2">
                  <FileText className="h-8 w-8 text-muted-foreground" />
                  <p className="text-muted-foreground">No profit & loss data available</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
        
        {/* Cash Flow */}
        {cashFlow ? (
          <CashFlowReport
            data={cashFlow}
            startDate={startDate}
            endDate={endDate}
            formatCurrency={formatCurrency}
            isLoading={isLoadingCashFlow}
          />
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Cash Flow</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-40 flex flex-col items-center justify-center gap-2">
                <FileText className="h-8 w-8 text-muted-foreground" />
                <p className="text-muted-foreground">No cash flow data available</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
