import { z } from 'zod';
import { Tables, TablesInsert, TablesUpdate } from './database';

// Type aliases for database tables
export type RecurringTransaction = Tables<'recurring_transactions'>;
export type RecurringTransactionInsert = TablesInsert<'recurring_transactions'>;
export type RecurringTransactionUpdate = TablesUpdate<'recurring_transactions'>;

export type RecurringTransactionItem = Tables<'recurring_transaction_items'>;
export type RecurringTransactionItemInsert = TablesInsert<'recurring_transaction_items'>;
export type RecurringTransactionItemUpdate = TablesUpdate<'recurring_transaction_items'>;

// Frequency type
export type RecurrenceFrequency = 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';

// Status type
export type RecurringTransactionStatus = 'active' | 'paused' | 'completed' | 'cancelled';

// Extended type with items
export interface RecurringTransactionWithItems extends RecurringTransaction {
  items: (RecurringTransactionItem & {
    account?: {
      id: string;
      name: string;
      code: string;
    }
  })[];
}

// Form schema for creating/editing recurring transactions
export const recurringTransactionSchema = z.object({
  // Basic information
  name: z.string().min(3, {
    message: "Name must be at least 3 characters",
  }).max(100, {
    message: "Name must be less than 100 characters",
  }),
  
  description: z.string().max(500, {
    message: "Description must be less than 500 characters",
  }).optional(),
  
  // Schedule information
  frequency: z.enum(['daily', 'weekly', 'monthly', 'quarterly', 'yearly'], {
    required_error: "Frequency is required",
  }),
  
  startDate: z.date({
    required_error: "Start date is required",
  }),
  
  endDate: z.date().optional(),
  
  // Optional day specifications for monthly/weekly recurrence
  dayOfMonth: z.number().min(1).max(31).optional(),
  dayOfWeek: z.number().min(0).max(6).optional(),
  
  // Transaction details
  debitAccount: z.string({
    required_error: "Debit account is required",
  }),
  
  creditAccount: z.string({
    required_error: "Credit account is required",
  }),
  
  amount: z.coerce.number().positive({
    message: "Amount must be greater than 0",
  }).lt(**********, {
    message: "Amount must be less than UGX 1B",
  }),
  
  // Optional reference
  reference: z.string().max(50, {
    message: "Reference must be less than 50 characters",
  }).optional(),
  
  // Optional currency code (defaults to UGX)
  currency: z.string().length(3).default('UGX'),
}).refine(
  (data) => data.debitAccount !== data.creditAccount,
  {
    message: "Debit and credit accounts cannot be the same",
    path: ["creditAccount"],
  }
).refine(
  (data) => {
    // If frequency is monthly, dayOfMonth should be provided
    if (data.frequency === 'monthly' && !data.dayOfMonth) {
      return false;
    }
    // If frequency is weekly, dayOfWeek should be provided
    if (data.frequency === 'weekly' && !data.dayOfWeek) {
      return false;
    }
    return true;
  },
  {
    message: "Day of month is required for monthly frequency, day of week is required for weekly frequency",
    path: ["frequency"],
  }
);

export type RecurringTransactionFormValues = z.infer<typeof recurringTransactionSchema>;

// Helper function to convert form values to database insert format
export function recurringTransactionFormToInsert(
  values: RecurringTransactionFormValues,
  companyId: string,
  userId: string
): { transaction: RecurringTransactionInsert, items: RecurringTransactionItemInsert[] } {
  // Calculate next due date based on start date and frequency
  const nextDueDate = values.startDate;
  
  // Create the recurring transaction record
  const transaction: RecurringTransactionInsert = {
    company_id: companyId,
    created_by: userId,
    name: values.name,
    description: values.description || null,
    frequency: values.frequency,
    start_date: values.startDate.toISOString().split('T')[0],
    end_date: values.endDate ? values.endDate.toISOString().split('T')[0] : null,
    next_due_date: nextDueDate.toISOString().split('T')[0],
    day_of_month: values.dayOfMonth || null,
    day_of_week: values.dayOfWeek || null,
    status: 'active',
  };
  
  // Create transaction items (debit and credit entries)
  const items: RecurringTransactionItemInsert[] = [
    {
      // Debit entry
      account_id: values.debitAccount,
      description: values.description || null,
      debit: values.amount,
      credit: 0,
    },
    {
      // Credit entry
      account_id: values.creditAccount,
      description: values.description || null,
      debit: 0,
      credit: values.amount,
    }
  ];
  
  return { transaction, items };
}
