import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Loader2, 
  AlertCircle, 
  CheckCircle, 
  RefreshCw, 
  Trash2, 
  Eye,
  Settings
} from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useProfileManagement, ProfileOperationCallbacks } from '@/hooks/useProfileManagement';
import { Profile } from '@/types/index';

/**
 * Comprehensive demo component showcasing all enhanced profile management features
 */
const ProfileManagementDemo: React.FC = () => {
  const { user } = useAuth();
  const [selectedFields, setSelectedFields] = useState<(keyof Profile)[]>(['first_name', 'last_name']);
  const [partialProfile, setPartialProfile] = useState<Partial<Profile> | null>(null);
  const [operationLog, setOperationLog] = useState<string[]>([]);

  // Initialize with custom retry configuration
  const {
    fetchUserProfile,
    fetchProfileFields,
    updateProfile,
    deleteProfile,
    loadingStates,
    lastError,
    optimisticUpdates,
    clearCache,
    getCachedProfile,
    retryLastOperation,
    cleanup
  } = useProfileManagement({
    maxRetries: 5,
    retryDelay: 500,
    exponentialBackoff: true
  });

  const addToLog = (message: string) => {
    setOperationLog(prev => [`${new Date().toLocaleTimeString()}: ${message}`, ...prev.slice(0, 9)]);
  };

  const callbacks: ProfileOperationCallbacks = {
    onStart: () => addToLog('Operation started'),
    onSuccess: (data) => addToLog(`Operation succeeded: ${JSON.stringify(data).slice(0, 50)}...`),
    onError: (error) => addToLog(`Operation failed: ${error.message}`),
    onComplete: () => addToLog('Operation completed')
  };

  const handleFetchProfile = async () => {
    if (!user?.id) return;
    const profile = await fetchUserProfile(user.id, callbacks);
    addToLog(`Fetched profile: ${profile ? 'Success' : 'Failed'}`);
  };

  const handleFetchPartialProfile = async () => {
    if (!user?.id || selectedFields.length === 0) return;
    const partial = await fetchProfileFields(user.id, selectedFields, callbacks);
    setPartialProfile(partial);
    addToLog(`Fetched ${selectedFields.length} fields: ${partial ? 'Success' : 'Failed'}`);
  };

  const handleOptimisticUpdate = async () => {
    if (!user?.id) return;
    const updates = {
      first_name: 'Optimistic',
      last_name: 'Update'
    };
    
    await updateProfile(user.id, updates, {
      optimistic: true,
      callbacks
    });
  };

  const handleSoftDelete = async () => {
    if (!user?.id) return;
    await deleteProfile(user.id, {
      hard: false,
      callbacks
    });
  };

  const handleClearCache = () => {
    clearCache();
    addToLog('Cache cleared');
  };

  const handleCheckCache = () => {
    if (!user?.id) return;
    const cached = getCachedProfile(user.id);
    addToLog(`Cache check: ${cached ? 'Found' : 'Not found'}`);
  };

  const handleRetry = async () => {
    const success = await retryLastOperation();
    addToLog(`Retry: ${success ? 'Success' : 'Failed'}`);
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Enhanced Profile Management Demo
          </CardTitle>
          <CardDescription>
            Comprehensive demonstration of all profile management features including 
            caching, optimistic updates, granular loading states, and error handling.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Status Dashboard */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Status Dashboard</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Loading States */}
          <div>
            <Label className="text-sm font-medium">Loading States:</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {Object.entries(loadingStates).map(([operation, loading]) => (
                <Badge 
                  key={operation} 
                  variant={loading ? "default" : "outline"}
                  className="flex items-center gap-1"
                >
                  {loading && <Loader2 className="h-3 w-3 animate-spin" />}
                  {operation}: {loading ? 'Loading' : 'Idle'}
                </Badge>
              ))}
            </div>
          </div>

          {/* Optimistic Updates */}
          <div>
            <Label className="text-sm font-medium">Optimistic Updates:</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {optimisticUpdates.length === 0 ? (
                <Badge variant="outline">None</Badge>
              ) : (
                optimisticUpdates.map((update) => (
                  <Badge key={update.id} variant="secondary" className="flex items-center gap-1">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    {update.operation} pending
                  </Badge>
                ))
              )}
            </div>
          </div>

          {/* Last Error */}
          <div>
            <Label className="text-sm font-medium">Last Error:</Label>
            <div className="mt-2">
              {lastError ? (
                <Badge variant="destructive" className="flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {lastError.operation}: {lastError.error.message}
                </Badge>
              ) : (
                <Badge variant="outline" className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3" />
                  No errors
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Operations Panel */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Core Operations */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Core Operations</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button 
              onClick={handleFetchProfile}
              disabled={loadingStates.fetch}
              className="w-full flex items-center gap-2"
            >
              {loadingStates.fetch && <Loader2 className="h-4 w-4 animate-spin" />}
              Fetch Full Profile
            </Button>

            <Button 
              onClick={handleOptimisticUpdate}
              disabled={loadingStates.update}
              variant="outline"
              className="w-full flex items-center gap-2"
            >
              {loadingStates.update && <Loader2 className="h-4 w-4 animate-spin" />}
              Optimistic Update
            </Button>

            <Button 
              onClick={handleSoftDelete}
              disabled={loadingStates.delete}
              variant="destructive"
              className="w-full flex items-center gap-2"
            >
              {loadingStates.delete && <Loader2 className="h-4 w-4 animate-spin" />}
              <Trash2 className="h-4 w-4" />
              Soft Delete
            </Button>
          </CardContent>
        </Card>

        {/* Partial Fetch Demo */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Partial Profile Fetch</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="text-sm">Select Fields:</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {(['first_name', 'last_name', 'phone', 'avatar_url'] as (keyof Profile)[]).map((field) => (
                  <Badge
                    key={field}
                    variant={selectedFields.includes(field) ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => {
                      setSelectedFields(prev => 
                        prev.includes(field) 
                          ? prev.filter(f => f !== field)
                          : [...prev, field]
                      );
                    }}
                  >
                    {field}
                  </Badge>
                ))}
              </div>
            </div>

            <Button 
              onClick={handleFetchPartialProfile}
              disabled={loadingStates.fetch || selectedFields.length === 0}
              className="w-full flex items-center gap-2"
            >
              {loadingStates.fetch && <Loader2 className="h-4 w-4 animate-spin" />}
              <Eye className="h-4 w-4" />
              Fetch Selected Fields
            </Button>

            {partialProfile && (
              <div className="text-xs bg-muted p-2 rounded">
                <pre>{JSON.stringify(partialProfile, null, 2)}</pre>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Cache & Utility Operations */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Cache & Utilities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button onClick={handleCheckCache} variant="outline" size="sm">
              Check Cache
            </Button>
            <Button onClick={handleClearCache} variant="outline" size="sm">
              Clear Cache
            </Button>
            <Button 
              onClick={handleRetry} 
              disabled={!lastError}
              variant="outline" 
              size="sm"
              className="flex items-center gap-1"
            >
              <RefreshCw className="h-3 w-3" />
              Retry Last Operation
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Operation Log */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Operation Log</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-1 max-h-40 overflow-y-auto">
            {operationLog.length === 0 ? (
              <p className="text-sm text-muted-foreground">No operations yet</p>
            ) : (
              operationLog.map((log, index) => (
                <div key={index} className="text-xs font-mono bg-muted p-1 rounded">
                  {log}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileManagementDemo;
