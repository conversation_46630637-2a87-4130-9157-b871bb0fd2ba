import { useState } from "react";
import { format } from "date-fns";
import {
  Mail,
  Trash2,
  Printer,
  Download,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Send
} from "lucide-react";

import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { useEnhancedInvoices } from "@/hooks/use-enhanced-invoices";
import { useAuth } from "@/context/AuthContext";
import { getAvailableTransitions } from "@/services/invoice-workflow-service";
import { InvoiceStatusHistory } from "./invoice-status-history";
import { RecordPaymentDialog } from "./record-payment-dialog";

interface EnhancedInvoiceDetailProps {
  invoiceId: string;
  onClose: () => void;
}

/**
 * Enhanced invoice detail component with workflow features
 */
export function EnhancedInvoiceDetail({ invoiceId, onClose }: EnhancedInvoiceDetailProps): JSX.Element {
  const [activeTab, setActiveTab] = useState("details");
  const [showDeleteAlert, setShowDeleteAlert] = useState(false);

  const { roles } = useAuth();
  const userRole = roles?.[0] || 'viewer';

  const {
    selectedInvoice,
    isLoadingInvoice,
    updateStatus,
    sendEmail,
    recordPayment,
    deleteInvoice,
    isUpdatingStatus,
    isSendingEmail,
    isRecordingPayment,
    isDeletingInvoice,
    setSelectedInvoiceId,
  } = useEnhancedInvoices();

  // Set the selected invoice ID when the component mounts
  useState(() => {
    setSelectedInvoiceId(invoiceId);
  });

  // Handle invoice deletion
  const handleDeleteInvoice = (): void => {
    deleteInvoice(invoiceId, {
      onSuccess: () => {
        onClose();
      },
    });
  };

  // Format currency as UGX
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Get status icon
  const getStatusIcon = (status: string): JSX.Element | null => {
    switch (status) {
      case 'draft':
        return <Clock className="h-4 w-4" />;
      case 'sent':
        return <Send className="h-4 w-4" />;
      case 'paid':
        return <CheckCircle className="h-4 w-4" />;
      case 'overdue':
        return <AlertTriangle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      default:
        return null;
    }
  };

  // Get status badge variant
  const getStatusVariant = (status: string): "outline" | "secondary" | "success" | "warning" | "destructive" => {
    switch (status) {
      case 'draft':
        return 'outline';
      case 'sent':
        return 'secondary';
      case 'paid':
        return 'success';
      case 'overdue':
        return 'warning';
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Loading state
  if (isLoadingInvoice || !selectedInvoice) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Invoice Details</CardTitle>
          <CardDescription>Loading invoice information...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get available status transitions
  const availableTransitions = getAvailableTransitions(selectedInvoice.status, userRole);

  // Calculate payment information
  const totalPaid = selectedInvoice.invoice_payments
    ? selectedInvoice.invoice_payments.reduce((sum, payment) => sum + payment.amount, 0)
    : 0;
  const remainingAmount = selectedInvoice.total_amount - totalPaid;
  const isPaid = remainingAmount <= 0;

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-xl">
            Invoice #{selectedInvoice.invoice_number}
          </CardTitle>
          <CardDescription>
            {selectedInvoice.customers?.name}
          </CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={getStatusVariant(selectedInvoice.status)} className="flex items-center gap-1">
            {getStatusIcon(selectedInvoice.status)}
            {selectedInvoice.status.charAt(0).toUpperCase() + selectedInvoice.status.slice(1)}
          </Badge>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <XCircle className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <div className="px-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="payments">Payments</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="details" className="p-0">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Customer Information</h3>
                <div className="space-y-1">
                  <p className="font-medium">{selectedInvoice.customers?.name}</p>
                  {selectedInvoice.customers?.email && (
                    <p className="text-sm">{selectedInvoice.customers.email}</p>
                  )}
                  {selectedInvoice.customers?.phone && (
                    <p className="text-sm">{selectedInvoice.customers.phone}</p>
                  )}
                  {selectedInvoice.customers?.address && (
                    <p className="text-sm">{selectedInvoice.customers.address}</p>
                  )}
                  {(selectedInvoice.customers?.city || selectedInvoice.customers?.country) && (
                    <p className="text-sm">
                      {selectedInvoice.customers.city}{selectedInvoice.customers.city && selectedInvoice.customers.country ? ', ' : ''}
                      {selectedInvoice.customers.country}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Invoice Information</h3>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span className="text-sm">Issue Date:</span>
                    <span className="text-sm font-medium">
                      {format(new Date(selectedInvoice.issue_date), 'MMM d, yyyy')}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Due Date:</span>
                    <span className="text-sm font-medium">
                      {format(new Date(selectedInvoice.due_date), 'MMM d, yyyy')}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Subtotal:</span>
                    <span className="text-sm font-medium">
                      {formatCurrency(selectedInvoice.amount)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Tax:</span>
                    <span className="text-sm font-medium">
                      {formatCurrency(selectedInvoice.tax_amount)}
                    </span>
                  </div>
                  <Separator className="my-2" />
                  <div className="flex justify-between">
                    <span className="font-medium">Total:</span>
                    <span className="font-medium">
                      {formatCurrency(selectedInvoice.total_amount)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Amount Paid:</span>
                    <span className="text-sm font-medium text-green-600">
                      {formatCurrency(totalPaid)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Balance Due:</span>
                    <span className={`text-sm font-medium ${isPaid ? 'text-green-600' : 'text-amber-600'}`}>
                      {formatCurrency(remainingAmount)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6">
              <h3 className="text-sm font-medium text-muted-foreground mb-2">Items</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Quantity</TableHead>
                    <TableHead className="text-right">Unit Price</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {selectedInvoice.invoice_items?.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.description}</TableCell>
                      <TableCell className="text-right">{item.quantity}</TableCell>
                      <TableCell className="text-right">{formatCurrency(item.unit_price)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(item.amount)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {selectedInvoice.notes && (
              <div className="mt-6">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Notes</h3>
                <p className="text-sm bg-muted p-3 rounded-md">{selectedInvoice.notes}</p>
              </div>
            )}
          </CardContent>
        </TabsContent>

        <TabsContent value="payments" className="p-0">
          <CardContent className="pt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Payment History</h3>
              <RecordPaymentDialog
                invoice={selectedInvoice}
                onRecordPayment={recordPayment}
                isSubmitting={isRecordingPayment}
              />
            </div>

            {selectedInvoice.invoice_payments && selectedInvoice.invoice_payments.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {selectedInvoice.invoice_payments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell>{format(new Date(payment.payment_date), 'MMM d, yyyy')}</TableCell>
                      <TableCell className="capitalize">{payment.payment_method.replace('_', ' ')}</TableCell>
                      <TableCell>{payment.reference || '-'}</TableCell>
                      <TableCell className="text-right">{formatCurrency(payment.amount)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <p>No payments recorded yet</p>
              </div>
            )}

            <div className="mt-6 bg-muted p-4 rounded-md">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-muted-foreground">Total Invoice Amount</p>
                  <p className="text-lg font-medium">{formatCurrency(selectedInvoice.total_amount)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Amount Paid</p>
                  <p className="text-lg font-medium text-green-600">{formatCurrency(totalPaid)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Balance Due</p>
                  <p className={`text-lg font-medium ${isPaid ? 'text-green-600' : 'text-amber-600'}`}>
                    {formatCurrency(remainingAmount)}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </TabsContent>

        <TabsContent value="history" className="p-0">
          <CardContent className="pt-6">
            <InvoiceStatusHistory invoice={selectedInvoice} />
          </CardContent>
        </TabsContent>
      </Tabs>

      <CardFooter className="flex justify-between border-t pt-6">
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="gap-2">
            <Printer className="h-4 w-4" />
            Print
          </Button>
          <Button variant="outline" size="sm" className="gap-2">
            <Download className="h-4 w-4" />
            Download PDF
          </Button>
        </div>

        <div className="flex gap-2">
          {selectedInvoice.status === 'draft' && (
            <Button
              size="sm"
              className="gap-2"
              onClick={() => sendEmail(invoiceId)}
              disabled={isSendingEmail}
            >
              <Mail className="h-4 w-4" />
              {isSendingEmail ? 'Sending...' : 'Send Invoice'}
            </Button>
          )}

          {availableTransitions.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  Change Status
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Status Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {availableTransitions.map((transition) => (
                  <DropdownMenuItem
                    key={transition.to}
                    onClick={() => updateStatus({ invoiceId, status: transition.to })}
                    disabled={isUpdatingStatus}
                    className="gap-2"
                  >
                    {getStatusIcon(transition.to)}
                    {transition.label}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          <AlertDialog open={showDeleteAlert} onOpenChange={setShowDeleteAlert}>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" size="sm" className="gap-2">
                <Trash2 className="h-4 w-4" />
                Delete
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete the invoice
                  and all associated data.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDeleteInvoice}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  disabled={isDeletingInvoice}
                >
                  {isDeletingInvoice ? 'Deleting...' : 'Delete'}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </CardFooter>
    </Card>
  );
}
