import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { TransactionApprovalDashboard } from '@/components/general-ledger/TransactionApprovalDashboard';
import { ApprovalThresholdsManager } from '@/components/general-ledger/ApprovalThresholdsManager';

const TransactionApprovals: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="page-title mb-1">Transaction Approvals</h1>
          <p className="text-muted-foreground">
            Manage transaction approvals and approval thresholds
          </p>
        </div>
      </div>

      <Card>
        <CardContent className="pt-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="dashboard">Approval Dashboard</TabsTrigger>
              <TabsTrigger value="thresholds">Approval Thresholds</TabsTrigger>
            </TabsList>
            <TabsContent value="dashboard">
              <TransactionApprovalDashboard />
            </TabsContent>
            <TabsContent value="thresholds">
              <ApprovalThresholdsManager />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default TransactionApprovals;
