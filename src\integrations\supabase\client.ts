import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

// Custom fetch implementation with error handling and retries
const customFetch = async (url: string, options: RequestInit) => {
  const MAX_RETRIES = 3;
  const RETRY_DELAY = 1000; // 1 second

  for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    try {
      const response = await fetch(url, options);
      
      // Handle specific status codes
      if (response.status === 406) {
        console.warn('Received 406 status, this might be expected for new users');
        return response;
      }
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response;
    } catch (error) {
      console.error(`Network request failed (attempt ${attempt}/${MAX_RETRIES}):`, error);
      
      if (attempt === MAX_RETRIES) {
        throw error;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * attempt));
    }
  }
  
  throw new Error('Max retries exceeded');
};

// Custom storage implementation with error handling
const customStorage = {
  getItem: (key: string): string | null => {
    try {
      return localStorage.getItem(key);
    } catch (error) {
      console.error('Error reading from storage:', error);
      return null;
    }
  },
  setItem: (key: string, value: string): void => {
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      console.error('Error writing to storage:', error);
    }
  },
  removeItem: (key: string): void => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Error removing from storage:', error);
    }
  }
};

// Get environment variables
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL;
const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  throw new Error('Missing Supabase environment variables');
}

// Create Supabase client with custom configuration
export const supabase = createClient<Database>(
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  {
    global: {
      fetch: customFetch,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    },
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
      storageKey: 'kaya_finance_auth',
      storage: customStorage,
      flowType: 'pkce',
      debug: process.env.NODE_ENV === 'development'
    },
    db: {
      schema: 'public'
    },
    realtime: {
      params: {
        eventsPerSecond: 10
      }
    }
  }
);

// Export a function to check if the session is valid
export const isSessionValid = async (): Promise<boolean> => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) throw error;
    return !!session;
  } catch (error) {
    console.error('Error checking session:', error);
    return false;
  }
};

// Export a function to refresh the session
export const refreshSession = async (): Promise<boolean> => {
  try {
    const { data: { session }, error } = await supabase.auth.refreshSession();
    if (error) throw error;
    return !!session;
  } catch (error) {
    console.error('Error refreshing session:', error);
    return false;
  }
};

// Helper function to create a function that automatically handles errors
export const createSupabaseQuery = <T, P extends any[]>(
  queryFn: (supabase: SupabaseClient<Database>, ...params: P) => Promise<{ data: T | null; error: any }>
) => {
  return async (...params: P): Promise<T | null> => {
    try {
      const { data, error } = await queryFn(supabase, ...params);
      if (error) {
        console.error('Supabase query error:', error);
        throw error;
      }
      return data;
    } catch (err) {
      console.error('Unexpected error in Supabase query:', err);
      throw err;
    }
  };
};
