import React, { Component, ErrorInfo, ReactNode } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>riangle, RefreshCw, Home } from 'lucide-react';

interface ErrorBoundaryProps {
  /**
   * The children to render
   */
  children: ReactNode;

  /**
   * Custom fallback component to render when an error occurs
   */
  fallback?: ReactNode;

  /**
   * Whether to log errors to the console
   * @default true
   */
  logErrors?: boolean;

  /**
   * Whether to show the error details in the UI
   * @default false in production, true in development
   */
  showDetails?: boolean;

  /**
   * Callback function to run when an error occurs
   */
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * A component that catches JavaScript errors anywhere in its child component tree,
 * logs those errors, and displays a fallback UI instead of the component tree that crashed.
 */
class ErrorBoundaryClass extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Catch errors in any components below and re-render with error message
    this.setState({
      error,
      errorInfo,
    });

    // Log error to console if enabled
    if (this.props.logErrors !== false) {
      console.error('Error caught by ErrorBoundary:', error, errorInfo);
    }

    // Call onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  render(): ReactNode {
    const { hasError, error, errorInfo } = this.state;
    const { children, fallback, showDetails } = this.props;

    // If there's no error, render children normally
    if (!hasError) {
      return children;
    }

    // If a custom fallback is provided, render it
    if (fallback) {
      return fallback;
    }

    // Otherwise, render the default error UI
    return (
      <DefaultErrorFallback
        error={error}
        errorInfo={errorInfo}
        showDetails={showDetails}
        resetError={() => this.setState({ hasError: false, error: null, errorInfo: null })}
      />
    );
  }
}

interface DefaultErrorFallbackProps {
  /**
   * The error that occurred
   */
  error: Error | null;

  /**
   * Additional error information
   */
  errorInfo: ErrorInfo | null;

  /**
   * Whether to show detailed error information
   */
  showDetails?: boolean;

  /**
   * Function to reset the error state
   */
  resetError: () => void;
}

/**
 * Default fallback UI for the ErrorBoundary
 */
function DefaultErrorFallback({
  error,
  errorInfo,
  showDetails = process.env.NODE_ENV !== 'production',
  resetError,
}: DefaultErrorFallbackProps): ReactNode {
  const handleGoHome = (): void => {
    resetError();
    // Always use window.location for navigation in error boundary
    // to avoid potential issues with React Router context
    window.location.href = '/';
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-muted/20">
      <Card className="w-full max-w-2xl shadow-lg">
        <CardHeader className="bg-destructive/10 border-b">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-6 w-6 text-destructive" />
            <CardTitle>Something went wrong</CardTitle>
          </div>
          <CardDescription>
            An unexpected error has occurred. Our team has been notified.
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <p className="text-muted-foreground">
              We apologize for the inconvenience. You can try refreshing the page or going back to the dashboard.
            </p>

            {showDetails && error && (
              <div className="mt-4 space-y-2">
                <h3 className="text-sm font-medium">Error Details:</h3>
                <div className="bg-muted p-3 rounded-md overflow-auto text-sm">
                  <p className="font-mono">{error.toString()}</p>

                  {errorInfo && (
                    <details className="mt-2">
                      <summary className="cursor-pointer text-xs text-muted-foreground">
                        Component Stack
                      </summary>
                      <pre className="mt-2 text-xs overflow-auto">
                        {errorInfo.componentStack}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between gap-2 border-t pt-4">
          <Button variant="outline" onClick={resetError} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Try Again
          </Button>
          <Button onClick={handleGoHome} className="flex items-center gap-2">
            <Home className="h-4 w-4" />
            Go to Dashboard
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

/**
 * A wrapper component that provides navigation context to the ErrorBoundary
 */
export function ErrorBoundary(props: ErrorBoundaryProps): ReactNode {
  return <ErrorBoundaryClass {...props} />;
}
