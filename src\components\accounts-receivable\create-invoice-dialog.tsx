
import { useState } from "react";
import { useAuth } from "@/context/AuthContext";
import { useInvoices } from "@/hooks/use-invoices";
import { InvoiceFormValues } from "@/validations/invoice-schema";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CreditCard } from "lucide-react";
import { InvoiceForm } from "./invoice-form";

export function CreateInvoiceDialog() {
  const [open, setOpen] = useState(false);
  const { user } = useAuth();
  const { createInvoice, isCreatingInvoice } = useInvoices();
  
  const handleSubmit = (data: InvoiceFormValues, customerId: string) => {
    createInvoice(
      { formData: data, customerId },
      {
        onSuccess: () => {
          setOpen(false);
        },
      }
    );
  };

  if (!user) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <CreditCard className="mr-2 h-4 w-4" />
          Create Invoice
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Invoice</DialogTitle>
          <DialogDescription>
            Fill in the details to create a new invoice. All fields marked with an asterisk (*) are required.
          </DialogDescription>
        </DialogHeader>
        <InvoiceForm 
          onSubmit={handleSubmit} 
          isSubmitting={isCreatingInvoice} 
        />
      </DialogContent>
    </Dialog>
  );
};
