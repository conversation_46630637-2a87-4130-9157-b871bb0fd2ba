
import { Card, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import {
  ChartContainer,
} from "@/components/ui/chart";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
} from "recharts";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

const monthlyData = [
  {
    name: "Jan",
    income: 4000,
    expenses: 2400,
  },
  {
    name: "Feb",
    income: 3000,
    expenses: 1398,
  },
  {
    name: "Mar",
    income: 2000,
    expenses: 3800,
  },
  {
    name: "Apr",
    income: 2780,
    expenses: 3908,
  },
  {
    name: "May",
    income: 1890,
    expenses: 4800,
  },
  {
    name: "Jun",
    income: 2390,
    expenses: 3800,
  },
];



const chartConfig = {
  income: {
    color: "#10B981",
  },
  expenses: {
    color: "#EF4444",
  },
};

export const CashFlowChart = (): JSX.Element => {
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat("en-UG", {
      style: "currency",
      currency: "UGX",
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle className="flex justify-between">
          <span>Cash Flow</span>
          <Tabs defaultValue="monthly" className="w-[250px]">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="weekly">Weekly</TabsTrigger>
              <TabsTrigger value="monthly">Monthly</TabsTrigger>
            </TabsList>
            <TabsContent value="weekly"></TabsContent>
            <TabsContent value="monthly"></TabsContent>
          </Tabs>
        </CardTitle>
      </CardHeader>
      <CardContent className="h-80">
        <ChartContainer config={chartConfig} className="h-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={monthlyData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
              <XAxis dataKey="name" />
              <YAxis tickFormatter={(value) => `${value / 1000}K`} />
              <Tooltip
                content={({ active, payload }) => {
                  if (active && payload && payload.length) {
                    return (
                      <div className="rounded-lg border bg-background p-2 shadow-sm">
                        <div className="font-semibold">{payload[0].payload.name}</div>
                        <div className="grid grid-cols-2 gap-2">
                          <div className="flex items-center">
                            <div className="mr-1 h-2 w-2 rounded-full bg-[#10B981]" />
                            <span className="text-sm font-medium text-muted-foreground">
                              Income:
                            </span>
                          </div>
                          <div className="text-sm">
                            {formatCurrency(Number(payload[0].value))}
                          </div>
                          <div className="flex items-center">
                            <div className="mr-1 h-2 w-2 rounded-full bg-[#EF4444]" />
                            <span className="text-sm font-medium text-muted-foreground">
                              Expenses:
                            </span>
                          </div>
                          <div className="text-sm">
                            {formatCurrency(Number(payload[1].value))}
                          </div>
                        </div>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              <Bar dataKey="income" fill="#10B981" radius={[4, 4, 0, 0]} />
              <Bar dataKey="expenses" fill="#EF4444" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};
