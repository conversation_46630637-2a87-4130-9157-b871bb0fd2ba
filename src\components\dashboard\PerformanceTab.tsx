
import { StatCard } from "@/components/dashboard/StatCard";
import { TrendingUp, BarChart3, Activity, DollarSign, ArrowUp } from "lucide-react";

export const PerformanceTab = () => {
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 my-8">
        <StatCard
          title="Growth Rate"
          value="18.5%"
          description="Up 3.2% from previous quarter"
          icon={TrendingUp}
          trend="up"
          progress={78}
        />
        <StatCard
          title="Average Sale"
          value="UGX 2,345,000"
          description="11.3% higher than last quarter"
          icon={BarChart3}
          trend="up"
          progress={72}
        />
        <StatCard
          title="Client Retention"
          value="92%"
          description="Improved from 86% last year"
          icon={Activity}
          trend="up"
          progress={92}
        />
        <StatCard
          title="Operating Margin"
          value="24.6%"
          description="Target: 25%"
          icon={DollarSign}
          trend="neutral"
          progress={98}
        />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <QuarterlyMetrics />
        <YearOverYearComparison />
      </div>
    </>
  );
};

const QuarterlyMetrics = () => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-border">
      <h3 className="text-lg font-semibold mb-4">Quarterly Performance Metrics</h3>
      <div className="space-y-4">
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span>Revenue Growth</span>
            <span className="font-medium">22.4%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-primary h-2 rounded-full" style={{ width: '78%' }}></div>
          </div>
        </div>
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span>Cost Optimization</span>
            <span className="font-medium">15.7%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-primary h-2 rounded-full" style={{ width: '65%' }}></div>
          </div>
        </div>
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span>Market Share</span>
            <span className="font-medium">28.3%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-primary h-2 rounded-full" style={{ width: '82%' }}></div>
          </div>
        </div>
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span>Customer Satisfaction</span>
            <span className="font-medium">94.2%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-primary h-2 rounded-full" style={{ width: '94%' }}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

const YearOverYearComparison = () => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-border">
      <h3 className="text-lg font-semibold mb-4">Year-over-Year Comparison</h3>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-sm text-muted-foreground">Revenue</p>
            <p className="text-2xl font-bold">UGX 245.8M</p>
            <p className="text-xs text-green-600">
              <span className="flex items-center">
                <ArrowUp className="h-3 w-3 mr-1" /> 18.2% from last year
              </span>
            </p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Expenses</p>
            <p className="text-2xl font-bold">UGX 176.4M</p>
            <p className="text-xs text-red-600">
              <span className="flex items-center">
                <ArrowUp className="h-3 w-3 mr-1" /> 12.5% from last year
              </span>
            </p>
          </div>
        </div>
        
        <div className="flex justify-between items-center pt-4 border-t">
          <div>
            <p className="text-sm text-muted-foreground">New Clients</p>
            <p className="text-2xl font-bold">32</p>
            <p className="text-xs text-green-600">
              <span className="flex items-center">
                <ArrowUp className="h-3 w-3 mr-1" /> 8 more than last year
              </span>
            </p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Avg. Deal Size</p>
            <p className="text-2xl font-bold">UGX 4.2M</p>
            <p className="text-xs text-green-600">
              <span className="flex items-center">
                <ArrowUp className="h-3 w-3 mr-1" /> 15.3% increase
              </span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
