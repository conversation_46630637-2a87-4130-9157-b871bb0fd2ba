/**
 * Aging Report Service
 * 
 * This service provides functions for fetching and processing accounts receivable aging data.
 */

import { format } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import { showErrorToast } from '@/utils/toast-utils';

/**
 * Aging bucket type
 */
export type AgingBucket = 'Current' | '1-30 days' | '31-60 days' | '61-90 days' | 'Over 90 days' | 'Paid';

/**
 * Invoice aging data structure
 */
export interface InvoiceAgingData {
  customer_id: string;
  customer_name: string;
  invoice_id: string;
  invoice_number: string;
  issue_date: string;
  due_date: string;
  total_amount: number;
  paid_amount: number;
  balance: number;
  currency: string;
  days_overdue: number;
  aging_bucket: AgingBucket;
}

/**
 * Customer aging summary
 */
export interface CustomerAgingSummary {
  customer_id: string;
  customer_name: string;
  total_balance: number;
  current_amount: number;
  days_1_30_amount: number;
  days_31_60_amount: number;
  days_61_90_amount: number;
  over_90_days_amount: number;
  currency: string;
}

/**
 * Aging report summary data
 */
export interface AgingReportSummary {
  total_receivables: number;
  current_amount: number;
  days_1_30_amount: number;
  days_31_60_amount: number;
  days_61_90_amount: number;
  over_90_days_amount: number;
  currency: string;
  customers: CustomerAgingSummary[];
}

/**
 * Detailed aging report data
 */
export interface AgingReportDetailed {
  summary: AgingReportSummary;
  invoices: InvoiceAgingData[];
}

/**
 * Fetch aging report data for a company
 * 
 * @param companyId Company ID
 * @param asOfDate Optional date to calculate aging as of (defaults to current date)
 * @returns Aging report data or null if there was an error
 */
export const fetchAgingReport = async (
  companyId: string,
  asOfDate: Date = new Date()
): Promise<AgingReportDetailed | null> => {
  try {
    const formattedDate = format(asOfDate, 'yyyy-MM-dd');

    // Call the stored function to get invoice aging data
    const { data: invoices, error } = await supabase
      .rpc('get_invoice_aging', {
        p_company_id: companyId,
        p_as_of_date: formattedDate
      });

    if (error) throw error;
    if (!invoices) return null;

    // Process the data to create the summary
    const typedInvoices = invoices as InvoiceAgingData[];
    const summary = calculateAgingSummary(typedInvoices);

    return {
      summary,
      invoices: typedInvoices
    };
  } catch (error: any) {
    console.error('Error fetching aging report:', error);
    showErrorToast(
      'Failed to Load Aging Report',
      error.message || 'An error occurred while loading the aging report'
    );
    return null;
  }
};

/**
 * Calculate aging summary from invoice data
 * 
 * @param invoices List of invoice aging data
 * @returns Aging report summary
 */
export const calculateAgingSummary = (
  invoices: InvoiceAgingData[]
): AgingReportSummary => {
  // Default to UGX currency
  const currency = invoices.length > 0 ? invoices[0].currency : 'UGX';
  
  // Initialize summary
  const summary: AgingReportSummary = {
    total_receivables: 0,
    current_amount: 0,
    days_1_30_amount: 0,
    days_31_60_amount: 0,
    days_61_90_amount: 0,
    over_90_days_amount: 0,
    currency,
    customers: []
  };

  // Customer map to aggregate by customer
  const customerMap = new Map<string, CustomerAgingSummary>();

  // Process each invoice
  invoices.forEach(invoice => {
    // Update total receivables
    summary.total_receivables += invoice.balance;

    // Update aging buckets
    switch (invoice.aging_bucket) {
      case 'Current':
        summary.current_amount += invoice.balance;
        break;
      case '1-30 days':
        summary.days_1_30_amount += invoice.balance;
        break;
      case '31-60 days':
        summary.days_31_60_amount += invoice.balance;
        break;
      case '61-90 days':
        summary.days_61_90_amount += invoice.balance;
        break;
      case 'Over 90 days':
        summary.over_90_days_amount += invoice.balance;
        break;
    }

    // Update customer summary
    if (!customerMap.has(invoice.customer_id)) {
      customerMap.set(invoice.customer_id, {
        customer_id: invoice.customer_id,
        customer_name: invoice.customer_name,
        total_balance: 0,
        current_amount: 0,
        days_1_30_amount: 0,
        days_31_60_amount: 0,
        days_61_90_amount: 0,
        over_90_days_amount: 0,
        currency: invoice.currency
      });
    }

    const customerSummary = customerMap.get(invoice.customer_id)!;
    customerSummary.total_balance += invoice.balance;

    // Update customer aging buckets
    switch (invoice.aging_bucket) {
      case 'Current':
        customerSummary.current_amount += invoice.balance;
        break;
      case '1-30 days':
        customerSummary.days_1_30_amount += invoice.balance;
        break;
      case '31-60 days':
        customerSummary.days_31_60_amount += invoice.balance;
        break;
      case '61-90 days':
        customerSummary.days_61_90_amount += invoice.balance;
        break;
      case 'Over 90 days':
        customerSummary.over_90_days_amount += invoice.balance;
        break;
    }
  });

  // Convert customer map to array
  summary.customers = Array.from(customerMap.values());

  return summary;
};
