# Zod Schema Audit

This document lists all Zod validation schemas in the Kaya Finance project that need to be updated to align with the Supabase-generated types from `src/types/database.ts`.

## Validation Schemas

### 1. `src/validations/company-schema.ts`

**Current Status**: Uses custom schema definition
**Required Changes**:
- Align with `Companies` table schema from database.ts
- Use proper field types and constraints
- Add validation for fiscal_year_start, base_currency, etc.

```typescript
// Current schema
export const companySchema = z.object({
  name: z.string().min(2, { message: 'Company name must be at least 2 characters' }),
  tax_id: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  country: z.string().min(1, { message: 'Country is required' }).default('Uganda'),
  postal_code: z.string().optional(),
  phone: z.string().optional(),
  email: z.string()
    .email({ message: 'Invalid email format' })
    .optional()
    .or(z.string().length(0)),
  website: z.string()
    .url({ message: 'Invalid URL format' })
    .optional()
    .or(z.string().length(0)),
  logo_url: z.string().optional(),
});
```

### 2. `src/validations/invoice-schema.ts`

**Current Status**: Uses custom schema definition with hardcoded values
**Required Changes**:
- Align with `Invoices` table schema from database.ts
- Use `Enums<'invoice_status'>` for status values
- Ensure validation rules match database constraints

```typescript
// Current schema (partial)
export const invoiceSchema = z.object({
  // Basic invoice information
  invoice_number: z.string()
    .min(1, { message: "Invoice number is required" })
    .max(50, { message: "Invoice number must be less than 50 characters" })
    .regex(/^[A-Za-z0-9\-/]+$/, {
      message: "Invoice number can only contain letters, numbers, hyphens, and forward slashes"
    }),
  // ...more fields
  status: z.enum(["draft", "sent", "paid", "overdue", "cancelled"], {
    required_error: "Status is required",
    invalid_type_error: "Invalid invoice status"
  }),
  // ...more fields
});
```

### 3. `src/validations/invoiceSchema.ts`

**Current Status**: Duplicate of invoice-schema.ts with simplified fields
**Required Changes**:
- Consolidate with invoice-schema.ts
- Use the same Supabase-aligned types

```typescript
// Current schema
export const invoiceSchema = z.object({
  customer: z.string().min(1, {
    message: "Customer name is required",
  }),
  amount: z.number().min(0, {
    message: "Amount must be a positive number",
  }),
  // ...more fields
});
```

### 4. `src/validations/transaction-schema.ts`

**Current Status**: Complex schema with multiple sub-schemas
**Required Changes**:
- Align with `Transactions` and `TransactionItems` tables from database.ts
- Use proper enum types from database
- Ensure validation rules match database constraints

```typescript
// Current schema (partial)
export const transactionSchema = z.object({
  // Basic transaction information
  transaction_date: z.date({
    required_error: "Transaction date is required",
    invalid_type_error: "Invalid date format"
  }),
  // ...more fields
  status: z.enum(transactionStatuses, {
    required_error: "Status is required",
    invalid_type_error: "Invalid status"
  }).default('draft'),
  // ...more fields
});
```

### 5. `src/components/general-ledger/types.ts`

**Current Status**: Contains a simplified transaction schema
**Required Changes**:
- Align with the standardized transaction schema
- Import and reuse types from central location

```typescript
// Current schema
export const transactionSchema = z.object({
  date: z.date({
    required_error: "Transaction date is required",
  }),
  description: z.string().min(5, {
    message: "Description must be at least 5 characters",
  }),
  debitAccount: z.string({
    required_error: "Debit account is required",
  }),
  creditAccount: z.string({
    required_error: "Credit account is required",
  }),
  amount: z.coerce.number().positive({
    message: "Amount must be greater than 0",
  }).lt(**********, {
    message: "Amount must be less than UGX 1B",
  }),
  reference: z.string().optional(),
});
```

### 6. `src/components/settings/CompanyManagement.tsx`

**Current Status**: Contains an inline company schema
**Required Changes**:
- Import schema from validations/company-schema.ts
- Remove duplicate schema definition

```typescript
// Current schema
const companySchema = z.object({
  name: z.string().min(2, { message: 'Company name must be at least 2 characters' }),
  tax_id: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  country: z.string().min(1, { message: 'Country is required' }),
  postal_code: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email({ message: 'Invalid email format' }).optional().or(z.string().length(0)),
  website: z.string().url({ message: 'Invalid URL format' }).optional().or(z.string().length(0)),
});
```

## Common Issues

1. **Duplicate Schemas**: Multiple definitions of the same entity (e.g., invoice schema)
2. **Hardcoded Enums**: Using string literals instead of database enum types
3. **Inconsistent Validation**: Different validation rules for the same fields
4. **Missing Fields**: Schema doesn't include all fields from the database
5. **Inline Schemas**: Schema definitions embedded in components instead of imported

## Recommended Approach

1. Create a central location for all Zod schemas that align with database types
2. Use database enums for all enum fields
3. Ensure validation rules match database constraints
4. Import schemas from the central location instead of defining inline
5. Add proper error messages that are user-friendly
