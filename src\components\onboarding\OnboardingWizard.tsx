import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { CheckCircle2, ArrowRight, ArrowLeft } from 'lucide-react';
import { CompanySetupForm } from './steps/CompanySetupForm';
import { AdminUserForm } from './steps/AdminUserForm';
import { InitialSettingsForm } from './steps/InitialSettingsForm';
import { CompletionStep } from './steps/CompletionStep';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';

/**
 * Onboarding wizard steps
 */
type OnboardingStep = 'company-setup' | 'admin-user' | 'initial-settings' | 'complete';

/**
 * Onboarding wizard component
 *
 * A multi-step wizard for new user onboarding with the following steps:
 * 1. Company setup
 * 2. Admin user creation
 * 3. Initial settings
 */
export function OnboardingWizard(): JSX.Element {
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('company-setup');
  const [progress, setProgress] = useState(0);
  const [companyData, setCompanyData] = useState<Record<string, any> | null>(null);
  const [userData, setUserData] = useState<Record<string, any> | null>(null);
  const [settingsData, setSettingsData] = useState<Record<string, any> | null>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { refreshProfile, user, currentCompanyId } = useAuth();

  // Load saved progress from localStorage on initial render
  useEffect(() => {
    const savedProgress = localStorage.getItem('onboarding_progress');
    if (savedProgress) {
      try {
        const { step, company, user, settings } = JSON.parse(savedProgress);
        if (step) setCurrentStep(step);
        if (company) setCompanyData(company);
        if (user) setUserData(user);
        if (settings) setSettingsData(settings);
        setProgress(calculateProgress(step));
      } catch (error) {
        console.error('Error loading saved onboarding progress:', error);
      }
    }
  }, []);

  // Save progress to localStorage when data changes
  useEffect(() => {
    if (currentStep !== 'company-setup' || companyData) {
      localStorage.setItem('onboarding_progress', JSON.stringify({
        step: currentStep,
        company: companyData,
        user: userData,
        settings: settingsData
      }));
    }
  }, [currentStep, companyData, userData, settingsData]);

  // Update company settings with onboarding completion status
  useEffect(() => {
    const updateOnboardingStatus = async (): Promise<void> => {
      if (currentStep === 'complete' && currentCompanyId) {
        try {
          await supabase
            .from('company_settings')
            .upsert({
              company_id: currentCompanyId,
              is_onboarding_complete: true,
              onboarding_step: 4,
              updated_at: new Date().toISOString(),
            });
        } catch (error) {
          console.error('Error updating onboarding status:', error);
        }
      }
    };

    updateOnboardingStatus();
  }, [currentStep, currentCompanyId]);

  // Calculate progress percentage
  const calculateProgress = (step: OnboardingStep): number => {
    switch (step) {
      case 'company-setup':
        return 0;
      case 'admin-user':
        return 33;
      case 'initial-settings':
        return 66;
      case 'complete':
        return 100;
      default:
        return 0;
    }
  };

  // Handle step completion
  const completeStep = (step: OnboardingStep, data: Record<string, any>): void => {
    switch (step) {
      case 'company-setup':
        setCompanyData(data);
        setCurrentStep('admin-user');
        setProgress(calculateProgress('admin-user'));

        // Save progress to localStorage
        localStorage.setItem('onboarding_progress', JSON.stringify({
          step: 'admin-user',
          company: data,
          user: userData,
          settings: settingsData
        }));
        break;

      case 'admin-user':
        setUserData(data);
        setCurrentStep('initial-settings');
        setProgress(calculateProgress('initial-settings'));

        // Save progress to localStorage
        localStorage.setItem('onboarding_progress', JSON.stringify({
          step: 'initial-settings',
          company: companyData,
          user: data,
          settings: settingsData
        }));
        break;

      case 'initial-settings':
        setSettingsData(data);
        setCurrentStep('complete');
        setProgress(calculateProgress('complete'));

        // Save progress to localStorage
        localStorage.setItem('onboarding_progress', JSON.stringify({
          step: 'complete',
          company: companyData,
          user: userData,
          settings: data
        }));

        // Show success toast
        toast({
          title: 'Onboarding Complete',
          description: 'Your account has been successfully set up.',
        });
        break;

      case 'complete':
        // Clear onboarding progress from localStorage
        localStorage.removeItem('onboarding_progress');

        // Refresh user profile and navigate to dashboard
        refreshProfile().then(() => {
          navigate('/dashboard');
        });
        break;
    }
  };

  // Handle step back
  const goBack = (): void => {
    switch (currentStep) {
      case 'admin-user':
        setCurrentStep('company-setup');
        setProgress(calculateProgress('company-setup'));
        break;

      case 'initial-settings':
        setCurrentStep('admin-user');
        setProgress(calculateProgress('admin-user'));
        break;

      case 'complete':
        setCurrentStep('initial-settings');
        setProgress(calculateProgress('initial-settings'));
        break;
    }
  };

  // Handle skip (only available for initial settings)
  const handleSkip = (): void => {
    if (currentStep === 'initial-settings') {
      // Use default settings
      const defaultSettings = {
        fiscal_year_start: 'january',
        currency: 'UGX',
        tax_rate: 18,
        email_notifications: true,
        invoice_reminders: true,
        payment_notifications: true,
      };

      // Save default settings to database
      if (currentCompanyId) {
        supabase
          .from('company_settings')
          .upsert({
            company_id: currentCompanyId,
            fiscal_year_start: defaultSettings.fiscal_year_start,
            default_currency: defaultSettings.currency,
            default_tax_rate: defaultSettings.tax_rate,
            updated_at: new Date().toISOString(),
          })
          .then(({ error }) => {
            if (error) {
              console.error('Error saving default settings:', error);
            }
          });
      }

      setSettingsData(defaultSettings);
      setCurrentStep('complete');
      setProgress(calculateProgress('complete'));
    }
  };

  // Render step content
  const renderStepContent = (): JSX.Element => {
    switch (currentStep) {
      case 'company-setup':
        return (
          <CompanySetupForm
            onComplete={(data) => completeStep('company-setup', data)}
            initialData={companyData}
          />
        );

      case 'admin-user':
        return (
          <AdminUserForm
            onComplete={(data) => completeStep('admin-user', data)}
            initialData={userData}
          />
        );

      case 'initial-settings':
        return (
          <InitialSettingsForm
            onComplete={(data) => completeStep('initial-settings', data)}
            initialData={settingsData}
          />
        );

      case 'complete':
        return (
          <CompletionStep
            companyData={companyData}
            userData={userData}
            settingsData={settingsData}
          />
        );

      default:
        return (
          <div className="p-8 text-center">
            <p>Something went wrong. Please refresh the page and try again.</p>
            <Button
              onClick={() => {
                localStorage.removeItem('onboarding_progress');
                window.location.reload();
              }}
              className="mt-4"
            >
              Restart Onboarding
            </Button>
          </div>
        );
    }
  };

  // Get step title and description
  const getStepInfo = (): { title: string; description: string } => {
    switch (currentStep) {
      case 'company-setup':
        return {
          title: 'Company Setup',
          description: 'Enter your company information to get started',
        };

      case 'admin-user':
        return {
          title: 'Admin User',
          description: 'Create your administrator account',
        };

      case 'initial-settings':
        return {
          title: 'Initial Settings',
          description: 'Configure your account preferences',
        };

      case 'complete':
        return {
          title: 'Setup Complete',
          description: 'Your account is ready to use',
        };

      default:
        return {
          title: 'Onboarding',
          description: 'Set up your account',
        };
    }
  };

  // Get step indicators for the progress bar
  const getStepIndicators = (): JSX.Element => {
    return (
      <div className="flex justify-between mt-4 mb-2">
        <div className={`flex flex-col items-center ${currentStep === 'company-setup' ? 'text-primary' : 'text-muted-foreground'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${currentStep === 'company-setup' ? 'bg-primary text-white' : companyData ? 'bg-primary/20 text-primary' : 'bg-muted text-muted-foreground'}`}>
            1
          </div>
          <span className="text-xs">Company</span>
        </div>

        <div className={`flex flex-col items-center ${currentStep === 'admin-user' ? 'text-primary' : 'text-muted-foreground'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${currentStep === 'admin-user' ? 'bg-primary text-white' : userData ? 'bg-primary/20 text-primary' : 'bg-muted text-muted-foreground'}`}>
            2
          </div>
          <span className="text-xs">Admin</span>
        </div>

        <div className={`flex flex-col items-center ${currentStep === 'initial-settings' ? 'text-primary' : 'text-muted-foreground'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${currentStep === 'initial-settings' ? 'bg-primary text-white' : settingsData ? 'bg-primary/20 text-primary' : 'bg-muted text-muted-foreground'}`}>
            3
          </div>
          <span className="text-xs">Settings</span>
        </div>

        <div className={`flex flex-col items-center ${currentStep === 'complete' ? 'text-primary' : 'text-muted-foreground'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${currentStep === 'complete' ? 'bg-primary text-white' : 'bg-muted text-muted-foreground'}`}>
            <CheckCircle2 className="h-4 w-4" />
          </div>
          <span className="text-xs">Complete</span>
        </div>
      </div>
    );
  };

  const { title, description } = getStepInfo();

  return (
    <div className="flex items-center justify-center min-h-screen bg-muted/20 p-4">
      <Card className="w-full max-w-2xl shadow-lg">
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
          <Progress value={progress} className="mt-2" />
          {getStepIndicators()}
        </CardHeader>

        <CardContent>
          {renderStepContent()}
        </CardContent>

        <CardFooter className="flex justify-between border-t pt-4">
          {currentStep !== 'company-setup' && (
            <Button
              variant="outline"
              onClick={goBack}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
          )}

          {currentStep === 'company-setup' && (
            <div /> // Empty div for spacing
          )}

          {currentStep === 'initial-settings' && (
            <Button
              variant="ghost"
              onClick={handleSkip}
            >
              Skip for now
            </Button>
          )}

          {currentStep === 'complete' && (
            <Button
              onClick={() => completeStep('complete', {})}
              className="flex items-center gap-2"
            >
              Go to Dashboard
              <ArrowRight className="h-4 w-4" />
            </Button>
          )}

          {currentStep !== 'initial-settings' && currentStep !== 'complete' && (
            <div /> // Empty div for spacing when skip button is not shown
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
