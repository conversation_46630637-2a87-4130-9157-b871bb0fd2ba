import { useState } from 'react';
import {
  CheckCircle,
  Cloud,
  Database,
  Laptop
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { GuestHeader } from '@/components/layout/GuestHeader';
import { GuestFooter } from '@/components/layout/GuestFooter';

/**
 * GuestLanding component
 *
 * This is the public landing page for Kaya Finance.
 * It showcases the product features and benefits for potential customers.
 */
const GuestLanding = (): JSX.Element => {
  const [isHeroHovered, setIsHeroHovered] = useState(false);

  return (
    <div className="min-h-screen flex flex-col bg-cream text-dark-blue">
      <GuestHeader />

      {/* Main Content with padding-top to account for fixed header */}
      <main className="flex-grow pt-16">
        {/* 1. Hero Section */}
        <section
          className="min-h-[calc(100vh-4rem)] flex flex-col items-center justify-center px-4 py-16 bg-cream relative overflow-hidden"
          onMouseEnter={() => setIsHeroHovered(true)}
          onMouseLeave={() => setIsHeroHovered(false)}
        >
        <div className="container mx-auto text-center z-10">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-dark-blue mb-4">
            Accounting, Wherever You Work
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-dark-blue/80">
            Cloud flexibility with offline reliability. Keep your books updated even when the internet isn't.
          </p>
          {/* Hero illustration */}
          <div
            className={`relative max-w-4xl mx-auto transition-transform duration-500 ease-in-out ${isHeroHovered ? 'translate-y-[-8px]' : ''}`}
            aria-hidden="true"
          >
            <img
              src="/guest/kaya3.png"
              alt=""
              className="w-full h-auto rounded-lg shadow-xl"
            />
            <div className="absolute -top-4 -right-4 bg-off-white p-2 rounded-full shadow-lg border border-cream">
              <Cloud className="h-8 w-8 text-dark-blue" />
            </div>
            <div className="absolute -bottom-4 -left-4 bg-off-white p-2 rounded-full shadow-lg border border-cream">
              <Database className="h-8 w-8 text-dark-blue" />
            </div>
          </div>
        </div>
      </section>

      {/* 3. Feature Trio */}
      <section className="py-16 bg-cream">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-dark-blue">
            Flexible Solutions for Your Business
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: Cloud,
                title: "Cloud Freedom",
                description: "Access your financial data from anywhere with secure, encrypted backups.",
                link: "#cloud-details"
              },
              {
                icon: Laptop,
                title: "Offline Capture",
                description: "Continue working without internet and sync your data with one click later.",
                link: "#offline-details"
              },
              {
                icon: Database,
                title: "On-Prem Installation",
                description: "Run on your own local server with no monthly hosting fees.",
                link: "#onprem-details"
              }
            ].map((feature, index) => (
              <Card
                key={index}
                className="border border-border bg-cream-alt transition-all duration-300 hover:translate-y-[-8px] hover:shadow-lg"
              >
                <CardHeader className="text-center">
                  <div className="mx-auto bg-[#E6F7F5] p-4 rounded-full mb-4">
                    <feature.icon className="h-8 w-8 text-dark-blue" />
                  </div>
                  <CardTitle>{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-center text-dark-blue/80 mb-4">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* 4. Deep-Dive Strip */}
      <section
        id="why-accountants"
        aria-labelledby="why-accountants-heading"
        className="py-20 bg-cream-alt border-y border-border"
      >
        <div className="max-w-6xl mx-auto px-6">
          <h2
            id="why-accountants-heading"
            className="text-center text-4xl font-bold text-dark-blue mb-12"
          >
            Why Accountants Love Kaya Finance
          </h2>

          <div className="flex flex-col lg:flex-row items-start gap-12">
            {/* Left: Single illustrative image */}
            <div className="flex-1">
              <img
                src="/guest/kaya1.jpg"
                alt="Overview of Kaya Finance interface"
                className="w-full rounded-md shadow-soft border border-border"
              />
            </div>

            {/* Right: Feature bullets */}
            <div className="flex-1">
              <h3 className="text-2xl font-semibold text-dark-blue mb-6">
                Engineered for Ugandan Accountancy
              </h3>
              <ul className="space-y-5">
                {[
                  "Maintain a complete double-entry audit trail with automatic validation checks.",
                  "Start instantly with our pre-configured Ugandan Chart of Accounts, or tailor your own.",
                  "Generate URA-compliant tax returns with one click.",
                  "Support multi-currency transactions with live exchange-rate updates."
                ].map((text, i) => (
                  <li key={i} className="flex items-start">
                    <CheckCircle className="mt-1 h-6 w-6 text-green-600 flex-shrink-0" />
                    <p className="ml-3 text-dark-blue/90">{text}</p>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>


      </main>

      <GuestFooter />
    </div>
  );
};

export default GuestLanding;
