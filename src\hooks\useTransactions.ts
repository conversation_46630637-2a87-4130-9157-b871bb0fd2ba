
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { useToast } from './use-toast';
import {
  Transaction,
  TransactionInsert,
  TransactionItem,
  TransactionItemInsert,
  TransactionWithAccounts,
  ApprovalStatus,
  Account
} from '@/types/index';

// Type for transaction item with nested account
interface TransactionItemWithAccount extends TransactionItem {
  accounts: Pick<Account, 'id' | 'name' | 'code'>;
}

// Type for transaction with nested items
interface TransactionWithItems extends Transaction {
  transaction_items: TransactionItemWithAccount[];
}

export const useTransactions = () => {
  const [transactions, setTransactions] = useState<TransactionWithAccounts[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentCompanyId } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    const fetchTransactions = async () => {
      if (!currentCompanyId) return;

      try {
        setIsLoading(true);

        // Fetch transactions with their associated transaction items and account details
        const { data, error } = await supabase
          .from('transactions')
          .select(`
            id,
            transaction_date,
            reference,
            description,
            status,
            created_at,
            transaction_items (
              id,
              account_id,
              description,
              debit,
              credit,
              accounts (
                id,
                name,
                code
              )
            )
          `)
          .eq('company_id', currentCompanyId)
          .order('transaction_date', { ascending: false });

        if (error) {
          throw error;
        }

        if (data) {
          // Type assertion for the fetched data
          const typedData = data as unknown as TransactionWithItems[];

          // Transform the data to match our TransactionWithAccounts type
          const formattedTransactions: TransactionWithAccounts[] = typedData.map(item => {
            // Map transaction items with their account details
            const items = item.transaction_items.map(ti => ({
              ...ti,
              account: ti.accounts
            }));

            return {
              ...item,
              date: item.transaction_date, // Map transaction_date to date for UI consistency
              items
            };
          });

          setTransactions(formattedTransactions);
        }
      } catch (err) {
        console.error('Error fetching transactions:', err);
        const errorMessage = err instanceof Error
          ? err.message
          : "Failed to fetch transactions. Please try again.";

        toast({
          title: "Error fetching transactions",
          description: errorMessage,
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchTransactions();
  }, [currentCompanyId, toast]);

  const addTransaction = async (transactionData: {
    date: string;
    description: string;
    reference?: string;
    items: Array<{
      account_id: string;
      description?: string;
      debit?: number | null;
      credit?: number | null;
    }>;
  }) => {
    try {
      if (!currentCompanyId) {
        toast({
          title: "Error",
          description: "No company selected",
          variant: "destructive"
        });
        return null;
      }

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        toast({
          title: "Error",
          description: "User not authenticated",
          variant: "destructive"
        });
        return null;
      }

      // First insert the transaction
      const transactionInsert: TransactionInsert = {
        transaction_date: transactionData.date,
        reference: transactionData.reference || null,
        description: transactionData.description,
        status: 'pending', // Start as pending
        company_id: currentCompanyId,
        created_by: user.id
      };

      const { data: newTransaction, error: transactionError } = await supabase
        .from('transactions')
        .insert(transactionInsert)
        .select()
        .single();

      if (transactionError) throw transactionError;

      // Then insert the transaction items
      const transactionItems: TransactionItemInsert[] = transactionData.items.map(item => ({
        transaction_id: newTransaction.id,
        account_id: item.account_id,
        description: item.description || null,
        debit: item.debit || null,
        credit: item.credit || null
      }));

      const { error: itemsError } = await supabase
        .from('transaction_items')
        .insert(transactionItems);

      if (itemsError) throw itemsError;

      // Fetch the complete transaction with account details
      const { data: completeTransaction, error: fetchError } = await supabase
        .from('transactions')
        .select(`
          id,
          transaction_date,
          reference,
          description,
          status,
          created_at,
          transaction_items (
            id,
            account_id,
            description,
            debit,
            credit,
            accounts (
              id,
              name,
              code
            )
          )
        `)
        .eq('id', newTransaction.id)
        .single();

      if (fetchError) throw fetchError;

      // Type assertion for the complete transaction
      const typedTransaction = completeTransaction as unknown as TransactionWithItems;

      // Format the transaction for state update
      const items = typedTransaction.transaction_items.map(ti => ({
        ...ti,
        account: ti.accounts
      }));

      const formattedTransaction: TransactionWithAccounts = {
        ...typedTransaction,
        date: typedTransaction.transaction_date, // Map transaction_date to date for UI consistency
        items
      };

      // Update state
      setTransactions(prev => [formattedTransaction, ...prev]);

      toast({
        title: "Transaction Added",
        description: "New transaction has been recorded successfully."
      });

      return newTransaction.id;
    } catch (err) {
      console.error('Error adding transaction:', err);
      const errorMessage = err instanceof Error
        ? err.message
        : "Failed to add transaction. Please try again.";

      toast({
        title: "Error adding transaction",
        description: errorMessage,
        variant: "destructive"
      });
      return null;
    }
  };

  const updateTransaction = async (
    transactionId: string,
    updates: {
      date?: string;
      description?: string;
      reference?: string | null;
      items?: Array<{
        id?: string;
        account_id: string;
        description?: string | null;
        debit?: number | null;
        credit?: number | null;
      }>;
    }
  ) => {
    try {
      // Start a transaction to ensure all updates are atomic
      const transactionUpdates: Partial<Transaction> = {};

      if (updates.date) transactionUpdates.transaction_date = updates.date;
      if (updates.description) transactionUpdates.description = updates.description;
      if (updates.reference !== undefined) transactionUpdates.reference = updates.reference;

      // Update the transaction record if there are changes
      if (Object.keys(transactionUpdates).length > 0) {
        const { error: transactionError } = await supabase
          .from('transactions')
          .update(transactionUpdates)
          .eq('id', transactionId);

        if (transactionError) throw transactionError;
      }

      // Update transaction items if provided
      if (updates.items && updates.items.length > 0) {
        // Get existing transaction items
        const { data: existingItems, error: itemsError } = await supabase
          .from('transaction_items')
          .select('id, account_id')
          .eq('transaction_id', transactionId);

        if (itemsError) throw itemsError;

        // Process each item update
        for (const item of updates.items) {
          if (item.id) {
            // Update existing item
            const { error } = await supabase
              .from('transaction_items')
              .update({
                account_id: item.account_id,
                description: item.description,
                debit: item.debit,
                credit: item.credit
              })
              .eq('id', item.id);

            if (error) throw error;
          } else {
            // Insert new item
            const { error } = await supabase
              .from('transaction_items')
              .insert({
                transaction_id: transactionId,
                account_id: item.account_id,
                description: item.description,
                debit: item.debit,
                credit: item.credit
              });

            if (error) throw error;
          }
        }
      }

      // Fetch the updated transaction with all its details
      const { data: updatedTransaction, error: fetchError } = await supabase
        .from('transactions')
        .select(`
          id,
          transaction_date,
          reference,
          description,
          status,
          created_at,
          transaction_items (
            id,
            account_id,
            description,
            debit,
            credit,
            accounts (
              id,
              name,
              code
            )
          )
        `)
        .eq('id', transactionId)
        .single();

      if (fetchError) throw fetchError;

      // Type assertion for the updated transaction
      const typedTransaction = updatedTransaction as unknown as TransactionWithItems;

      // Format the transaction for state update
      const items = typedTransaction.transaction_items.map(ti => ({
        ...ti,
        account: ti.accounts
      }));

      const formattedTransaction: TransactionWithAccounts = {
        ...typedTransaction,
        date: typedTransaction.transaction_date, // Map transaction_date to date for UI consistency
        items
      };

      // Update the local state
      setTransactions(prev =>
        prev.map(t => t.id === transactionId ? formattedTransaction : t)
      );

      toast({
        title: "Transaction Updated",
        description: `Transaction ${transactionId} has been updated.`
      });
    } catch (err) {
      console.error('Error updating transaction:', err);
      const errorMessage = err instanceof Error
        ? err.message
        : "Failed to update transaction. Please try again.";

      toast({
        title: "Error updating transaction",
        description: errorMessage,
        variant: "destructive"
      });
    }
  };

  const deleteTransaction = async (transactionId: string) => {
    try {
      // Delete the transaction (this will cascade delete transaction items due to foreign key)
      const { error } = await supabase
        .from('transactions')
        .delete()
        .eq('id', transactionId);

      if (error) throw error;

      // Update local state
      setTransactions(prev => prev.filter(t => t.id !== transactionId));

      toast({
        title: "Transaction Deleted",
        description: `Transaction ${transactionId} has been removed.`
      });

      return true;
    } catch (err) {
      console.error('Error deleting transaction:', err);
      const errorMessage = err instanceof Error
        ? err.message
        : "Failed to delete transaction. Please try again.";

      toast({
        title: "Error deleting transaction",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  };

  const updateTransactionStatus = async (transactionId: string, status: ApprovalStatus) => {
    try {
      const { error } = await supabase
        .from('transactions')
        .update({ status })
        .eq('id', transactionId);

      if (error) throw error;

      // Update local state
      setTransactions(prev =>
        prev.map(t => t.id === transactionId ? { ...t, status } : t)
      );

      toast({
        title: "Status Updated",
        description: `Transaction status changed to ${status}.`
      });

      return true;
    } catch (err) {
      console.error('Error updating transaction status:', err);
      const errorMessage = err instanceof Error
        ? err.message
        : "Failed to update transaction status. Please try again.";

      toast({
        title: "Error updating status",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  };

  return {
    transactions,
    isLoading,
    addTransaction,
    updateTransaction,
    deleteTransaction,
    updateTransactionStatus
  };
};
