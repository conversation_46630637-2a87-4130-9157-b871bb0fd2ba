
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CashFlowData, PeriodComparisonData } from '@/services/report-service';
import { format } from 'date-fns';
import { ArrowDownIcon, ArrowUpIcon, ArrowRightIcon, TrendingDownIcon, TrendingUpIcon } from 'lucide-react';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface CashFlowReportProps {
  data: CashFlowData;
  startDate: Date;
  endDate: Date;
  formatCurrency: (amount: number) => string;
  isLoading?: boolean;
}

// Helper component for displaying period comparison
const PeriodComparison = ({
  data,
  formatCurrency
}: {
  data: PeriodComparisonData;
  formatCurrency: (amount: number) => string;
}) => {
  const isPositive = data.percentageChange > 0;
  const isNegative = data.percentageChange < 0;
  const isZero = data.percentageChange === 0;

  return (
    <div className="flex items-center gap-2">
      <span className={`font-medium ${isPositive ? 'text-green-600' : isNegative ? 'text-red-600' : ''}`}>
        {formatCurrency(data.currentPeriodTotal)}
      </span>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center">
              {isPositive && <TrendingUpIcon className="h-4 w-4 text-green-600" />}
              {isNegative && <TrendingDownIcon className="h-4 w-4 text-red-600" />}
              {isZero && <ArrowRightIcon className="h-4 w-4 text-gray-500" />}
              <span className={`text-xs ${isPositive ? 'text-green-600' : isNegative ? 'text-red-600' : 'text-gray-500'}`}>
                {data.percentageChange.toFixed(1)}%
              </span>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>Previous period: {formatCurrency(data.previousPeriodTotal)}</p>
            <p>Change: {isPositive ? '+' : ''}{data.percentageChange.toFixed(1)}%</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

// Helper component for displaying cash flow items
const CashFlowItems = ({
  items,
  formatCurrency
}: {
  items: Array<{ name: string; amount: number; transaction_id?: string }>;
  formatCurrency: (amount: number) => string;
}) => {
  return (
    <div className="space-y-1">
      {items.map((item, index) => (
        <div key={index} className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            {item.amount > 0 ? (
              <ArrowUpIcon className="h-3 w-3 text-green-600" />
            ) : (
              <ArrowDownIcon className="h-3 w-3 text-red-600" />
            )}
            <span className="text-sm">{item.name}</span>
          </div>
          <span className={`text-sm ${item.amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {formatCurrency(item.amount)}
          </span>
        </div>
      ))}
      {items.length === 0 && (
        <div className="text-sm text-muted-foreground py-1">No activities in this period</div>
      )}
    </div>
  );
};

export const CashFlowReport = ({
  data,
  startDate,
  endDate,
  formatCurrency,
  isLoading = false
}: CashFlowReportProps) => {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Cash Flow</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-40 flex items-center justify-center">
            <p className="text-muted-foreground">Loading cash flow data...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const formatDateRange = `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`;
  const hasPreviousPeriodData = !!data.previousPeriod;

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Cash Flow Statement</CardTitle>
            <p className="text-sm text-muted-foreground">{formatDateRange}</p>
          </div>
          {hasPreviousPeriodData && (
            <Badge variant="outline" className="ml-2">
              Period-over-Period Comparison
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="standard" className="mb-4">
          <TabsList>
            <TabsTrigger value="standard">Standard View</TabsTrigger>
            <TabsTrigger value="detailed">Detailed View</TabsTrigger>
          </TabsList>

          <TabsContent value="standard" className="space-y-6">
            {/* Operating Activities Section */}
            <section>
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-medium text-lg">Operating Activities</h3>
                {hasPreviousPeriodData && (
                  <PeriodComparison
                    data={data.previousPeriod.operatingActivities}
                    formatCurrency={formatCurrency}
                  />
                )}
              </div>
              <div className="space-y-1 border-b pb-2">
                <CashFlowItems
                  items={data.operatingActivities.items}
                  formatCurrency={formatCurrency}
                />
              </div>
              <div className="flex justify-between py-2 font-medium">
                <span>Net Cash from Operating Activities</span>
                <span className={data.operatingActivities.total >= 0 ? 'text-green-600' : 'text-red-600'}>
                  {formatCurrency(data.operatingActivities.total)}
                </span>
              </div>
            </section>

            {/* Investing Activities Section */}
            <section>
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-medium text-lg">Investing Activities</h3>
                {hasPreviousPeriodData && (
                  <PeriodComparison
                    data={data.previousPeriod.investingActivities}
                    formatCurrency={formatCurrency}
                  />
                )}
              </div>
              <div className="space-y-1 border-b pb-2">
                <CashFlowItems
                  items={data.investingActivities.items}
                  formatCurrency={formatCurrency}
                />
              </div>
              <div className="flex justify-between py-2 font-medium">
                <span>Net Cash from Investing Activities</span>
                <span className={data.investingActivities.total >= 0 ? 'text-green-600' : 'text-red-600'}>
                  {formatCurrency(data.investingActivities.total)}
                </span>
              </div>
            </section>

            {/* Financing Activities Section */}
            <section>
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-medium text-lg">Financing Activities</h3>
                {hasPreviousPeriodData && (
                  <PeriodComparison
                    data={data.previousPeriod.financingActivities}
                    formatCurrency={formatCurrency}
                  />
                )}
              </div>
              <div className="space-y-1 border-b pb-2">
                <CashFlowItems
                  items={data.financingActivities.items}
                  formatCurrency={formatCurrency}
                />
              </div>
              <div className="flex justify-between py-2 font-medium">
                <span>Net Cash from Financing Activities</span>
                <span className={data.financingActivities.total >= 0 ? 'text-green-600' : 'text-red-600'}>
                  {formatCurrency(data.financingActivities.total)}
                </span>
              </div>
            </section>

            {/* Net Cash Flow */}
            <section>
              <div className="flex justify-between items-center py-2 font-bold text-lg border-t">
                <span>Net Change in Cash</span>
                <div className="flex items-center gap-2">
                  <span className={data.netCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}>
                    {formatCurrency(data.netCashFlow)}
                  </span>
                  {hasPreviousPeriodData && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="flex items-center">
                            {data.previousPeriod.netCashFlow.percentageChange > 0 && (
                              <TrendingUpIcon className="h-4 w-4 text-green-600" />
                            )}
                            {data.previousPeriod.netCashFlow.percentageChange < 0 && (
                              <TrendingDownIcon className="h-4 w-4 text-red-600" />
                            )}
                            {data.previousPeriod.netCashFlow.percentageChange === 0 && (
                              <ArrowRightIcon className="h-4 w-4 text-gray-500" />
                            )}
                            <span className={`text-xs ${
                              data.previousPeriod.netCashFlow.percentageChange > 0
                                ? 'text-green-600'
                                : data.previousPeriod.netCashFlow.percentageChange < 0
                                  ? 'text-red-600'
                                  : 'text-gray-500'
                            }`}>
                              {data.previousPeriod.netCashFlow.percentageChange.toFixed(1)}%
                            </span>
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Previous period: {formatCurrency(data.previousPeriod.netCashFlow.previousPeriodTotal)}</p>
                          <p>Change: {data.previousPeriod.netCashFlow.percentageChange > 0 ? '+' : ''}
                            {data.previousPeriod.netCashFlow.percentageChange.toFixed(1)}%</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </div>
              </div>
            </section>
          </TabsContent>

          <TabsContent value="detailed">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className={`border-l-4 ${data.operatingActivities.total >= 0 ? 'border-l-green-500' : 'border-l-red-500'}`}>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Operating Activities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrency(data.operatingActivities.total)}
                    </div>
                    {hasPreviousPeriodData && (
                      <div className="flex items-center mt-1 text-sm">
                        <span className="text-muted-foreground mr-1">vs previous:</span>
                        <span className={
                          data.previousPeriod.operatingActivities.percentageChange > 0
                            ? 'text-green-600'
                            : data.previousPeriod.operatingActivities.percentageChange < 0
                              ? 'text-red-600'
                              : ''
                        }>
                          {data.previousPeriod.operatingActivities.percentageChange > 0 ? '+' : ''}
                          {data.previousPeriod.operatingActivities.percentageChange.toFixed(1)}%
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card className={`border-l-4 ${data.investingActivities.total >= 0 ? 'border-l-green-500' : 'border-l-red-500'}`}>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Investing Activities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrency(data.investingActivities.total)}
                    </div>
                    {hasPreviousPeriodData && (
                      <div className="flex items-center mt-1 text-sm">
                        <span className="text-muted-foreground mr-1">vs previous:</span>
                        <span className={
                          data.previousPeriod.investingActivities.percentageChange > 0
                            ? 'text-green-600'
                            : data.previousPeriod.investingActivities.percentageChange < 0
                              ? 'text-red-600'
                              : ''
                        }>
                          {data.previousPeriod.investingActivities.percentageChange > 0 ? '+' : ''}
                          {data.previousPeriod.investingActivities.percentageChange.toFixed(1)}%
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card className={`border-l-4 ${data.financingActivities.total >= 0 ? 'border-l-green-500' : 'border-l-red-500'}`}>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Financing Activities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrency(data.financingActivities.total)}
                    </div>
                    {hasPreviousPeriodData && (
                      <div className="flex items-center mt-1 text-sm">
                        <span className="text-muted-foreground mr-1">vs previous:</span>
                        <span className={
                          data.previousPeriod.financingActivities.percentageChange > 0
                            ? 'text-green-600'
                            : data.previousPeriod.financingActivities.percentageChange < 0
                              ? 'text-red-600'
                              : ''
                        }>
                          {data.previousPeriod.financingActivities.percentageChange > 0 ? '+' : ''}
                          {data.previousPeriod.financingActivities.percentageChange.toFixed(1)}%
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              <Card className={`border-t-4 ${data.netCashFlow >= 0 ? 'border-t-green-500' : 'border-t-red-500'}`}>
                <CardHeader className="pb-2">
                  <CardTitle>Net Change in Cash</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center">
                    <div className="text-3xl font-bold">
                      {formatCurrency(data.netCashFlow)}
                    </div>
                    {hasPreviousPeriodData && (
                      <div className="flex flex-col items-end">
                        <div className="text-sm text-muted-foreground">Previous period</div>
                        <div className="text-lg font-medium">
                          {formatCurrency(data.previousPeriod.netCashFlow.previousPeriodTotal)}
                        </div>
                        <div className={`text-sm ${
                          data.previousPeriod.netCashFlow.percentageChange > 0
                            ? 'text-green-600'
                            : data.previousPeriod.netCashFlow.percentageChange < 0
                              ? 'text-red-600'
                              : ''
                        }`}>
                          {data.previousPeriod.netCashFlow.percentageChange > 0 ? '+' : ''}
                          {data.previousPeriod.netCashFlow.percentageChange.toFixed(1)}% change
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
