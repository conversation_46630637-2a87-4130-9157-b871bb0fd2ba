/**
 * Utility Types for Kaya Finance
 *
 * This file contains reusable TypeScript utility types that can be used
 * throughout the application to enhance type safety and reduce duplication.
 */

/**
 * Extract the ID type from a table row type
 * @example type UserId = Id<User>; // string
 */
export type Id<T extends { id: unknown }> = T['id'];

/**
 * Create a partial record type where some keys may be undefined
 * @example type PartialUserRoles = PartialRecord<UserRole, boolean>;
 */
export type PartialRecord<K extends keyof any, T> = Partial<Record<K, T>>;

/**
 * Make all properties in T nullable
 * @example type NullableUser = Nullable<User>;
 */
export type Nullable<T> = { [P in keyof T]: T[P] | null };

/**
 * Make all properties in T optional
 * @example type OptionalUser = Optional<User>;
 */
export type Optional<T> = { [P in keyof T]?: T[P] };

/**
 * Make all properties in T both nullable and optional
 * @example type NullableOptionalUser = NullableOptional<User>;
 */
export type NullableOptional<T> = { [P in keyof T]?: T[P] | null };

/**
 * Pick specific properties from T and make them required
 * @example type UserCredentials = RequiredPick<User, 'email' | 'password'>;
 */
export type RequiredPick<T, K extends keyof T> = Required<Pick<T, K>>;

/**
 * Omit specific properties from T and make the rest required
 * @example type RequiredUserFields = RequiredOmit<User, 'avatar' | 'bio'>;
 */
export type RequiredOmit<T, K extends keyof T> = Required<Omit<T, K>>;

/**
 * Create a type with only the methods of T
 * @example type UserMethods = Methods<User>;
 */
export type Methods<T> = {
  [P in keyof T as T[P] extends (...args: any[]) => any ? P : never]: T[P]
};

/**
 * Create a type with only the properties (non-methods) of T
 * @example type UserProperties = Properties<User>;
 */
export type Properties<T> = {
  [P in keyof T as T[P] extends (...args: any[]) => any ? never : P]: T[P]
};

/**
 * Create a deep partial type where nested objects are also partial
 * @example type DeepPartialUser = DeepPartial<User>;
 */
export type DeepPartial<T> = T extends object ? {
  [P in keyof T]?: DeepPartial<T[P]>;
} : T;

/**
 * Create a type that requires at least one of the properties in T
 * @example type UserSearch = AtLeastOne<{ email?: string; name?: string; id?: string }>;
 */
export type AtLeastOne<T, U = { [K in keyof T]: Pick<T, K> }> = Partial<T> & U[keyof U];

/**
 * Create a type that requires exactly one of the properties in T
 * @example type UserIdentifier = ExactlyOne<{ email: string; id: string }>;
 */
export type ExactlyOne<T, U = { [K in keyof T]: Pick<T, K> & Partial<Record<Exclude<keyof T, K>, never>> }> = U[keyof U];

/**
 * Type guard to check if a value is not null or undefined
 * @example if (isNotNullOrUndefined(user)) { // user is User, not User | null | undefined }
 */
export function isNotNullOrUndefined<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

/**
 * Type guard to check if a value is a string
 * @example if (isString(value)) { // value is string }
 */
export function isString(value: unknown): value is string {
  return typeof value === 'string';
}

/**
 * Type guard to check if a value is a number
 * @example if (isNumber(value)) { // value is number }
 */
export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value);
}

/**
 * Type guard to check if a value is a boolean
 * @example if (isBoolean(value)) { // value is boolean }
 */
export function isBoolean(value: unknown): value is boolean {
  return typeof value === 'boolean';
}

/**
 * Type guard to check if a value is an object
 * @example if (isObject(value)) { // value is object }
 */
export function isObject(value: unknown): value is object {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

/**
 * Type guard to check if a value is an array
 * @example if (isArray(value)) { // value is unknown[] }
 */
export function isArray(value: unknown): value is unknown[] {
  return Array.isArray(value);
}

/**
 * Type guard to check if a value is an array of a specific type
 * @example if (isArrayOf(value, isString)) { // value is string[] }
 */
export function isArrayOf<T>(value: unknown, typeGuard: (item: unknown) => item is T): value is T[] {
  return Array.isArray(value) && value.every(typeGuard);
}

/**
 * Type assertion function that throws if condition is false
 * @example assertType(isString(value), 'Value must be a string');
 */
export function assertType(condition: boolean, message: string): asserts condition {
  if (!condition) {
    throw new Error(`Type assertion failed: ${message}`);
  }
}

/**
 * Type assertion function for non-null values
 * @example const user = assertNonNull(maybeUser, 'User not found');
 */
export function assertNonNull<T>(value: T | null | undefined, message: string): T {
  if (value === null || value === undefined) {
    throw new Error(`Null assertion failed: ${message}`);
  }
  return value;
}
