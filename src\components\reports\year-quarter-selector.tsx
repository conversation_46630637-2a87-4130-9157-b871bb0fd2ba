
import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";

interface YearQuarterSelectorProps {
  reportYear: number;
  reportQuarter: number;
  dateRange: string;
  setReportYear: (year: number) => void;
  setReportQuarter: (quarter: number) => void;
  setDateRange: (range: string) => void;
}

export function YearQuarterSelector({
  reportYear,
  reportQuarter,
  dateRange,
  setReportYear,
  setReportQuarter,
  setDateRange
}: YearQuarterSelectorProps) {
  const { currentCompanyId } = useAuth();
  const [fiscalYearStart, setFiscalYearStart] = useState<Date | null>(null);
  const [fiscalYearStartMonth, setFiscalYearStartMonth] = useState(0); // Default to January (0)

  // Fetch company fiscal year settings
  useEffect(() => {
    const fetchFiscalYearSettings = async () => {
      if (!currentCompanyId) return;

      try {
        const { data, error } = await supabase
          .from('companies')
          .select('fiscal_year_start')
          .eq('id', currentCompanyId)
          .single();

        if (error) {
          console.error('Error fetching fiscal year settings:', error);
          return;
        }

        if (data?.fiscal_year_start) {
          const fiscalDate = new Date(data.fiscal_year_start);
          setFiscalYearStart(fiscalDate);
          setFiscalYearStartMonth(fiscalDate.getMonth());
        }
      } catch (error) {
        console.error('Error in fiscal year fetch:', error);
      }
    };

    fetchFiscalYearSettings();
  }, [currentCompanyId]);

  // Generate year options for dropdown
  const currentYear = new Date().getFullYear();
  const yearOptions = [];
  for (let year = currentYear; year >= currentYear - 4; year--) {
    yearOptions.push(year);
  }

  // Get quarter labels based on fiscal year start
  const getQuarterLabel = (quarter: number): string => {
    if (fiscalYearStartMonth === 0) {
      // Calendar year (Jan-Dec)
      switch (quarter) {
        case 1: return "Q1 (Jan-Mar)";
        case 2: return "Q2 (Apr-Jun)";
        case 3: return "Q3 (Jul-Sep)";
        case 4: return "Q4 (Oct-Dec)";
        default: return `Q${quarter}`;
      }
    } else if (fiscalYearStartMonth === 6) {
      // July-June fiscal year
      switch (quarter) {
        case 1: return "Q1 (Jul-Sep)";
        case 2: return "Q2 (Oct-Dec)";
        case 3: return "Q3 (Jan-Mar)";
        case 4: return "Q4 (Apr-Jun)";
        default: return `Q${quarter}`;
      }
    } else {
      // Custom fiscal year
      const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
      const startMonth = fiscalYearStartMonth;

      const q1Start = startMonth;
      const q1End = (startMonth + 2) % 12;
      const q2Start = (startMonth + 3) % 12;
      const q2End = (startMonth + 5) % 12;
      const q3Start = (startMonth + 6) % 12;
      const q3End = (startMonth + 8) % 12;
      const q4Start = (startMonth + 9) % 12;
      const q4End = (startMonth + 11) % 12;

      switch (quarter) {
        case 1: return `Q1 (${months[q1Start]}-${months[q1End]})`;
        case 2: return `Q2 (${months[q2Start]}-${months[q2End]})`;
        case 3: return `Q3 (${months[q3Start]}-${months[q3End]})`;
        case 4: return `Q4 (${months[q4Start]}-${months[q4End]})`;
        default: return `Q${quarter}`;
      }
    }
  };

  return (
    <div className="mb-6 flex items-center gap-4 flex-wrap">
      <div>
        <label className="block text-sm font-medium mb-1">Year</label>
        <Select value={reportYear.toString()} onValueChange={(value) => setReportYear(parseInt(value))}>
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="Select Year" />
          </SelectTrigger>
          <SelectContent>
            {yearOptions.map((year) => (
              <SelectItem key={year} value={year.toString()}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Report Type</label>
        <Select value={dateRange} onValueChange={setDateRange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select Report Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="year">Annual</SelectItem>
            <SelectItem value="quarter">Quarterly</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {dateRange === 'quarter' && (
        <div>
          <label className="block text-sm font-medium mb-1">Quarter</label>
          <Select value={reportQuarter.toString()} onValueChange={(value) => setReportQuarter(parseInt(value))}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Select Quarter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">{getQuarterLabel(1)}</SelectItem>
              <SelectItem value="2">{getQuarterLabel(2)}</SelectItem>
              <SelectItem value="3">{getQuarterLabel(3)}</SelectItem>
              <SelectItem value="4">{getQuarterLabel(4)}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}
    </div>
  );
}
