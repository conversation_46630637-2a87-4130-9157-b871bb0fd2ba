
import { Input } from '@/components/ui/input';
import { Search, Filter } from 'lucide-react';
import {
  Select, SelectContent, SelectItem, SelectTrigger
} from '@/components/ui/select';

interface InvoiceFilterProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  statusFilter: string;
  setStatusFilter: (value: string) => void;
}

export const InvoiceFilter = ({
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter
}: InvoiceFilterProps): JSX.Element => {
  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0">
      <h2 className="text-xl font-semibold">Customer Invoices</h2>
      <div className="flex items-center space-x-2">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search invoices..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <div className="flex items-center">
              <Filter className="mr-2 h-4 w-4" />
              <span>{statusFilter === 'all' ? 'All statuses' : statusFilter}</span>
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All statuses</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="paid">Paid</SelectItem>
            <SelectItem value="overdue">Overdue</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};
