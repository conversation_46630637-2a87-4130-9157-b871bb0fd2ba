import { supabase } from '@/integrations/supabase/client';

export type BudgetRequest = {
  id: string;
  department: string;
  title: string;
  amount: number;
  submittedBy: string;
  submittedDate: string;
  status: string;
  priority: string;
};

// Empty array for budget requests - will be populated from the database
export const budgetRequests: BudgetRequest[] = [];

// Function to fetch budget requests from Supabase
export const fetchBudgetRequests = async (companyId: string): Promise<BudgetRequest[]> => {
  try {
    const { data, error } = await supabase
      .from('budgets')
      .select(`
        id,
        name,
        amount,
        period_start,
        period_end,
        category,
        description,
        created_at,
        created_by,
        status,
        profiles (
          first_name,
          last_name
        )
      `)
      .eq('company_id', companyId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching budget requests:', error);
      return [];
    }

    if (!data || data.length === 0) {
      return [];
    }

    // Transform the data to match the BudgetRequest type
    return data.map(budget => ({
      id: budget.id,
      department: budget.category || 'General',
      title: budget.name,
      amount: budget.amount || 0,
      submittedBy: budget.profiles ? 
        `${budget.profiles.first_name} ${budget.profiles.last_name}` : 
        'Unknown User',
      submittedDate: budget.created_at ? new Date(budget.created_at).toISOString().split('T')[0] : '',
      status: budget.status || 'pending',
      priority: getPriorityFromAmount(budget.amount || 0)
    }));
  } catch (error) {
    console.error('Unexpected error fetching budget requests:', error);
    return [];
  }
};

// Helper function to determine priority based on amount
const getPriorityFromAmount = (amount: number): string => {
  if (amount > 50000) return 'critical';
  if (amount > 20000) return 'high';
  if (amount > 5000) return 'medium';
  return 'low';
};

// Calculate summary metrics
export const getSummaryMetrics = async (companyId: string) => {
  try {
    const requests = await fetchBudgetRequests(companyId);
    
    return {
      totalBudgetRequests: requests.length,
      approvedBudgets: requests.filter(req => req.status === 'approved').length,
      pendingBudgets: requests.filter(req => req.status === 'pending').length,
      totalApprovedAmount: requests
        .filter(req => req.status === 'approved')
        .reduce((sum, req) => sum + req.amount, 0)
    };
  } catch (error) {
    console.error('Error calculating summary metrics:', error);
    return {
      totalBudgetRequests: 0,
      approvedBudgets: 0,
      pendingBudgets: 0,
      totalApprovedAmount: 0
    };
  }
};

// Generate page numbers for pagination
export const getPageNumbers = (currentPage: number, totalPages: number) => {
  const pageNumbers = [];
  const maxPagesToShow = 5; // Maximum number of page links to show

  if (totalPages <= maxPagesToShow) {
    // If total pages is less than or equal to max pages to show, display all pages
    for (let i = 1; i <= totalPages; i++) {
      pageNumbers.push(i);
    }
  } else {
    // Always include first page
    pageNumbers.push(1);

    // Calculate start and end page numbers
    let startPage = Math.max(2, currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(totalPages - 1, startPage + maxPagesToShow - 3);

    // Adjust if we're near the beginning
    if (startPage === 2) {
      endPage = Math.min(totalPages - 1, maxPagesToShow - 1);
    }

    // Adjust if we're near the end
    if (endPage === totalPages - 1) {
      startPage = Math.max(2, totalPages - maxPagesToShow + 2);
    }

    // Add ellipsis after first page if needed
    if (startPage > 2) {
      pageNumbers.push('...');
    }

    // Add middle pages
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    // Add ellipsis before last page if needed
    if (endPage < totalPages - 1) {
      pageNumbers.push('...');
    }

    // Always include last page
    pageNumbers.push(totalPages);
  }
  
  return pageNumbers;
};

// Format currency
export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-UG', {
    style: 'currency',
    currency: 'UGX',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};
