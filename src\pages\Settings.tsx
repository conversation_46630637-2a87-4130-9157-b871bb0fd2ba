
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { UserRoleManagement } from '@/components/settings/UserRoleManagement';
import { CompanyManagement } from '@/components/settings/CompanyManagement';
import { FiscalYearSettings } from '@/components/settings/FiscalYearSettings';
import { RLSPolicyTester } from '@/components/settings/RLSPolicyTester';
import { usePermissions } from '@/hooks/usePermissions';

const Settings = (): React.JSX.Element => {
  const [companyName, setCompanyName] = useState('Acme Corporation');
  const [email, setEmail] = useState('<EMAIL>');
  const { canManageUsers } = usePermissions();

  return (
    <div>
      <h1 className="page-title">Settings</h1>

      <Tabs defaultValue="company">
        <TabsList className="mb-6">
          <TabsTrigger value="company">Company</TabsTrigger>
          <TabsTrigger value="fiscal">Fiscal Year</TabsTrigger>
          <TabsTrigger value="users">Users & Roles</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="tax">Tax Settings</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="company">
          <CompanyManagement />
        </TabsContent>

        <TabsContent value="fiscal">
          <FiscalYearSettings />
        </TabsContent>

        <TabsContent value="users">
          {canManageUsers() ? (
            <UserRoleManagement />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Users & Roles</CardTitle>
                <CardDescription>Manage users and their permissions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80 flex items-center justify-center">
                  <p className="text-muted-foreground">You do not have permission to manage users</p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>Manage authentication and security options</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="password">Password Requirements</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="require-uppercase" className="rounded border-gray-300" checked />
                      <label htmlFor="require-uppercase">Require uppercase letters</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="require-lowercase" className="rounded border-gray-300" checked />
                      <label htmlFor="require-lowercase">Require lowercase letters</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="require-numbers" className="rounded border-gray-300" checked />
                      <label htmlFor="require-numbers">Require numbers</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="require-symbols" className="rounded border-gray-300" />
                      <label htmlFor="require-symbols">Require symbols</label>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="min-length">Minimum Password Length</Label>
                  <Input type="number" id="min-length" min="8" max="32" defaultValue="8" />
                </div>

                <div className="space-y-2 pt-4">
                  <Label htmlFor="session-timeout">Session Timeout (minutes)</Label>
                  <Input type="number" id="session-timeout" min="5" defaultValue="60" />
                </div>

                <Button className="mt-4">Save Security Settings</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tax">
          <Card>
            <CardHeader>
              <CardTitle>Tax Configuration</CardTitle>
              <CardDescription>Configure tax rates and settings</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="default-tax">Default Tax Rate (%)</Label>
                  <Input type="number" id="default-tax" min="0" max="100" step="0.01" defaultValue="18" />
                </div>

                <div className="space-y-2">
                  <Label>Tax Registration</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="tax-id">Tax ID Number</Label>
                      <Input id="tax-id" defaultValue="UG123456789" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="tax-authority">Tax Authority</Label>
                      <Input id="tax-authority" defaultValue="Uganda Revenue Authority" />
                    </div>
                  </div>
                </div>

                <Button className="mt-4">Save Tax Settings</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced">
          <div className="space-y-6">
            {canManageUsers() ? (
              <RLSPolicyTester />
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>Advanced Settings</CardTitle>
                  <CardDescription>Advanced system configuration</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80 flex items-center justify-center">
                    <p className="text-muted-foreground">You do not have permission to access advanced settings</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;
