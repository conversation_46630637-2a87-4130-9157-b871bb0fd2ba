/**
 * Credit Note Service
 *
 * This service provides functions for creating, updating, and managing credit notes.
 */

import { v4 as uuidv4 } from 'uuid';
import { format } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import { showSuccessToast, showErrorToast } from '@/utils/toast-utils';
import { Database } from '@/types/supabase';
import { PostgrestError } from '@supabase/supabase-js';

// Type definitions
export type CreditNoteStatus = 'draft' | 'issued' | 'approved' | 'rejected';

export interface CreditNoteItem {
  description: string;
  quantity: number;
  unit_price: number;
  tax_rate: number;
  invoice_item_id?: string;
}

export interface CreditNoteFormValues {
  invoice_id: string;
  reason: string;
  notes?: string;
  issue_date: Date;
  items: CreditNoteItem[];
  currency?: string;
  exchange_rate?: number;
}

export type CreditNoteInsert = Database['public']['Tables']['credit_notes']['Insert'];
export type CreditNoteItemInsert = Database['public']['Tables']['credit_note_items']['Insert'];
export type CreditNote = Database['public']['Tables']['credit_notes']['Row'];
export type CreditNoteWithRelations = CreditNote & {
  customers: {
    id: string;
    name: string;
    email: string | null;
  };
  invoices: {
    id: string;
    invoice_number: string;
  };
  credit_note_items: Array<{
    id: string;
    description: string;
    quantity: number;
    unit_price: number;
    tax_rate: number;
    line_total: number;
  }>;
};

/**
 * Generate a unique credit note number
 *
 * @returns A unique credit note number
 */
export const generateCreditNoteNumber = (): string => {
  const prefix = 'CN';
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}-${timestamp}-${random}`;
};

/**
 * Create a new credit note
 *
 * @param creditNoteData Credit note form data
 * @param companyId Company ID
 * @param customerId Customer ID
 * @param userId User ID of the creator
 * @returns The created credit note or null if there was an error
 */
export const createCreditNote = async (
  creditNoteData: CreditNoteFormValues,
  companyId: string,
  customerId: string,
  userId: string
): Promise<CreditNote | null> => {
  try {
    // Calculate totals
    const subtotal = creditNoteData.items.reduce(
      (sum, item) => sum + (item.quantity * item.unit_price),
      0
    );

    const taxAmount = creditNoteData.items.reduce(
      (sum, item) => sum + (item.quantity * item.unit_price * (item.tax_rate / 100)),
      0
    );

    const totalAmount = subtotal + taxAmount;

    // Prepare the credit note data for insertion
    const creditNoteInsert: CreditNoteInsert = {
      id: uuidv4(),
      credit_note_number: generateCreditNoteNumber(),
      company_id: companyId,
      customer_id: customerId,
      invoice_id: creditNoteData.invoice_id,
      status: 'draft',
      issue_date: format(creditNoteData.issue_date, 'yyyy-MM-dd'),
      amount: subtotal,
      tax_amount: taxAmount,
      total_amount: totalAmount,
      reason: creditNoteData.reason,
      notes: creditNoteData.notes || null,
      created_by: userId,
      currency: creditNoteData.currency || 'UGX',
      exchange_rate: creditNoteData.exchange_rate || 1
    };

    // Insert the credit note
    const { data: creditNote, error } = await supabase
      .from('credit_notes')
      .insert(creditNoteInsert)
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Create credit note items
    if (creditNoteData.items.length > 0) {
      const itemsToInsert = creditNoteData.items.map(item => ({
        company_id: companyId,
        credit_note_id: creditNote.id,
        invoice_item_id: item.invoice_item_id,
        description: item.description,
        quantity: item.quantity,
        unit_price: item.unit_price,
        tax_rate: item.tax_rate
      }));

      const { error: itemsError } = await supabase
        .from('credit_note_items')
        .insert(itemsToInsert);

      if (itemsError) {
        throw itemsError;
      }
    }

    // Create approval history record
    await supabase
      .from('credit_note_approval_history')
      .insert({
        credit_note_id: creditNote.id,
        from_status: 'none',
        to_status: 'draft',
        changed_by: userId,
        notes: 'Credit note created'
      });

    showSuccessToast(
      'Credit Note Created',
      `Credit note ${creditNote.credit_note_number} has been created successfully.`
    );

    return creditNote;
  } catch (error: unknown) {
    console.error('Error creating credit note:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while creating the credit note';
    showErrorToast(
      'Failed to Create Credit Note',
      errorMessage
    );
    return null;
  }
};

/**
 * Update credit note status
 *
 * @param creditNoteId Credit note ID
 * @param newStatus New status
 * @param userId User ID making the change
 * @param notes Optional notes about the status change
 * @returns True if successful, false otherwise
 */
export const updateCreditNoteStatus = async (
  creditNoteId: string,
  newStatus: CreditNoteStatus,
  userId: string,
  notes?: string
): Promise<boolean> => {
  try {
    // Get current status
    const { data: creditNote, error: fetchError } = await supabase
      .from('credit_notes')
      .select('status, credit_note_number')
      .eq('id', creditNoteId)
      .single();

    if (fetchError) throw fetchError;
    if (!creditNote) throw new Error('Credit note not found');

    const oldStatus = creditNote.status;

    // Update credit note status
    const updateData: Partial<CreditNote> = {
      status: newStatus,
      updated_at: new Date().toISOString()
    };

    // If approving, add approval info
    if (newStatus === 'approved') {
      updateData.approved_by = userId;
      updateData.approved_at = new Date().toISOString();
    }

    const { error: updateError } = await supabase
      .from('credit_notes')
      .update(updateData)
      .eq('id', creditNoteId);

    if (updateError) throw updateError;

    // Create status history record
    await supabase
      .from('credit_note_approval_history')
      .insert({
        credit_note_id: creditNoteId,
        from_status: oldStatus,
        to_status: newStatus,
        changed_by: userId,
        notes: notes || `Status changed from ${oldStatus} to ${newStatus}`
      });

    // If approved, update the invoice status to credited
    if (newStatus === 'approved') {
      const { data: creditNoteData, error: cnError } = await supabase
        .from('credit_notes')
        .select('invoice_id')
        .eq('id', creditNoteId)
        .single();

      if (!cnError && creditNoteData && creditNoteData.invoice_id) {
        await supabase
          .from('invoices')
          .update({
            status: 'credited',
            updated_at: new Date().toISOString()
          })
          .eq('id', creditNoteData.invoice_id);
      }
    }

    showSuccessToast(
      'Credit Note Updated',
      `Credit note ${creditNote.credit_note_number} status changed to ${newStatus}.`
    );

    return true;
  } catch (error: unknown) {
    console.error('Error updating credit note status:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while updating the credit note status';
    showErrorToast(
      'Failed to Update Credit Note',
      errorMessage
    );
    return false;
  }
};
