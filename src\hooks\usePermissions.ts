
import { useAuth } from '@/context/AuthContext';
import { UserRole } from '@/types/auth';

/**
 * Hook for checking user permissions based on roles
 *
 * This hook provides utility functions to check if the current user
 * has specific roles or permissions within the application.
 *
 * @example
 * ```tsx
 * const { canCreateInvoices, isAdmin } = usePermissions();
 *
 * if (canCreateInvoices()) {
 *   // Show create invoice button
 * }
 * ```
 *
 * @returns Object containing role and permission check functions
 */
export function usePermissions() {
  const { roles } = useAuth();

  /**
   * Check if the user has a specific role
   *
   * @param role - The role to check
   * @returns True if the user has the specified role
   */
  const hasRole = (role: UserRole): boolean => {
    return roles.includes(role);
  };

  /**
   * Check if the user has the admin role
   *
   * @returns True if the user is an admin
   */
  const isAdmin = (): boolean => {
    return hasRole('admin');
  };

  /**
   * Check if the user has the accountant role
   *
   * @returns True if the user is an accountant
   */
  const isAccountant = (): boolean => {
    return hasRole('accountant');
  };

  /**
   * Check if the user has the manager role
   *
   * @returns True if the user is a manager
   */
  const isManager = (): boolean => {
    return hasRole('manager');
  };

  /**
   * Check if the user has the viewer role
   *
   * @returns True if the user is a viewer
   */
  const isViewer = (): boolean => {
    return hasRole('viewer');
  };

  /**
   * Check if the user has any of the specified roles
   *
   * @param allowedRoles - Array of roles to check against
   * @returns True if the user has any of the specified roles
   */
  const hasAnyRole = (allowedRoles: UserRole[]): boolean => {
    return roles.some(role => allowedRoles.includes(role));
  };

  /**
   * Check if the user can manage users
   *
   * @returns True if the user can manage users (admin only)
   */
  const canManageUsers = (): boolean => {
    return isAdmin();
  };

  /**
   * Check if the user can manage company settings
   *
   * @returns True if the user can manage company settings (admin only)
   */
  const canManageCompany = (): boolean => {
    return isAdmin();
  };

  /**
   * Check if the user can approve transactions
   *
   * @returns True if the user can approve transactions (admin or accountant)
   */
  const canApproveTransactions = (): boolean => {
    return hasAnyRole(['admin', 'accountant']);
  };

  /**
   * Check if the user can create invoices
   *
   * @returns True if the user can create invoices (admin, accountant, or manager)
   */
  const canCreateInvoices = (): boolean => {
    return hasAnyRole(['admin', 'accountant', 'manager']);
  };

  /**
   * Check if the user can delete invoices
   *
   * @returns True if the user can delete invoices (admin or accountant)
   */
  const canDeleteInvoices = (): boolean => {
    return hasAnyRole(['admin', 'accountant']);
  };

  /**
   * Check if the user can view financial reports
   *
   * @returns True if the user can view reports (admin, accountant, or manager)
   */
  const canViewReports = (): boolean => {
    return hasAnyRole(['admin', 'accountant', 'manager']);
  };

  return {
    hasRole,
    isAdmin,
    isAccountant,
    isManager,
    isViewer,
    hasAnyRole,
    canManageUsers,
    canManageCompany,
    canApproveTransactions,
    canCreateInvoices,
    canDeleteInvoices,
    canViewReports
  };
}
