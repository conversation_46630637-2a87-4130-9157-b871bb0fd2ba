
import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Search, Plus } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';

// Custom components
import { BudgetRequestForm } from '@/components/budgets/BudgetRequestForm';
import { BudgetSummaryCards } from '@/components/budgets/BudgetSummaryCards';
import { BudgetRequestsTable } from '@/components/budgets/BudgetRequestsTable';
import { BudgetPagination } from '@/components/budgets/BudgetPagination';

// Data and utilities
import {
  BudgetRequest,
  fetchBudgetRequests,
  getSummaryMetrics,
  getPageNumbers,
  formatCurrency
} from '@/data/budgetData';

const Budgets = (): React.JSX.Element => {
  const { currentCompanyId } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [budgetRequests, setBudgetRequests] = useState<BudgetRequest[]>([]);
  const [summaryMetrics, setSummaryMetrics] = useState({
    totalBudgetRequests: 0,
    approvedBudgets: 0,
    pendingBudgets: 0,
    totalApprovedAmount: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const itemsPerPage = 5; // Number of items to display per page

  // Fetch budget requests and summary metrics
  useEffect(() => {
    const fetchData = async () => {
      if (!currentCompanyId) return;

      setIsLoading(true);
      try {
        // Fetch budget requests
        const requests = await fetchBudgetRequests(currentCompanyId);
        setBudgetRequests(requests);

        // Get summary metrics
        const metrics = await getSummaryMetrics(currentCompanyId);
        setSummaryMetrics(metrics);
      } catch (error) {
        console.error('Error fetching budget data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [currentCompanyId]);

  // Filter budget requests based on search term
  const filteredRequests = budgetRequests.filter(request =>
    request.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    request.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
    request.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    request.submittedBy.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Calculate pagination values
  const totalPages = Math.ceil(filteredRequests.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredRequests.slice(indexOfFirstItem, indexOfLastItem);

  // Handle page change
  const handlePageChange = (page: number): void => {
    setCurrentPage(page);
  };

  // Get page numbers for pagination
  const pageNumbers = getPageNumbers(currentPage, totalPages);

  // Function to fetch data when a new budget request is created
  const handleBudgetCreated = async (): Promise<void> => {
    if (!currentCompanyId) return;

    try {
      // Fetch budget requests
      const requests = await fetchBudgetRequests(currentCompanyId);
      setBudgetRequests(requests);

      // Get summary metrics
      const metrics = await getSummaryMetrics(currentCompanyId);
      setSummaryMetrics(metrics);

      // Close the dialog
      setDialogOpen(false);
    } catch (error) {
      console.error('Error refreshing budget data:', error);
    }
  };

  return (
    <div>
      {/* Page header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="page-title mb-1">Budget Management</h1>
          <p className="text-muted-foreground">
            Track, review, and approve budget requests
          </p>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button className="whitespace-nowrap">
              <Plus className="mr-1" /> New Budget Request
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[550px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Budget Request</DialogTitle>
              <DialogDescription>
                Fill out the form below to submit a new budget request for approval.
              </DialogDescription>
            </DialogHeader>
            <BudgetRequestForm onClose={handleBudgetCreated} />
          </DialogContent>
        </Dialog>
      </div>

      {/* Summary metrics */}
      <BudgetSummaryCards
        summaryMetrics={summaryMetrics}
        formatCurrency={formatCurrency}
      />

      {/* Budget requests table */}
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
            <CardTitle>Budget Requests</CardTitle>
            <div className="relative w-full md:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search budget requests..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="py-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-sm text-muted-foreground">Loading budget requests...</p>
            </div>
          ) : currentItems.length === 0 ? (
            <div className="py-8 text-center">
              <p className="text-muted-foreground">No budget requests found.</p>
            </div>
          ) : (
            <BudgetRequestsTable
              requests={currentItems}
              formatCurrency={formatCurrency}
            />
          )}
        </CardContent>
        {!isLoading && filteredRequests.length > 0 && (
          <div className="p-6 pt-0">
            <BudgetPagination
              currentPage={currentPage}
              totalPages={totalPages}
              pageNumbers={pageNumbers}
              indexOfFirstItem={indexOfFirstItem}
              indexOfLastItem={indexOfLastItem}
              totalItems={filteredRequests.length}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </Card>
    </div>
  );
};

export default Budgets;
