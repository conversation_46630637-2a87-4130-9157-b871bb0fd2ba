import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { exportToCSV, formatDateForCSV, formatCurrencyForCSV } from '@/utils/csv-export-utils';
import { format } from 'date-fns';
import { useToast } from '@/hooks/use-toast';

interface AccountStatementEntry {
  date: string;
  description: string;
  reference?: string;
  debit: number;
  credit: number;
  balance: number;
}

interface ExportAccountStatementButtonProps {
  accountId: string;
  accountName: string;
  statementData: AccountStatementEntry[];
  startDate: Date;
  endDate: Date;
  isLoading?: boolean;
}

/**
 * Button component for exporting account statements to CSV
 */
export function ExportAccountStatementButton({
  accountId,
  accountName,
  statementData,
  startDate,
  endDate,
  isLoading = false
}: ExportAccountStatementButtonProps) {
  const [exporting, setExporting] = useState(false);
  const { toast } = useToast();

  // Define headers for the CSV file
  const headers = {
    date: 'Date',
    description: 'Description',
    reference: 'Reference',
    debit: 'Debit (UGX)',
    credit: 'Credit (UGX)',
    balance: 'Balance (UGX)'
  };

  const handleExport = async () => {
    try {
      setExporting(true);

      // Format statement data for CSV export
      const formattedStatementData = statementData.map(entry => {
        return {
          ...entry,
          date: formatDateForCSV(entry.date),
          debit: formatCurrencyForCSV(entry.debit),
          credit: formatCurrencyForCSV(entry.credit),
          balance: formatCurrencyForCSV(entry.balance)
        };
      });

      // Generate filename with account name and date range
      const formattedStartDate = format(startDate, 'yyyy-MM-dd');
      const formattedEndDate = format(endDate, 'yyyy-MM-dd');
      const safeAccountName = accountName.replace(/[^a-z0-9]/gi, '-').toLowerCase();
      const filename = `account-statement-${safeAccountName}-${formattedStartDate}-to-${formattedEndDate}.csv`;

      // Export to CSV
      exportToCSV(formattedStatementData, filename, headers);

      // Show success message
      toast({
        title: 'Export Successful',
        description: `Account statement exported to ${filename}`,
        variant: 'default'
      });
    } catch (error) {
      console.error('Error exporting account statement:', error);
      toast({
        title: 'Export Failed',
        description: 'There was an error exporting the account statement. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setExporting(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleExport}
      disabled={isLoading || exporting || statementData.length === 0}
    >
      <Download className="mr-2 h-4 w-4" />
      {exporting ? 'Exporting...' : 'Export Statement'}
    </Button>
  );
}
