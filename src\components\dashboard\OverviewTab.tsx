
import { StatCard } from "@/components/dashboard/StatCard";
import { CashFlowChart } from "@/components/dashboard/CashFlowChart";
import { RecentTransactions } from "@/components/dashboard/RecentTransactions";
import { ActionableInsights } from "@/components/dashboard/ActionableInsights";
import { DollarSign, ArrowUp, ArrowDown, User } from "lucide-react";
import { useCompanyStats } from "@/hooks/useCompanyStats";
import { Skeleton } from "@/components/ui/skeleton";

export const OverviewTab = (): React.JSX.Element => {
  const { stats, isLoading } = useCompanyStats();

  // Format currency for display
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat("en-UG", {
      style: "currency",
      currency: "UGX",
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 my-8">
        {isLoading ? (
          // Loading skeletons
          Array(4).fill(0).map((_, i) => (
            <div key={i} className="bg-card border rounded-lg p-6 space-y-2">
              <Skeleton className="h-5 w-24 mb-4" />
              <Skeleton className="h-8 w-36" />
              <Skeleton className="h-4 w-40 mt-2" />
              <div className="mt-4">
                <Skeleton className="h-2 w-full rounded-full" />
              </div>
            </div>
          ))
        ) : (
          <>
            <StatCard
              title="Total Revenue"
              value={formatCurrency(stats.totalRevenue)}
              description={`Increased by ${stats.revenueChangePercent}% from last month`}
              icon={DollarSign}
              trend="up"
              progress={82}
            />
            <StatCard
              title="Payables Due"
              value={formatCurrency(stats.payablesDue)}
              description={`${stats.pendingInvoices} invoices pending payment`}
              icon={ArrowUp}
              trend="neutral"
              progress={45}
            />
            <StatCard
              title="Receivables"
              value={formatCurrency(stats.receivables)}
              description={`${stats.outstandingInvoices} invoices outstanding`}
              icon={ArrowDown}
              trend="neutral"
              progress={65}
            />
            <StatCard
              title="Active Clients"
              value={stats.activeClients.toString()}
              description={`+${stats.newClients} new clients this month`}
              icon={User}
              trend="up"
              progress={90}
            />
          </>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <CashFlowChart />
        <RecentTransactions />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <ActionableInsights />
        </div>
        <div className="lg:col-span-1">
          <iframe
            title="Calendar Preview"
            src="https://calendar.google.com/calendar/embed?height=300&wkst=1&bgcolor=%23ffffff&ctz=UTC&showTitle=0&showNav=0&showDate=0&showPrint=0&showTabs=0&showCalendars=0&showTz=0"
            className="w-full h-[300px] border-0 rounded-lg shadow-sm bg-white"
            frameBorder="0"
            scrolling="no"
          ></iframe>
        </div>
      </div>
    </>
  );
};
