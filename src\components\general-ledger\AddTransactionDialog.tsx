
import React, { useEffect } from 'react';
import {
  Dialog,
  DialogContent,
} from '@/components/ui/dialog';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AccountWithBalance, TransactionWithAccounts } from '@/types/index';
import { transactionSchema, TransactionFormValues } from './types';
import TransactionDialogHeader from './TransactionDialogHeader';
import TransactionDialogFooter from './TransactionDialogFooter';
import TransactionForm from './TransactionForm';
import { useTransactionSubmit } from './hooks/useTransactionSubmit';
import { transactionToFormValues } from './utils/transactionUtils';

interface AddTransactionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTransactionComplete: (transaction: TransactionWithAccounts) => void;
  editTransaction?: TransactionWithAccounts;
  accounts: AccountWithBalance[];
}

const AddTransactionDialog: React.FC<AddTransactionDialogProps> = ({
  open,
  onOpenChange,
  onTransactionComplete,
  editTransaction,
  accounts,
}) => {
  // Initialize form with default values
  const form = useForm<TransactionFormValues>({
    resolver: zodResolver(transactionSchema),
    defaultValues: {
      date: new Date(),
      description: '',
      debitAccount: '',
      creditAccount: '',
      amount: 0,
      reference: '',
      status: undefined,
      currency: 'UGX',
    },
  });

  // Reset form when dialog opens for editing
  useEffect(() => {
    if (open && editTransaction) {
      // Convert transaction to form values
      const formValues = transactionToFormValues(editTransaction);
      form.reset(formValues);
    } else if (open && !editTransaction) {
      // Reset to default values
      form.reset({
        date: new Date(),
        description: '',
        debitAccount: '',
        creditAccount: '',
        amount: 0,
        reference: '',
        status: undefined,
        currency: 'UGX',
      });
    }
  }, [open, editTransaction, form]);

  const handleCancel = (): void => {
    onOpenChange(false);
  };

  const { isLoading, handleFormSubmit } = useTransactionSubmit({
    onTransactionComplete,
    onDialogClose: () => onOpenChange(false),
    editTransaction,
    form
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px] max-h-[90vh]">
        <TransactionDialogHeader editTransaction={editTransaction} />

        <ScrollArea className="max-h-[60vh]">
          <div className="p-1"> {/* Add padding to avoid scroll cut-off */}
            <TransactionForm
              form={form}
              accounts={accounts}
              isSubmitting={isLoading}
              onSubmit={handleFormSubmit}
              onCancel={handleCancel}
            />
          </div>
        </ScrollArea>

        <TransactionDialogFooter
          isLoading={isLoading}
          onCancel={handleCancel}
          onSubmit={handleFormSubmit}
          editTransaction={editTransaction}
        />
      </DialogContent>
    </Dialog>
  );
};

export default AddTransactionDialog;
