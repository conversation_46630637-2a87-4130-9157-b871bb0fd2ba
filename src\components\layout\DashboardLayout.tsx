
import { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import Header from './Header';
import EnhancedSidebar from './EnhancedSidebar';
import EnhancedMobileSidebar from './EnhancedMobileSidebar';
import BottomNavigation from './BottomNavigation';
import { SidebarProvider } from '@/components/ui/sidebar';

const DashboardLayout = (): React.JSX.Element => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const isMobile = useIsMobile();

  const toggleSidebar = (): void => {
    setSidebarOpen(!sidebarOpen);
  };

  // Close sidebar when switching from mobile to desktop
  useEffect(() => {
    if (!isMobile) {
      setSidebarOpen(false);
    }
  }, [isMobile]);

  // Add padding to the bottom on mobile for the bottom navigation
  const mainContentStyle = isMobile ? { paddingBottom: '4rem' } : {};

  return (
    <SidebarProvider defaultOpen={!isMobile}>
      <div className="min-h-screen flex flex-col">
        <Header toggleSidebar={toggleSidebar} />
        <div className="flex-1 flex">
          {/* Desktop sidebar */}
          {!isMobile && <EnhancedSidebar open={sidebarOpen} setOpen={setSidebarOpen} />}

          {/* Mobile sidebar - rendered conditionally */}
          {isMobile && <EnhancedMobileSidebar open={sidebarOpen} setOpen={setSidebarOpen} />}

          <main
            className="flex-1 transition-all duration-300 overflow-x-hidden"
            style={mainContentStyle}
          >
            <div className="page-container max-w-full">
              <Outlet />
            </div>
          </main>
        </div>

        {/* Mobile bottom navigation */}
        {isMobile && <BottomNavigation toggleSidebar={toggleSidebar} />}
      </div>
    </SidebarProvider>
  );
};

export default DashboardLayout;
