
import React from "react";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { ChartBar, LayoutGrid, LayoutList } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { usePermissions } from "@/hooks/usePermissions";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { useNavigate } from "react-router-dom";

interface DashboardHeaderProps {
  dashboardView: 'classic' | 'custom';
  onViewChange: (view: 'classic' | 'custom') => void;
}

export const DashboardHeader = ({
  dashboardView,
  onViewChange
}: DashboardHeaderProps): React.JSX.Element => {
  const { profile, companies, currentCompanyId } = useAuth();
  const { isAdmin, isAccountant, isManager, isViewer } = usePermissions();
  const navigate = useNavigate();

  const userName = profile?.first_name && profile?.last_name
    ? `${profile.first_name} ${profile.last_name}`
    : "User";

  const currentCompanyName = companies.find(c => c.id === currentCompanyId)?.name || "Your Company";

  // Determine the current user role for display
  const getRoleBadgeVariant = (): { label: string; variant: string } => {
    if (isAdmin()) return { label: "Admin", variant: "default" };
    if (isAccountant()) return { label: "Accountant", variant: "secondary" };
    if (isManager()) return { label: "Manager", variant: "outline" };
    if (isViewer()) return { label: "Viewer", variant: "outline" };
    return { label: "User", variant: "outline" };
  };

  const roleBadge = getRoleBadgeVariant();

  return (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
      <div>
        <div className="flex items-center gap-2">
          <h1 className="page-title">Dashboard</h1>
          <Badge variant={roleBadge.variant as "default" | "secondary" | "outline"} className="ml-2">
            {roleBadge.label}
          </Badge>
        </div>
        <p className="page-subtitle">
          <span className="font-medium">{currentCompanyName}</span> - Welcome back, {userName}!
          Here's an overview of your financial status.
        </p>
      </div>
      <div className="mt-4 md:mt-0 flex items-center gap-3">
        <ToggleGroup type="single" value={dashboardView} onValueChange={(value) => value && onViewChange(value as 'classic' | 'custom')}>
          <ToggleGroupItem value="classic" aria-label="Classic View">
            <LayoutList className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">Classic</span>
          </ToggleGroupItem>
          <ToggleGroupItem value="custom" aria-label="Custom View">
            <LayoutGrid className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">Custom</span>
          </ToggleGroupItem>
        </ToggleGroup>

        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate('/reports')}
        >
          <ChartBar className="h-4 w-4 mr-1" />
          <span className="hidden sm:inline">View</span> Reports
        </Button>
      </div>
    </div>
  );
};
