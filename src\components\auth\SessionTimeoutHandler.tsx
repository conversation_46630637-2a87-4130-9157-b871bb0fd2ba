import { useState, useEffect, useRef, useCallback } from 'react';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';

const DEFAULT_TIMEOUT = 30 * 60 * 1000;
const WARNING_BEFORE_TIMEOUT = 2 * 60 * 1000;
const ACTIVITY_EVENTS = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

interface SessionTimeoutHandlerProps {
  timeout?: number;
  warningTime?: number;
}

export function SessionTimeoutHandler({
  timeout = DEFAULT_TIMEOUT,
  warningTime = WARNING_BEFORE_TIMEOUT,
}: SessionTimeoutHandlerProps) {
  const { session, signOut } = useAuth();
  const { toast } = useToast();

  const [showWarning, setShowWarning] = useState<boolean>(false);
  const [timeRemaining, setTimeRemaining] = useState<number>(0);

  const activityTimerRef = useRef<number | null>(null);
  const warningTimerRef = useRef<number | null>(null);
  const countdownIntervalRef = useRef<number | null>(null);

  const resetActivityTimer = useCallback(() => {
    if (activityTimerRef.current) {
      window.clearTimeout(activityTimerRef.current);
    }

    if (warningTimerRef.current) {
      window.clearTimeout(warningTimerRef.current);
    }

    if (countdownIntervalRef.current) {
      window.clearInterval(countdownIntervalRef.current);
    }

    warningTimerRef.current = window.setTimeout(() => {
      setShowWarning(true);
      setTimeRemaining(warningTime);

      countdownIntervalRef.current = window.setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1000) {
            if (countdownIntervalRef.current) {
              window.clearInterval(countdownIntervalRef.current);
            }
            return 0;
          }
          return prev - 1000;
        });
      }, 1000);
    }, timeout - warningTime);

    activityTimerRef.current = window.setTimeout(async () => {
      await handleSessionTimeout();
    }, timeout);
  }, [timeout, warningTime]);

  const handleSessionTimeout = async (): Promise<void> => {
    try {
      if (activityTimerRef.current) {
        window.clearTimeout(activityTimerRef.current);
        activityTimerRef.current = null;
      }

      if (warningTimerRef.current) {
        window.clearTimeout(warningTimerRef.current);
        warningTimerRef.current = null;
      }

      if (countdownIntervalRef.current) {
        window.clearInterval(countdownIntervalRef.current);
        countdownIntervalRef.current = null;
      }

      await signOut();

      toast({
        title: 'Session Expired',
        description: 'Your session has expired due to inactivity.',
        variant: 'destructive',
      });
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const extendSession = (): void => {
    setShowWarning(false);
    resetActivityTimer();
  };

  const formatTimeRemaining = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  useEffect(() => {
    if (!session) return;

    let isMounted = true;

    const safeResetActivityTimer = (): void => {
      if (isMounted) {
        resetActivityTimer();
      }
    };

    safeResetActivityTimer();

    ACTIVITY_EVENTS.forEach(event => {
      window.addEventListener(event, safeResetActivityTimer);
    });

    return () => {
      isMounted = false;

      if (activityTimerRef.current) {
        window.clearTimeout(activityTimerRef.current);
        activityTimerRef.current = null;
      }

      if (warningTimerRef.current) {
        window.clearTimeout(warningTimerRef.current);
        warningTimerRef.current = null;
      }

      if (countdownIntervalRef.current) {
        window.clearInterval(countdownIntervalRef.current);
        countdownIntervalRef.current = null;
      }

      ACTIVITY_EVENTS.forEach(event => {
        window.removeEventListener(event, safeResetActivityTimer);
      });
    };
  }, [session, resetActivityTimer]);

  useEffect(() => {
    if (timeRemaining === 0 && showWarning) {
      handleSessionTimeout();
    }
  }, [timeRemaining, showWarning]);

  return (
    <AlertDialog open={showWarning} onOpenChange={setShowWarning}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Session Timeout Warning</AlertDialogTitle>
          <AlertDialogDescription>
            Your session will expire in {formatTimeRemaining(timeRemaining)} due to inactivity.
            Would you like to continue working?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleSessionTimeout}>
            Logout Now
          </AlertDialogCancel>
          <AlertDialogAction onClick={extendSession}>
            Continue Session
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}