import { useState } from 'react';
import { CheckCircle, XCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';

interface BillApprovalDialogProps {
  billId: string;
  billNumber: string;
  onApprove: (billId: string, notes: string) => Promise<void>;
  onReject: (billId: string, notes: string) => Promise<void>;
  isSubmitting: boolean;
}

/**
 * Dialog for approving or rejecting a bill
 */
export function BillApprovalDialog({
  billId,
  billNumber,
  onApprove,
  onReject,
  isSubmitting
}: BillApprovalDialogProps) {
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [rejectionDialogOpen, setRejectionDialogOpen] = useState(false);
  const [notes, setNotes] = useState('');

  // Handle approval
  const handleApprove = async () => {
    await onApprove(billId, notes);
    setApprovalDialogOpen(false);
    setNotes('');
  };

  // Handle rejection
  const handleReject = async () => {
    await onReject(billId, notes);
    setRejectionDialogOpen(false);
    setNotes('');
  };

  return (
    <div className="flex space-x-2">
      {/* Approve Dialog */}
      <Dialog open={approvalDialogOpen} onOpenChange={setApprovalDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className="bg-green-50 text-green-600 hover:bg-green-100 hover:text-green-700 border-green-200">
            <CheckCircle className="mr-2 h-4 w-4" />
            Approve
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Bill</DialogTitle>
            <DialogDescription>
              Are you sure you want to approve bill #{billNumber}? This will mark the bill as approved for payment.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Add approval notes (optional)"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setApprovalDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleApprove} 
              disabled={isSubmitting}
              className="bg-green-600 hover:bg-green-700"
            >
              {isSubmitting ? 'Approving...' : 'Approve Bill'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={rejectionDialogOpen} onOpenChange={setRejectionDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className="bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700 border-red-200">
            <XCircle className="mr-2 h-4 w-4" />
            Reject
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Bill</DialogTitle>
            <DialogDescription>
              Are you sure you want to reject bill #{billNumber}? This will mark the bill as cancelled.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Add rejection reason (recommended)"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setRejectionDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleReject} 
              disabled={isSubmitting}
              variant="destructive"
            >
              {isSubmitting ? 'Rejecting...' : 'Reject Bill'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
