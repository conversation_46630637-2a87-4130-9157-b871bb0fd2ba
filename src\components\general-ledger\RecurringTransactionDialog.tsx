import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { RecurringTransactionForm } from './RecurringTransactionForm';
import { AccountWithBalance } from '@/types/index';
import { RecurringTransactionFormValues, RecurringTransactionWithItems } from '@/types/recurring-transactions';
import { useRecurringTransactions } from '@/hooks/useRecurringTransactions';

interface RecurringTransactionDialogProps {
  accounts: AccountWithBalance[];
  onSuccess?: () => void;
  trigger?: React.ReactNode;
  defaultOpen?: boolean;
  existingTransaction?: RecurringTransactionWithItems;
}

export const RecurringTransactionDialog: React.FC<RecurringTransactionDialogProps> = ({
  accounts,
  onSuccess,
  trigger,
  defaultOpen = false,
  existingTransaction,
}) => {
  const [open, setOpen] = React.useState(defaultOpen);
  const { createRecurringTransaction, updateRecurringTransaction } = useRecurringTransactions();

  const isEditing = !!existingTransaction;

  // Map existing transaction to form values if editing
  const defaultValues = React.useMemo(() => {
    if (!existingTransaction) return undefined;

    // Find debit and credit accounts
    const debitItem = existingTransaction.items.find(item => item.debit > 0);
    const creditItem = existingTransaction.items.find(item => item.credit > 0);

    return {
      name: existingTransaction.name,
      description: existingTransaction.description || '',
      frequency: existingTransaction.frequency,
      startDate: new Date(existingTransaction.start_date),
      endDate: existingTransaction.end_date ? new Date(existingTransaction.end_date) : undefined,
      dayOfMonth: existingTransaction.day_of_month || undefined,
      dayOfWeek: existingTransaction.day_of_week || undefined,
      debitAccount: debitItem?.account_id || '',
      creditAccount: creditItem?.account_id || '',
      amount: debitItem?.debit || 0,
      reference: existingTransaction.description || '',
    };
  }, [existingTransaction]);

  const handleSubmit = async (data: RecurringTransactionFormValues) => {
    let success = false;

    if (isEditing && existingTransaction) {
      success = await updateRecurringTransaction(existingTransaction.id, data);
    } else {
      const result = await createRecurringTransaction(data);
      success = !!result;
    }

    if (success) {
      setOpen(false);
      if (onSuccess) onSuccess();
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {trigger ? (
        <DialogTrigger asChild>
          {trigger}
        </DialogTrigger>
      ) : (
        <DialogTrigger asChild>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            {isEditing ? 'Edit Recurring Transaction' : 'New Recurring Transaction'}
          </Button>
        </DialogTrigger>
      )}
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Recurring Transaction' : 'Create Recurring Transaction'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update the details of this recurring transaction.' 
              : 'Set up a transaction that will automatically repeat on a schedule.'}
          </DialogDescription>
        </DialogHeader>
        <RecurringTransactionForm
          accounts={accounts}
          onSubmit={handleSubmit}
          defaultValues={defaultValues}
          isEditing={isEditing}
        />
      </DialogContent>
    </Dialog>
  );
};
