import React from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>eader, 
  <PERSON><PERSON>Title, 
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { WidgetType } from '@/types/dashboard';
import { 
  BarChart3, 
  DollarSign, 
  ClipboardList, 
  PieChart, 
  Users, 
  Calendar, 
  TrendingUp,
  Wallet
} from 'lucide-react';

interface WidgetSelectorProps {
  onSelect: (widgetType: WidgetType) => void;
  onCancel: () => void;
}

interface WidgetOption {
  type: WidgetType;
  title: string;
  description: string;
  icon: React.ReactNode;
}

export function WidgetSelector({ onSelect, onCancel }: WidgetSelectorProps): JSX.Element {
  const widgetOptions: WidgetOption[] = [
    {
      type: 'financial_overview',
      title: 'Financial Overview',
      description: 'Key financial metrics at a glance',
      icon: <DollarSign className="h-5 w-5" />
    },
    {
      type: 'cash_flow',
      title: 'Cash Flow',
      description: 'Visualize cash inflows and outflows',
      icon: <BarChart3 className="h-5 w-5" />
    },
    {
      type: 'recent_transactions',
      title: 'Recent Transactions',
      description: 'View your most recent transactions',
      icon: <ClipboardList className="h-5 w-5" />
    },
    {
      type: 'account_balance',
      title: 'Account Balance',
      description: 'Summary of your account balances',
      icon: <Wallet className="h-5 w-5" />
    },
    {
      type: 'expense_breakdown',
      title: 'Expense Breakdown',
      description: 'Analyze expenses by category',
      icon: <PieChart className="h-5 w-5" />
    },
    {
      type: 'top_customers',
      title: 'Top Customers',
      description: 'Your highest-value customers',
      icon: <Users className="h-5 w-5" />
    },
    {
      type: 'tax_calendar',
      title: 'Tax Calendar',
      description: 'Upcoming tax deadlines and obligations',
      icon: <Calendar className="h-5 w-5" />
    },
    {
      type: 'budget_progress',
      title: 'Budget Progress',
      description: 'Track actual spending against budget',
      icon: <TrendingUp className="h-5 w-5" />
    }
  ];

  return (
    <Dialog open={true} onOpenChange={() => onCancel()}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add Widget</DialogTitle>
          <DialogDescription>
            Select a widget to add to your dashboard
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
          {widgetOptions.map((option) => (
            <Button
              key={option.type}
              variant="outline"
              className="h-auto p-4 justify-start flex flex-col items-start text-left"
              onClick={() => onSelect(option.type)}
            >
              <div className="flex items-center mb-2">
                <div className="mr-2 p-1.5 rounded-full bg-primary/10 text-primary">
                  {option.icon}
                </div>
                <span className="font-medium">{option.title}</span>
              </div>
              <p className="text-sm text-muted-foreground">{option.description}</p>
            </Button>
          ))}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onCancel}>Cancel</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
