import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import * as z from 'zod';
import { format, addMonths, startOfMonth, endOfMonth, isValid } from 'date-fns';

// Fiscal year settings schema
const fiscalYearSchema = z.object({
  // Fiscal year type
  fiscal_year_type: z.enum(['calendar', 'custom']),

  // Fiscal year start month (1-12)
  fiscal_year_start_month: z.coerce.number().min(1).max(12),

  // Fiscal year start day (1-31)
  fiscal_year_start_day: z.coerce.number().min(1).max(31),

  // Custom fiscal year start date
  custom_start_date: z.date().optional(),

  // Auto-adjust reports to fiscal year
  adjust_reports_to_fiscal_year: z.boolean().default(true),
});

type FiscalYearFormValues = z.infer<typeof fiscalYearSchema>;

/**
 * Fiscal Year Settings component
 *
 * Allows configuring company-specific fiscal year settings
 */
export function FiscalYearSettings(): JSX.Element {
  const { toast } = useToast();
  const { currentCompanyId } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Initialize form with default values
  const form = useForm<FiscalYearFormValues>({
    resolver: zodResolver(fiscalYearSchema),
    defaultValues: {
      fiscal_year_type: 'calendar',
      fiscal_year_start_month: 1, // January
      fiscal_year_start_day: 1,
      adjust_reports_to_fiscal_year: true,
    },
  });

  // Watch form values for conditional rendering
  const fiscalYearType = form.watch('fiscal_year_type');
  const startMonth = form.watch('fiscal_year_start_month');
  const startDay = form.watch('fiscal_year_start_day');

  // Calculate fiscal year end date
  const calculateFiscalYearEnd = (startMonth: number, startDay: number): string => {
    try {
      // Create a date object for the start date in the current year
      const currentYear = new Date().getFullYear();
      const startDate = new Date(currentYear, startMonth - 1, startDay);

      if (!isValid(startDate)) {
        return 'Invalid date';
      }

      // End date is 1 year (12 months) after start date, minus 1 day
      const endDate = new Date(startDate);
      endDate.setFullYear(endDate.getFullYear() + 1);
      endDate.setDate(endDate.getDate() - 1);

      return format(endDate, 'MMMM d');
    } catch (error) {
      console.error('Error calculating fiscal year end:', error);
      return 'Error calculating end date';
    }
  };

  // Fetch company fiscal year settings
  useEffect(() => {
    const fetchSettings = async (): Promise<void> => {
      if (!currentCompanyId) return;

      try {
        setLoading(true);

        // Fetch company data
        const { data: company, error: companyError } = await supabase
          .from('companies')
          .select('fiscal_year_start')
          .eq('id', currentCompanyId)
          .single();

        if (companyError) {
          throw companyError;
        }

        // Fetch company settings
        const { data: settings, error: settingsError } = await supabase
          .from('company_settings')
          .select('*')
          .eq('company_id', currentCompanyId)
          .single();

        if (settingsError && settingsError.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
          throw settingsError;
        }

        // Parse fiscal year settings
        if (company?.fiscal_year_start) {
          const fiscalYearStart = new Date(company.fiscal_year_start);

          // Check if it's a calendar year (Jan 1) or custom
          const isCalendarYear = fiscalYearStart.getMonth() === 0 && fiscalYearStart.getDate() === 1;

          form.reset({
            fiscal_year_type: isCalendarYear ? 'calendar' : 'custom',
            fiscal_year_start_month: fiscalYearStart.getMonth() + 1, // 0-based to 1-based
            fiscal_year_start_day: fiscalYearStart.getDate(),
            custom_start_date: isCalendarYear ? undefined : fiscalYearStart,
            adjust_reports_to_fiscal_year: settings?.adjust_reports_to_fiscal_year ?? true,
          });
        }
      } catch (error) {
        console.error('Error fetching fiscal year settings:', error);
        toast({
          title: 'Error',
          description: 'Failed to load fiscal year settings',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [currentCompanyId, form, toast]);

  // Handle form submission
  const onSubmit = async (data: FiscalYearFormValues): Promise<void> => {
    if (!currentCompanyId) {
      toast({
        title: 'Error',
        description: 'No company selected',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSaving(true);

      // Calculate fiscal year start date
      let fiscalYearStart: Date;

      if (data.fiscal_year_type === 'calendar') {
        // Calendar year starts on January 1
        fiscalYearStart = new Date(new Date().getFullYear(), 0, 1);
      } else if (data.custom_start_date) {
        // Use custom date but only keep month and day
        const customDate = new Date(data.custom_start_date);
        fiscalYearStart = new Date(new Date().getFullYear(), customDate.getMonth(), customDate.getDate());
      } else {
        // Use specified month and day
        fiscalYearStart = new Date(new Date().getFullYear(), data.fiscal_year_start_month - 1, data.fiscal_year_start_day);
      }

      // Update company fiscal_year_start
      const { error: companyError } = await supabase
        .from('companies')
        .update({
          fiscal_year_start: fiscalYearStart.toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', currentCompanyId);

      if (companyError) {
        throw companyError;
      }

      // Update company settings
      const { error: settingsError } = await supabase
        .from('company_settings')
        .upsert({
          company_id: currentCompanyId,
          adjust_reports_to_fiscal_year: data.adjust_reports_to_fiscal_year,
          updated_at: new Date().toISOString(),
        });

      if (settingsError) {
        throw settingsError;
      }

      toast({
        title: 'Success',
        description: 'Fiscal year settings updated successfully',
      });
    } catch (error: any) {
      console.error('Error saving fiscal year settings:', error);

      toast({
        title: 'Error',
        description: error.message || 'Failed to save fiscal year settings',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Fiscal Year Settings</CardTitle>
        <CardDescription>
          Configure your company's fiscal year start and end dates
        </CardDescription>
      </CardHeader>

      <CardContent>
        {loading ? (
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="fiscal_year_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Fiscal Year Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select fiscal year type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="calendar">Calendar Year (Jan 1 - Dec 31)</SelectItem>
                        <SelectItem value="custom">Custom Fiscal Year</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose between calendar year or custom fiscal year
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {fiscalYearType === 'custom' && (
                <div className="space-y-4">
                  <Separator />
                  <h3 className="text-sm font-medium">Custom Fiscal Year Start</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="fiscal_year_start_month"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Start Month</FormLabel>
                          <Select
                            onValueChange={(value) => field.onChange(parseInt(value))}
                            value={field.value.toString()}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select month" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="1">January</SelectItem>
                              <SelectItem value="2">February</SelectItem>
                              <SelectItem value="3">March</SelectItem>
                              <SelectItem value="4">April</SelectItem>
                              <SelectItem value="5">May</SelectItem>
                              <SelectItem value="6">June</SelectItem>
                              <SelectItem value="7">July</SelectItem>
                              <SelectItem value="8">August</SelectItem>
                              <SelectItem value="9">September</SelectItem>
                              <SelectItem value="10">October</SelectItem>
                              <SelectItem value="11">November</SelectItem>
                              <SelectItem value="12">December</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="fiscal_year_start_day"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Start Day</FormLabel>
                          <Select
                            onValueChange={(value) => field.onChange(parseInt(value))}
                            value={field.value.toString()}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select day" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Array.from({ length: 31 }, (_, i) => i + 1).map((day) => (
                                <SelectItem key={day} value={day.toString()}>
                                  {day}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="bg-muted/50 p-4 rounded-md">
                    <h4 className="font-medium mb-2">Fiscal Year Period</h4>
                    <p className="text-sm text-muted-foreground">
                      Your fiscal year will run from{' '}
                      <span className="font-medium">
                        {format(new Date(new Date().getFullYear(), startMonth - 1, startDay), 'MMMM d')}
                      </span>{' '}
                      to{' '}
                      <span className="font-medium">
                        {calculateFiscalYearEnd(startMonth, startDay)}
                      </span>
                    </p>
                  </div>
                </div>
              )}

              <Separator />

              <FormField
                control={form.control}
                name="adjust_reports_to_fiscal_year"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Adjust Reports to Fiscal Year</FormLabel>
                      <FormDescription>
                        Automatically adjust financial reports to align with your fiscal year
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <Button type="submit" disabled={saving}>
                {saving ? 'Saving...' : 'Save Settings'}
              </Button>
            </form>
          </Form>
        )}
      </CardContent>
    </Card>
  );
}
