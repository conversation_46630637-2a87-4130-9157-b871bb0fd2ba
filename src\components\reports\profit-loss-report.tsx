
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ProfitAndLossData } from '@/services/report-service';
import { format } from 'date-fns';

interface ProfitLossReportProps {
  data: ProfitAndLossData;
  startDate: Date;
  endDate: Date;
  formatCurrency: (amount: number) => string;
  isLoading?: boolean;
}

export const ProfitLossReport = ({ 
  data, 
  startDate,
  endDate,
  formatCurrency, 
  isLoading = false 
}: ProfitLossReportProps) => {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Profit & Loss</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-40 flex items-center justify-center">
            <p className="text-muted-foreground">Loading profit and loss data...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const formatDateRange = `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`;
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Profit & Loss</CardTitle>
        <p className="text-sm text-muted-foreground">{formatDateRange}</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Revenue Section */}
          <section>
            <h3 className="font-medium text-lg mb-2">Revenue</h3>
            <div className="space-y-1 border-b pb-2">
              {data.revenue.items.map((item, index) => (
                <div key={index} className="flex justify-between">
                  <span className="text-sm">{item.name}</span>
                  <span className="text-sm">{formatCurrency(item.amount)}</span>
                </div>
              ))}
              {data.revenue.items.length === 0 && (
                <div className="text-sm text-muted-foreground py-1">No revenue data available</div>
              )}
            </div>
            <div className="flex justify-between py-2 font-medium">
              <span>Total Revenue</span>
              <span>{formatCurrency(data.revenue.total)}</span>
            </div>
          </section>
          
          {/* Expenses Section */}
          <section>
            <h3 className="font-medium text-lg mb-2">Expenses</h3>
            <div className="space-y-1 border-b pb-2">
              {data.expenses.items.map((item, index) => (
                <div key={index} className="flex justify-between">
                  <span className="text-sm">{item.name}</span>
                  <span className="text-sm">{formatCurrency(item.amount)}</span>
                </div>
              ))}
              {data.expenses.items.length === 0 && (
                <div className="text-sm text-muted-foreground py-1">No expense data available</div>
              )}
            </div>
            <div className="flex justify-between py-2 font-medium">
              <span>Total Expenses</span>
              <span>{formatCurrency(data.expenses.total)}</span>
            </div>
          </section>
          
          {/* Net Income */}
          <section>
            <div className={`flex justify-between py-2 font-bold text-lg border-t ${data.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              <span>Net {data.netIncome >= 0 ? 'Income' : 'Loss'}</span>
              <span>{formatCurrency(data.netIncome)}</span>
            </div>
          </section>
        </div>
      </CardContent>
    </Card>
  );
};
