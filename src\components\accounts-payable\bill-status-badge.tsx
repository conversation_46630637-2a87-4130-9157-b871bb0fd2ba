import { Check<PERSON>ir<PERSON>2, <PERSON>, AlertCircle, ArrowDown, XCircle } from 'lucide-react';
import { BillStatus } from '@/types/index';

interface BillStatusBadgeProps {
  status: BillStatus;
}

/**
 * Component to display a bill status badge with appropriate color and icon
 */
export function BillStatusBadge({ status }: BillStatusBadgeProps) {
  // Get status badge color and icon
  const getStatusDetails = (status: BillStatus): { color: string; icon: React.ReactNode } => {
    switch(status) {
      case 'paid':
        return {
          color: 'bg-green-100 text-green-800',
          icon: <CheckCircle2 className="h-4 w-4 text-green-600 mr-1" />
        };
      case 'approved':
        return {
          color: 'bg-blue-100 text-blue-800',
          icon: <ArrowDown className="h-4 w-4 text-blue-600 mr-1" />
        };
      case 'pending':
        return {
          color: 'bg-yellow-100 text-yellow-800',
          icon: <Clock className="h-4 w-4 text-yellow-600 mr-1" />
        };
      case 'overdue':
        return {
          color: 'bg-red-100 text-red-800',
          icon: <AlertCircle className="h-4 w-4 text-red-600 mr-1" />
        };
      case 'cancelled':
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <XCircle className="h-4 w-4 text-gray-600 mr-1" />
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <Clock className="h-4 w-4 text-gray-600 mr-1" />
        };
    }
  };

  const statusDetails = getStatusDetails(status);

  return (
    <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusDetails.color}`}>
      {statusDetails.icon}
      <span className="capitalize">{status}</span>
    </div>
  );
}
