import React, { useState, useEffect } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { Button } from '@/components/ui/button';
import { Plus, Save, X } from 'lucide-react';
import { TypedDashboardWidget, WidgetPosition } from '@/types/dashboard';
import { WidgetWrapper } from './widgets/WidgetWrapper';
import { WidgetSelector } from './WidgetSelector';
import { useWidgets } from '@/hooks/useWidgets';
import { Skeleton } from '@/components/ui/skeleton';

// Make the grid responsive
const ResponsiveGridLayout = WidthProvider(Responsive);

interface WidgetGridProps {
  className?: string;
}

export function WidgetGrid({ className }: WidgetGridProps): JSX.Element {
  const { widgets, loading, error, saveWidgetPositions, addWidget, removeWidget, updateWidgetSettings } = useWidgets();
  const [isEditing, setIsEditing] = useState(false);
  const [showWidgetSelector, setShowWidgetSelector] = useState(false);
  const [layouts, setLayouts] = useState<Record<string, any[]>>({
    lg: [],
    md: [],
    sm: [],
    xs: [],
  });

  // Convert widgets to layout format when widgets change
  useEffect(() => {
    if (widgets && widgets.length > 0) {
      const newLayouts = widgets.map((widget) => ({
        i: widget.id,
        x: widget.widget_position?.x || 0,
        y: widget.widget_position?.y || 0,
        w: widget.widget_position?.w || 1,
        h: widget.widget_position?.h || 1,
        minW: 1,
        minH: 1,
        maxW: 4,
        maxH: 4,
      }));

      setLayouts({
        lg: newLayouts,
        md: newLayouts,
        sm: newLayouts.map(item => ({ ...item, w: Math.min(item.w, 2), x: 0 })),
        xs: newLayouts.map(item => ({ ...item, w: 1, x: 0 })),
      });
    } else {
      // Reset layouts if there are no widgets
      setLayouts({
        lg: [],
        md: [],
        sm: [],
        xs: [],
      });
    }
  }, [widgets]);

  // Handle layout change
  const handleLayoutChange = (currentLayout: any[], allLayouts: any) => {
    // Only update the layouts state, don't save to database until user clicks Save
    setLayouts(allLayouts);
  };

  // Save layout changes to database
  const handleSaveLayout = async () => {
    const updatedPositions = layouts.lg.map(item => ({
      id: item.i,
      position: {
        x: item.x,
        y: item.y,
        w: item.w,
        h: item.h,
      } as WidgetPosition
    }));

    await saveWidgetPositions(updatedPositions);
    setIsEditing(false);
  };

  // Cancel editing mode
  const handleCancelEdit = () => {
    // Reset layouts to match current widget positions
    const currentLayouts = widgets.map((widget) => ({
      i: widget.id,
      x: widget.widget_position.x || 0,
      y: widget.widget_position.y || 0,
      w: widget.widget_position.w || 1,
      h: widget.widget_position.h || 1,
      minW: 1,
      minH: 1,
      maxW: 4,
      maxH: 4,
    }));

    setLayouts({
      lg: currentLayouts,
      md: currentLayouts,
      sm: currentLayouts.map(item => ({ ...item, w: Math.min(item.w, 2), x: 0 })),
      xs: currentLayouts.map(item => ({ ...item, w: 1, x: 0 })),
    });

    setIsEditing(false);
  };

  // Handle widget removal
  const handleRemoveWidget = async (widgetId: string) => {
    await removeWidget(widgetId);
  };

  // Handle widget settings update
  const handleUpdateWidgetSettings = async (widget: TypedDashboardWidget, newSettings: any) => {
    await updateWidgetSettings(widget.id, newSettings);
  };

  // Find widget by ID
  const findWidgetById = (id: string) => {
    return widgets.find(widget => widget.id === id);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Dashboard</h2>
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-64 w-full rounded-lg" />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-amber-50 text-amber-800 rounded-lg">
        <h3 className="font-semibold">Notice</h3>
        <p>Using local dashboard widgets. Your changes will be saved to your browser's local storage.</p>
        <p className="text-xs mt-2">Technical details: {error}</p>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Dashboard</h2>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <Button variant="outline" size="sm" onClick={handleCancelEdit}>
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
              <Button size="sm" onClick={handleSaveLayout}>
                <Save className="h-4 w-4 mr-1" />
                Save Layout
              </Button>
            </>
          ) : (
            <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
              Customize Dashboard
            </Button>
          )}
        </div>
      </div>

      {isEditing && (
        <div className="mb-4 p-3 bg-muted rounded-lg">
          <p className="text-sm text-muted-foreground mb-2">
            Drag widgets to rearrange them. Resize widgets by dragging the bottom-right corner.
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowWidgetSelector(true)}
            className="flex items-center"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Widget
          </Button>
        </div>
      )}

      {widgets.length === 0 ? (
        <div className="p-8 text-center bg-muted/30 rounded-lg">
          <h3 className="text-lg font-medium mb-4">Your dashboard is empty</h3>
          <p className="text-muted-foreground mb-6">
            Add widgets to customize your dashboard and get insights at a glance.
          </p>
          <Button
            onClick={() => {
              setIsEditing(true);
              setShowWidgetSelector(true);
            }}
            className="flex items-center"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Your First Widget
          </Button>
        </div>
      ) : (
        <ResponsiveGridLayout
          className="layout"
          layouts={layouts}
          breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480 }}
          cols={{ lg: 4, md: 3, sm: 2, xs: 1 }}
          rowHeight={150}
          isDraggable={isEditing}
          isResizable={isEditing}
          onLayoutChange={handleLayoutChange}
          margin={[16, 16]}
        >
          {layouts.lg.map((layout) => {
            const widget = findWidgetById(layout.i);
            if (!widget) return null;

            return (
              <div key={layout.i}>
                <WidgetWrapper
                  widget={widget}
                  isEditing={isEditing}
                  onRemove={() => handleRemoveWidget(widget.id)}
                  onUpdateSettings={(newSettings) => handleUpdateWidgetSettings(widget, newSettings)}
                />
              </div>
            );
          })}
        </ResponsiveGridLayout>
      )}

      {/* Widget selector dialog */}
      {showWidgetSelector && (
        <WidgetSelector
          onSelect={(widgetType) => {
            addWidget(widgetType);
            setShowWidgetSelector(false);
          }}
          onCancel={() => setShowWidgetSelector(false)}
        />
      )}
    </div>
  );
}
