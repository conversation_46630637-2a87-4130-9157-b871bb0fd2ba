import { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, FileText, CreditCard } from 'lucide-react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { InvoiceWithRelations } from '@/components/accounts-receivable/invoice-detail';

// Components
import { SummaryCards } from '@/components/accounts-receivable/SummaryCards';
import { InvoiceFilter } from '@/components/accounts-receivable/InvoiceFilter';
import { InvoiceTable } from '@/components/accounts-receivable/InvoiceTable';
import { PaginationNav } from '@/components/ui/pagination-nav';
import { CreateInvoiceDialog } from '@/components/accounts-receivable/create-invoice-dialog';
import { InvoiceDetail } from '@/components/accounts-receivable/invoice-detail';
import { ExportInvoicesButton } from '@/components/accounts-receivable/ExportInvoicesButton';
import { AgingReports } from '@/components/accounts-receivable/aging-reports';
import { CreditNoteDetail } from '@/components/accounts-receivable/credit-note-detail';

// Hooks
import { useInvoices } from '@/hooks/use-invoices';
import { useAgingReports } from '@/hooks/use-aging-reports';
import { useCreditNotes } from '@/hooks/use-credit-notes';

const AccountsReceivable = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('invoices');
  const [creditNoteDetailOpen, setCreditNoteDetailOpen] = useState(false);

  // Invoices hook
  const { invoices, isLoadingInvoices, selectedInvoice, selectInvoice } = useInvoices();

  // Aging reports hook
  const {
    agingReport,
    isLoading: isLoadingAgingReport,
    asOfDate,
    setAsOfDate,
    refetchAgingReport,
    formatCurrency
  } = useAgingReports();

  // Credit notes hook
  const {
    creditNotes,
    isLoadingCreditNotes,
    selectedCreditNote,
    isLoadingSelectedCreditNote,
    refetchCreditNotes,
    setSelectedCreditNoteId
  } = useCreditNotes();

  // Select an invoice and open detail dialog
  const handleViewInvoice = (invoiceId: string) => {
    selectInvoice(invoiceId);
    setSelectedInvoiceId(invoiceId);
    setDetailDialogOpen(true);
  };

  // Close detail dialog and reset selection
  const handleCloseDetailDialog = () => {
    setDetailDialogOpen(false);
    setTimeout(() => {
      setSelectedInvoiceId(null);
    }, 300);
  };

  // Handle credit note selection
  const handleCreditNoteSelect = (creditNoteId: string) => {
    setSelectedCreditNoteId(creditNoteId);
    setCreditNoteDetailOpen(true);
  };

  // Filter invoices based on search term and status filter
  const filteredInvoices = (invoices || []).filter(invoice => {
    const matchesSearch = searchTerm === '' ||
      (invoice.customer?.name && invoice.customer.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="page-title mb-0">Accounts Receivable</h1>
        <div className="flex items-center space-x-2">
          <ExportInvoicesButton
            invoices={invoices || []}
            isLoading={isLoadingInvoices}
          />
          <CreateInvoiceDialog />
        </div>
      </div>

      {/* Summary Cards */}
      <SummaryCards invoices={(invoices || []).map(invoice => ({
        id: invoice.id,
        customer: invoice.customer.name,
        amount: invoice.total_amount,
        date: invoice.issue_date,
        dueDate: invoice.due_date,
        status: invoice.status,
        email: invoice.customer.email
      }))} />

      {/* Tabs for different sections */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="invoices">
            <FileText className="mr-2 h-4 w-4" />
            Invoices
          </TabsTrigger>
          <TabsTrigger value="aging">
            <Calendar className="mr-2 h-4 w-4" />
            Aging Reports
          </TabsTrigger>
          <TabsTrigger value="credit-notes">
            <CreditCard className="mr-2 h-4 w-4" />
            Credit Notes
          </TabsTrigger>
        </TabsList>

        {/* Invoices Tab */}
        <TabsContent value="invoices">
          <Card>
            <CardHeader className="pb-3">
              <InvoiceFilter
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
                statusFilter={statusFilter}
                setStatusFilter={setStatusFilter}
              />
            </CardHeader>
            <CardContent>
              {isLoadingInvoices ? (
                <div className="h-40 flex items-center justify-center">
                  <p className="text-muted-foreground">Loading invoices...</p>
                </div>
              ) : filteredInvoices.length > 0 ? (
                <>
                  <InvoiceTable
                    invoices={filteredInvoices.map(invoice => ({
                      id: invoice.invoice_number,
                      customer: invoice.customer?.name || 'Unknown',
                      amount: invoice.total_amount,
                      date: invoice.issue_date,
                      dueDate: invoice.due_date,
                      status: invoice.status,
                      email: invoice.customer?.email || '',
                      // Add a click handler to view invoice
                      onClick: () => handleViewInvoice(invoice.id)
                    }))}
                  />
                  {filteredInvoices.length > 10 && <PaginationNav />}
                </>
              ) : (
                <div className="h-40 flex flex-col items-center justify-center gap-2">
                  <FileText className="h-8 w-8 text-muted-foreground" />
                  <p className="text-muted-foreground">
                    {searchTerm || statusFilter !== 'all' ? (
                      "No invoices match your filters"
                    ) : (
                      "No invoices found. Click 'Create Invoice' to add one."
                    )}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Aging Reports Tab */}
        <TabsContent value="aging">
          <AgingReports
            agingData={agingReport}
            isLoading={isLoadingAgingReport}
            asOfDate={asOfDate}
            onDateChange={setAsOfDate}
            onRefresh={refetchAgingReport}
            formatCurrency={formatCurrency}
          />
        </TabsContent>

        {/* Credit Notes Tab */}
        <TabsContent value="credit-notes">
          <Card>
            <CardHeader>
              <h2 className="text-2xl font-bold">Credit Notes</h2>
            </CardHeader>
            <CardContent>
              {isLoadingCreditNotes ? (
                <div className="h-40 flex items-center justify-center">
                  <p className="text-muted-foreground">Loading credit notes...</p>
                </div>
              ) : creditNotes && creditNotes.length > 0 ? (
                <div className="border rounded-md">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credit Note #</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice #</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {creditNotes.map((creditNote) => (
                        <tr
                          key={creditNote.id}
                          className="hover:bg-gray-50 cursor-pointer"
                          onClick={() => handleCreditNoteSelect(creditNote.id)}
                        >
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{creditNote.credit_note_number}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{creditNote.customers.name}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{creditNote.invoices.invoice_number}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(creditNote.issue_date).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatCurrency(creditNote.total_amount)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                              ${creditNote.status === 'approved' ? 'bg-green-100 text-green-800' :
                                creditNote.status === 'rejected' ? 'bg-red-100 text-red-800' :
                                creditNote.status === 'issued' ? 'bg-blue-100 text-blue-800' :
                                'bg-gray-100 text-gray-800'}`}>
                              {creditNote.status.charAt(0).toUpperCase() + creditNote.status.slice(1)}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="h-40 flex flex-col items-center justify-center gap-2">
                  <CreditCard className="h-8 w-8 text-muted-foreground" />
                  <p className="text-muted-foreground">
                    No credit notes found. You can create credit notes from invoice details.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Invoice Detail Dialog */}
      {selectedInvoice && (
        <Dialog open={detailDialogOpen} onOpenChange={setDetailDialogOpen}>
          <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
            <InvoiceDetail
              invoice={selectedInvoice as InvoiceWithRelations}
              onClose={handleCloseDetailDialog}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Credit Note Detail Dialog */}
      {selectedCreditNote && (
        <Dialog open={creditNoteDetailOpen} onOpenChange={setCreditNoteDetailOpen}>
          <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
            <CreditNoteDetail
              creditNote={selectedCreditNote}
              onClose={() => setCreditNoteDetailOpen(false)}
              onStatusChange={refetchCreditNotes}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default AccountsReceivable;
