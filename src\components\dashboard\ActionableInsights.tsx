
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CircleDollarSign, Timer, Info } from "lucide-react";

interface Insight {
  id: string;
  title: string;
  description: string;
  action?: string;
  icon: "payment" | "time" | "info";
  priority: "high" | "medium" | "low";
}

const insights: Insight[] = [
  {
    id: "ins-001",
    title: "Overdue Invoices",
    description: "You have 3 overdue invoices totaling UGX 8.6M that require attention.",
    action: "Send reminders",
    icon: "payment",
    priority: "high",
  },
  {
    id: "ins-002",
    title: "Cash Flow Forecast",
    description: "Based on current trends, you may face a cash shortage next month.",
    action: "View forecast",
    icon: "info",
    priority: "medium",
  },
  {
    id: "ins-003",
    title: "Payment Due Soon",
    description: "Office rent payment of UGX 1.2M is due in 3 days.",
    action: "Schedule payment",
    icon: "time",
    priority: "low",
  },
];

export const ActionableInsights = (): JSX.Element => {
  const getIcon = (iconType: string): JSX.Element => {
    switch (iconType) {
      case "payment":
        return <CircleDollarSign className="h-5 w-5" />;
      case "time":
        return <Timer className="h-5 w-5" />;
      case "info":
        return <Info className="h-5 w-5" />;
      default:
        return <Info className="h-5 w-5" />;
    }
  };

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case "high":
        return "border-l-4 border-red-500 bg-red-50";
      case "medium":
        return "border-l-4 border-yellow-500 bg-yellow-50";
      case "low":
        return "border-l-4 border-blue-500 bg-blue-50";
      default:
        return "";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Actionable Insights</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {insights.map((insight) => (
          <div
            key={insight.id}
            className={`p-4 rounded-md ${getPriorityColor(insight.priority)}`}
          >
            <div className="flex items-start gap-3">
              <div className="mt-1">{getIcon(insight.icon)}</div>
              <div className="flex-1">
                <h4 className="font-medium">{insight.title}</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  {insight.description}
                </p>
                {insight.action && (
                  <Button variant="link" className="p-0 h-auto mt-2 text-primary">
                    {insight.action}
                  </Button>
                )}
              </div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};
