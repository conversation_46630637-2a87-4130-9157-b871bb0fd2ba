
import { z } from 'zod';
import {
  TransactionInsert,
  TransactionItemInsert,
  ApprovalStatus
} from '@/types/index';

/**
 * Simplified transaction schema for the general ledger form
 *
 * This schema is used for basic double-entry transactions with a single debit and credit
 */
export const transactionSchema = z.object({
  // Transaction date (maps to transaction_date in database)
  date: z.date({
    required_error: "Transaction date is required",
  }),

  // Transaction description
  description: z.string().min(5, {
    message: "Description must be at least 5 characters",
  }).max(200, {
    message: "Description must be less than 200 characters",
  }),

  // Account IDs for the double entry
  debitAccount: z.string({
    required_error: "Debit account is required",
  }),

  creditAccount: z.string({
    required_error: "Credit account is required",
  }),

  // Transaction amount
  amount: z.coerce.number().positive({
    message: "Amount must be greater than 0",
  }).lt(**********, {
    message: "Amount must be less than UGX 1B",
  }),

  // Optional reference number
  reference: z.string().max(50, {
    message: "Reference must be less than 50 characters",
  }).optional(),

  // Optional currency code (defaults to UGX)
  currency: z.string().length(3).default('UGX'),

  // Optional status field
  status: z.enum(['pending', 'approved', 'rejected'] as const).optional(),
}).refine(
  (data) => data.debitAccount !== data.creditAccount,
  {
    message: "Debit and credit accounts cannot be the same",
    path: ["creditAccount"],
  }
);

export type TransactionFormValues = z.infer<typeof transactionSchema>;

/**
 * Convert form values to database insert types
 *
 * @param values Form values from the transaction form
 * @param companyId ID of the company
 * @param userId ID of the user creating the transaction
 * @returns Transaction and transaction items ready for database insertion
 */
export function transactionFormToInsert(
  values: TransactionFormValues,
  companyId: string,
  userId: string
): { transaction: TransactionInsert, items: TransactionItemInsert[] } {
  // Create the transaction record
  const transaction: TransactionInsert = {
    company_id: companyId,
    created_by: userId,
    transaction_date: values.date.toISOString().split('T')[0],
    description: values.description,
    reference: values.reference || null,
    currency: values.currency,
    // Add status if provided
    ...(values.status && { status: values.status }),
  };

  // Create the transaction items (debit and credit entries)
  const items: TransactionItemInsert[] = [
    {
      account_id: values.debitAccount,
      debit: values.amount,
      credit: null,
      description: values.description,
    },
    {
      account_id: values.creditAccount,
      debit: null,
      credit: values.amount,
      description: values.description,
    }
  ];

  return { transaction, items };
}
