
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { ExportReportsButton } from './ExportReportsButton';
import {
  BalanceSheetData,
  ProfitAndLossData,
  CashFlowData,
  TaxReportData
} from '@/services/report-service';

interface ReportHeaderProps {
  exportReport: () => void;
  balanceSheet?: BalanceSheetData | null;
  profitAndLoss?: ProfitAndLossData | null;
  cashFlow?: CashFlowData | null;
  taxReport?: TaxReportData | null;
  startDate: Date;
  endDate: Date;
  isLoading?: boolean;
}

export function ReportHeader({
  exportReport,
  balanceSheet,
  profitAndLoss,
  cashFlow,
  taxReport,
  startDate,
  endDate,
  isLoading = false
}: ReportHeaderProps): React.JSX.Element {
  return (
    <div className="flex justify-between items-center mb-6">
      <h1 className="page-title mb-0">Financial Reports</h1>
      <ExportReportsButton
        balanceSheet={balanceSheet}
        profitAndLoss={profitAndLoss}
        cashFlow={cashFlow}
        taxReport={taxReport}
        startDate={startDate}
        endDate={endDate}
        isLoading={isLoading}
      />
    </div>
  );
}
