
import React from 'react';
import {
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { TransactionWithAccounts } from '@/types/index';

interface TransactionDialogFooterProps {
  isLoading: boolean;
  onCancel: () => void;
  onSubmit: () => void;
  editTransaction?: TransactionWithAccounts;
}

const TransactionDialogFooter: React.FC<TransactionDialogFooterProps> = ({
  isLoading,
  onCancel,
  onSubmit,
  editTransaction
}) => {
  return (
    <DialogFooter className="mt-4">
      <Button type="button" variant="outline" onClick={onCancel}>
        Cancel
      </Button>
      <Button type="button" disabled={isLoading} onClick={onSubmit}>
        {isLoading ? 'Saving...' : editTransaction ? 'Update Transaction' : 'Record Transaction'}
      </Button>
    </DialogFooter>
  );
};

export default TransactionDialogFooter;
