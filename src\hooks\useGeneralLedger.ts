import { useState, useEffect } from 'react';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';
import { supabase } from "@/integrations/supabase/client";
import {
  Account,
  AccountWithBalance,
  Transaction,
  TransactionWithAccounts,
  ApprovalStatus
} from '@/types/index';
import { useAccounts } from './useAccounts';
import { useTransactions } from './useTransactions';

// Empty array for accounts - will be populated from the database
const emptyAccounts: AccountWithBalance[] = [];

export function useGeneralLedger(): {
  transactions: TransactionWithAccounts[];
  accounts: AccountWithBalance[];
  isLoading: boolean;
  handleImportComplete: (importedTransactions: Array<{
    date: string;
    description: string;
    debit: string;
    credit: string;
    amount: number;
  }>) => Promise<TransactionWithAccounts[]>;
  addTransaction: (transaction: TransactionWithAccounts) => void;
  updateTransaction: (transaction: TransactionWithAccounts) => void;
  deleteTransaction: (transactionId: string) => void;
} {
  const { user, currentCompanyId } = useAuth();
  const { accounts: fetchedAccounts, isLoading: isLoadingAccounts } = useAccounts();
  const { transactions: fetchedTransactions, isLoading: isLoadingTransactions } = useTransactions();

  const [accounts, setAccounts] = useState<AccountWithBalance[]>(emptyAccounts);
  const [transactions, setTransactions] = useState<TransactionWithAccounts[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Use the fetched accounts when available
  useEffect(() => {
    if (fetchedAccounts.length > 0) {
      setAccounts(fetchedAccounts);
    }
  }, [fetchedAccounts]);

  // Use the fetched transactions when available
  useEffect(() => {
    if (fetchedTransactions.length > 0) {
      setTransactions(fetchedTransactions);
    }
  }, [fetchedTransactions]);

  // Update loading state based on both data sources
  useEffect(() => {
    setIsLoading(isLoadingAccounts || isLoadingTransactions);
  }, [isLoadingAccounts, isLoadingTransactions]);

  const handleImportComplete = async (importedTransactions: Array<{
    date: string;
    description: string;
    debit: string;
    credit: string;
    amount: number;
  }>): Promise<TransactionWithAccounts[]> => {
    if (!currentCompanyId) {
      toast({
        title: "Error",
        description: "No company selected",
        variant: "destructive"
      });
      return [];
    }

    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        toast({
          title: "Error",
          description: "User not authenticated",
          variant: "destructive"
        });
        return [];
      }

      const createdTransactions: TransactionWithAccounts[] = [];

      // Process each imported transaction
      for (const importedTx of importedTransactions) {
        // Find account IDs
        const debitAccountId = accounts.find(a => a.name === importedTx.debit)?.id;
        const creditAccountId = accounts.find(a => a.name === importedTx.credit)?.id;

        if (!debitAccountId || !creditAccountId) {
          console.warn(`Skipping transaction with unknown accounts: ${importedTx.debit} or ${importedTx.credit}`);
          continue;
        }

        // Create transaction
        const { data: newTransaction, error: txError } = await supabase
          .from('transactions')
          .insert({
            date: importedTx.date,
            description: importedTx.description,
            status: 'pending' as ApprovalStatus,
            company_id: currentCompanyId,
            created_by: user.id
          })
          .select()
          .single();

        if (txError) {
          console.error('Error creating transaction:', txError);
          continue;
        }

        // Create transaction items
        const { error: itemsError } = await supabase
          .from('transaction_items')
          .insert([
            {
              transaction_id: newTransaction.id,
              account_id: debitAccountId,
              description: importedTx.description,
              debit: importedTx.amount,
              credit: null
            },
            {
              transaction_id: newTransaction.id,
              account_id: creditAccountId,
              description: importedTx.description,
              debit: null,
              credit: importedTx.amount
            }
          ]);

        if (itemsError) {
          console.error('Error creating transaction items:', itemsError);
          continue;
        }

        // Fetch the complete transaction with account details
        const { data: completeTransaction, error: fetchError } = await supabase
          .from('transactions')
          .select(`
            id,
            date,
            reference,
            description,
            status,
            created_at,
            transaction_items (
              id,
              account_id,
              description,
              debit,
              credit,
              accounts (
                id,
                name,
                code
              )
            )
          `)
          .eq('id', newTransaction.id)
          .single();

        if (fetchError) {
          console.error('Error fetching complete transaction:', fetchError);
          continue;
        }

        // Format the transaction for state update
        const items = completeTransaction.transaction_items.map((ti: any) => ({
          ...ti,
          account: ti.accounts
        }));

        const formattedTransaction: TransactionWithAccounts = {
          ...completeTransaction,
          items
        };

        createdTransactions.push(formattedTransaction);
      }

      // Update state with all created transactions
      if (createdTransactions.length > 0) {
        setTransactions(prev => [...createdTransactions, ...prev]);
      }

      toast({
        title: "Import Complete",
        description: `Successfully imported ${createdTransactions.length} transactions`,
      });

      return createdTransactions;
    } catch (err: any) {
      console.error('Error importing transactions:', err);
      toast({
        title: "Error importing transactions",
        description: err.message,
        variant: "destructive"
      });
      return [];
    }
  };

  const addTransaction = (transaction: TransactionWithAccounts): void => {
    setTransactions(prev => [transaction, ...prev]);
  };

  const updateTransaction = (transaction: TransactionWithAccounts): void => {
    setTransactions(prev => prev.map(t => t.id === transaction.id ? transaction : t));
  };

  const deleteTransaction = (transactionId: string): void => {
    setTransactions(prev => prev.filter(t => t.id !== transactionId));
  };

  return {
    transactions,
    accounts,
    isLoading,
    handleImportComplete,
    addTransaction,
    updateTransaction,
    deleteTransaction
  };
}
