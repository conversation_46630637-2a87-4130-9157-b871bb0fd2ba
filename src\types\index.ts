/**
 * Central type definitions for Kaya Finance
 *
 * This file serves as the single source of truth for TypeScript types,
 * leveraging the Supabase-generated types from database.ts
 */

import { User, Session } from '@supabase/supabase-js';
import { Database } from './supabase';
import { Tables, TablesInsert, TablesUpdate, Enums, Constants } from './database';

// Re-export the base Supabase types
export type { Database, Tables, TablesInsert, TablesUpdate, Enums };
export { Constants };

// Type aliases for common database tables
export type AccountType = Tables<'account_types'>;
export type AccountTypeInsert = TablesInsert<'account_types'>;
export type AccountTypeUpdate = TablesUpdate<'account_types'>;

export type Account = Tables<'accounts'>;
export type AccountInsert = TablesInsert<'accounts'>;
export type AccountUpdate = TablesUpdate<'accounts'>;

export type Company = Tables<'companies'>;
export type CompanyInsert = TablesInsert<'companies'>;
export type CompanyUpdate = TablesUpdate<'companies'>;

export type CompanySetting = Tables<'company_settings'>;
export type CompanySettingInsert = TablesInsert<'company_settings'>;
export type CompanySettingUpdate = TablesUpdate<'company_settings'>;

export type Customer = Tables<'customers'>;
export type CustomerInsert = TablesInsert<'customers'>;
export type CustomerUpdate = TablesUpdate<'customers'>;

export type Employee = Tables<'employees'>;
export type EmployeeInsert = TablesInsert<'employees'>;
export type EmployeeUpdate = TablesUpdate<'employees'>;

export type Invoice = Tables<'invoices'>;
export type InvoiceInsert = TablesInsert<'invoices'>;
export type InvoiceUpdate = TablesUpdate<'invoices'>;

export type PayrollEntry = Tables<'payroll_entries'>;
export type PayrollEntryInsert = TablesInsert<'payroll_entries'>;
export type PayrollEntryUpdate = TablesUpdate<'payroll_entries'>;

export type PayrollItem = Tables<'payroll_items'>;
export type PayrollItemInsert = TablesInsert<'payroll_items'>;
export type PayrollItemUpdate = TablesUpdate<'payroll_items'>;

export type PayrollRun = Tables<'payroll_runs'>;
export type PayrollRunInsert = TablesInsert<'payroll_runs'>;
export type PayrollRunUpdate = TablesUpdate<'payroll_runs'>;

export type Profile = Tables<'profiles'>;
export type ProfileInsert = TablesInsert<'profiles'>;
export type ProfileUpdate = TablesUpdate<'profiles'>;

export type TransactionItem = Tables<'transaction_items'>;
export type TransactionItemInsert = TablesInsert<'transaction_items'>;
export type TransactionItemUpdate = TablesUpdate<'transaction_items'>;

export type Transaction = Tables<'transactions'>;
export type TransactionInsert = TablesInsert<'transactions'>;
export type TransactionUpdate = TablesUpdate<'transactions'>;

export type UserCompany = Tables<'user_companies'>;
export type UserCompanyInsert = TablesInsert<'user_companies'>;
export type UserCompanyUpdate = TablesUpdate<'user_companies'>;

export type UserPreference = Tables<'user_preferences'>;
export type UserPreferenceInsert = TablesInsert<'user_preferences'>;
export type UserPreferenceUpdate = TablesUpdate<'user_preferences'>;

export type Vendor = Tables<'vendors'>;
export type VendorInsert = TablesInsert<'vendors'>;
export type VendorUpdate = TablesUpdate<'vendors'>;

// Enum type aliases
export type UserRole = Enums<'user_role'>;
export type InvoiceStatus = Enums<'invoice_status'>;
export type BillStatus = Enums<'bill_status'>;
export type BankTransactionType = Enums<'bank_txn_type'>;
export type BudgetStatus = Enums<'budget_status'>;
export type ApprovalStatus = Enums<'approval_status'>;
export type PayrollItemType = Enums<'payroll_item_type'>;

// Account category type (not directly from database)
export type AccountCategory = 'Assets' | 'Liabilities' | 'Equity' | 'Revenue' | 'Expenses';

// Auth related types
export interface AuthState {
  user: User | null;
  profile: Profile | null;
  roles: UserRole[];
  companies: Company[];
  currentCompanyId: string | null;
  loading: boolean;
  session: Session | null;
}

// Export utility types from utility-types.ts
export * from './utility-types';

// Extended types for UI components
export interface AccountWithBalance extends Account {
  balance: number;
}

export interface InvoiceWithCustomer extends Invoice {
  customer: Pick<Customer, 'id' | 'name' | 'email' | 'phone'>;
}

export interface TransactionWithAccounts extends Transaction {
  items: (TransactionItem & { account: Pick<Account, 'id' | 'name' | 'code'> })[];
}
