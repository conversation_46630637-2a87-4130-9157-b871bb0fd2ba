import { useState } from 'react';
import { useMutation, UseMutationOptions, UseMutationResult } from '@tanstack/react-query';
import { toast } from '@/components/ui/sonner';
import { ApiErrorCode, handleApiError } from '@/utils/api-error-utils';

/**
 * Options for retry mutation
 */
interface RetryMutationOptions<TData, TError, TVariables, TContext> 
  extends Omit<UseMutationOptions<TData, TError, TVariables, TContext>, 'mutationFn'> {
  /**
   * Maximum number of retry attempts
   * @default 3
   */
  maxRetries?: number;
  
  /**
   * Base delay in milliseconds between retries
   * @default 1000
   */
  baseDelay?: number;
  
  /**
   * Whether to use exponential backoff for retry delays
   * @default true
   */
  useExponentialBackoff?: boolean;
  
  /**
   * Whether to add jitter to retry delays
   * @default true
   */
  useJitter?: boolean;
  
  /**
   * Error codes that should not be retried
   * @default ['auth/unauthorized', 'permission/denied', 'validation/failed', 'resource/not-found']
   */
  nonRetryableErrorCodes?: ApiErrorCode[];
  
  /**
   * Whether to show toast notifications for retries
   * @default true
   */
  showRetryToasts?: boolean;
}

/**
 * Custom hook for mutations with automatic retry logic
 * 
 * This hook extends React Query's useMutation with automatic retry logic
 * for handling network failures and transient errors.
 * 
 * @param mutationFn The mutation function to execute
 * @param options Options for the mutation and retry behavior
 * @returns React Query mutation result with retry state
 */
export function useRetryMutation<TData, TError, TVariables, TContext>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: RetryMutationOptions<TData, TError, TVariables, TContext>
): UseMutationResult<TData, TError, TVariables, TContext> & {
  retryCount: number;
  isRetrying: boolean;
  retryDelay: number;
} {
  const {
    maxRetries = 3,
    baseDelay = 1000,
    useExponentialBackoff = true,
    useJitter = true,
    nonRetryableErrorCodes = [
      ApiErrorCode.UNAUTHORIZED,
      ApiErrorCode.PERMISSION_DENIED,
      ApiErrorCode.VALIDATION_FAILED,
      ApiErrorCode.NOT_FOUND,
    ],
    showRetryToasts = true,
    ...mutationOptions
  } = options || {};
  
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryDelay, setRetryDelay] = useState(0);
  
  // Calculate delay for next retry attempt
  const calculateRetryDelay = (attempt: number): number => {
    // Base delay
    let delay = baseDelay;
    
    // Apply exponential backoff if enabled
    if (useExponentialBackoff) {
      delay = baseDelay * Math.pow(2, attempt);
    }
    
    // Add jitter if enabled (±20% randomness)
    if (useJitter) {
      const jitterFactor = 0.8 + Math.random() * 0.4; // 0.8 to 1.2
      delay = Math.floor(delay * jitterFactor);
    }
    
    return delay;
  };
  
  // Determine if an error is retryable
  const isRetryableError = (error: any): boolean => {
    // Don't retry if we've reached the maximum retry count
    if (retryCount >= maxRetries) {
      return false;
    }
    
    // Check if the error has a code that should not be retried
    if (error.code && nonRetryableErrorCodes.includes(error.code)) {
      return false;
    }
    
    // Network errors are always retryable
    if (
      error.code === ApiErrorCode.NETWORK_ERROR ||
      error.code === ApiErrorCode.TIMEOUT ||
      error.code === ApiErrorCode.SERVICE_UNAVAILABLE
    ) {
      return true;
    }
    
    // Check for network error messages
    if (
      error.message &&
      (error.message.includes('network') ||
       error.message.includes('connection') ||
       error.message.includes('timeout') ||
       error.message.includes('offline'))
    ) {
      return true;
    }
    
    // Server errors (5xx) are retryable
    if (error.status && error.status >= 500 && error.status < 600) {
      return true;
    }
    
    // Default to not retrying for other errors
    return false;
  };
  
  // Wrap the mutation function with retry logic
  const retryableMutationFn = async (variables: TVariables): Promise<TData> => {
    try {
      // Reset retry state on new mutation
      setRetryCount(0);
      setIsRetrying(false);
      setRetryDelay(0);
      
      // Execute the mutation
      return await mutationFn(variables);
    } catch (error: any) {
      // Handle the error
      const apiError = handleApiError(error, { showToast: !isRetryableError(error) });
      
      // If the error is retryable, retry the mutation
      if (isRetryableError(apiError)) {
        const nextRetryCount = retryCount + 1;
        const nextRetryDelay = calculateRetryDelay(nextRetryCount);
        
        setRetryCount(nextRetryCount);
        setIsRetrying(true);
        setRetryDelay(nextRetryDelay);
        
        // Show retry toast if enabled
        if (showRetryToasts) {
          toast.info(`Retrying operation (${nextRetryCount}/${maxRetries})`, {
            description: `Retrying in ${Math.ceil(nextRetryDelay / 1000)} seconds...`,
          });
        }
        
        // Wait for the calculated delay
        await new Promise(resolve => setTimeout(resolve, nextRetryDelay));
        
        // Try again
        setIsRetrying(false);
        return await retryableMutationFn(variables);
      }
      
      // If not retryable, rethrow the error
      throw apiError;
    }
  };
  
  // Use React Query's useMutation with our retryable mutation function
  const mutation = useMutation<TData, TError, TVariables, TContext>({
    ...mutationOptions,
    mutationFn: retryableMutationFn,
    onSuccess: (data, variables, context) => {
      // Reset retry state on success
      setRetryCount(0);
      setIsRetrying(false);
      setRetryDelay(0);
      
      // Call the original onSuccess handler if provided
      if (mutationOptions.onSuccess) {
        mutationOptions.onSuccess(data, variables, context);
      }
    },
    onError: (error, variables, context) => {
      // Call the original onError handler if provided
      if (mutationOptions.onError) {
        mutationOptions.onError(error as TError, variables, context);
      }
    },
  });
  
  // Return the mutation result with retry state
  return {
    ...mutation,
    retryCount,
    isRetrying,
    retryDelay,
  };
}
