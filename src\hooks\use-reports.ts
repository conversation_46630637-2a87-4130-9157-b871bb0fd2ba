import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/context/AuthContext';
import { toast } from '@/hooks/use-toast';
import {
  fetchBalanceSheet,
  fetchProfitAndLoss,
  fetchCashFlow,
  fetchTaxReport
} from '@/services/report-service';
import { startOfYear, endOfYear, startOfQuarter, endOfQuarter, startOfMonth, endOfMonth } from 'date-fns';

type DateRange = 'year' | 'quarter' | 'month' | 'custom';

export const useReports = () => {
  const { currentCompanyId } = useAuth();
  const [dateRange, setDateRange] = useState<DateRange>('month');
  const [startDate, setStartDate] = useState<Date>(startOfMonth(new Date()));
  const [endDate, setEndDate] = useState<Date>(endOfMonth(new Date()));
  const [reportYear, setReportYear] = useState<number>(new Date().getFullYear());
  const [reportQuarter, setReportQuarter] = useState<number>(
    Math.floor(new Date().getMonth() / 3) + 1
  );

  // Update date range when date type changes
  const updateDateRange = (range: DateRange): void => {
    const now = new Date();

    switch (range) {
      case 'year':
        setStartDate(startOfYear(now));
        setEndDate(endOfYear(now));
        break;
      case 'quarter':
        setStartDate(startOfQuarter(now));
        setEndDate(endOfQuarter(now));
        break;
      case 'month':
        setStartDate(startOfMonth(now));
        setEndDate(endOfMonth(now));
        break;
      case 'custom':
        // Keep existing dates for custom range
        break;
    }

    setDateRange(range);
  };

  // Balance Sheet Query
  const balanceSheetQuery = useQuery({
    queryKey: ['balanceSheet', currentCompanyId, endDate],
    queryFn: async () => {
      if (!currentCompanyId) return null;

      try {
        return await fetchBalanceSheet(currentCompanyId, endDate);
      } catch (error: any) {
        toast({
          title: "Failed to load Balance Sheet",
          description: error.message || "Please try again later",
          variant: "destructive"
        });
        throw error;
      }
    },
    enabled: !!currentCompanyId,
  });

  // Profit and Loss Query
  const profitAndLossQuery = useQuery({
    queryKey: ['profitAndLoss', currentCompanyId, startDate, endDate],
    queryFn: async () => {
      if (!currentCompanyId) return null;

      try {
        return await fetchProfitAndLoss(currentCompanyId, startDate, endDate);
      } catch (error: any) {
        toast({
          title: "Failed to load Profit & Loss report",
          description: error.message || "Please try again later",
          variant: "destructive"
        });
        throw error;
      }
    },
    enabled: !!currentCompanyId,
  });

  // Cash Flow Query
  const cashFlowQuery = useQuery({
    queryKey: ['cashFlow', currentCompanyId, startDate, endDate],
    queryFn: async () => {
      if (!currentCompanyId) return null;

      try {
        // Fetch cash flow data with period-over-period comparison
        return await fetchCashFlow(currentCompanyId, startDate, endDate);
      } catch (error: any) {
        toast({
          title: "Failed to load Cash Flow report",
          description: error.message || "Please try again later",
          variant: "destructive"
        });
        throw error;
      }
    },
    enabled: !!currentCompanyId,
  });

  // Tax Report Query
  const taxReportQuery = useQuery({
    queryKey: ['taxReport', currentCompanyId, reportYear, reportQuarter],
    queryFn: async () => {
      if (!currentCompanyId) return null;

      try {
        return await fetchTaxReport(currentCompanyId, reportYear, dateRange === 'quarter' ? reportQuarter : undefined);
      } catch (error: any) {
        toast({
          title: "Failed to load Tax report",
          description: error.message || "Please try again later",
          variant: "destructive"
        });
        throw error;
      }
    },
    enabled: !!currentCompanyId,
  });

  // Format currency as UGX
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return {
    // Date range controls
    dateRange,
    startDate,
    endDate,
    reportYear,
    reportQuarter,
    setDateRange: updateDateRange,
    setStartDate,
    setEndDate,
    setReportYear,
    setReportQuarter,

    // Report data
    balanceSheet: balanceSheetQuery.data,
    isLoadingBalanceSheet: balanceSheetQuery.isLoading,

    profitAndLoss: profitAndLossQuery.data,
    isLoadingProfitAndLoss: profitAndLossQuery.isLoading,

    cashFlow: cashFlowQuery.data,
    isLoadingCashFlow: cashFlowQuery.isLoading,

    taxReport: taxReportQuery.data,
    isLoadingTaxReport: taxReportQuery.isLoading,

    // Utilities
    formatCurrency,
  };
};
