
import React from 'react';
import {
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { TransactionWithAccounts } from '@/types/index';

interface TransactionDialogHeaderProps {
  editTransaction?: TransactionWithAccounts;
}

const TransactionDialogHeader: React.FC<TransactionDialogHeaderProps> = ({
  editTransaction
}) => {
  return (
    <DialogHeader>
      <DialogTitle>
        {editTransaction ? 'Edit Transaction' : 'Record New Transaction'}
      </DialogTitle>
      <DialogDescription>
        {editTransaction
          ? 'Update the details of this financial transaction.'
          : 'Enter the details of the financial transaction.'}
      </DialogDescription>
    </DialogHeader>
  );
};

export default TransactionDialogHeader;
