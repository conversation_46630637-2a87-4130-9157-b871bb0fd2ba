import React, { useState } from 'react';
import { format } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import {
  MoreHorizontal,
  CheckCircle,
  RefreshCw,
  Trash2,
  Eye,
  Layers,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { TransactionBatch, useBatchTransactions } from '@/hooks/useBatchTransactions';
import { TransactionWithAccounts } from '@/types/index';
import { BatchTransactionDialog } from './BatchTransactionDialog';
import { AccountWithBalance } from '@/types/index';

interface BatchTransactionsTableProps {
  accounts: AccountWithBalance[];
}

export const BatchTransactionsTable: React.FC<BatchTransactionsTableProps> = ({
  accounts,
}) => {
  const { batches, isLoading, postBatch, reverseBatch, deleteBatch, getBatchTransactions } = useBatchTransactions();
  const [selectedBatch, setSelectedBatch] = useState<TransactionBatch | null>(null);
  const [batchTransactions, setBatchTransactions] = useState<TransactionWithAccounts[]>([]);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const handleViewDetails = async (batch: TransactionBatch) => {
    setSelectedBatch(batch);
    const transactions = await getBatchTransactions(batch.id);
    setBatchTransactions(transactions);
    setDetailsDialogOpen(true);
  };

  const handlePostBatch = async (batchId: string) => {
    await postBatch(batchId);
  };

  const handleReverseBatch = async (batchId: string) => {
    await reverseBatch(batchId);
  };

  const handleDeleteClick = (batch: TransactionBatch) => {
    setSelectedBatch(batch);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (selectedBatch) {
      await deleteBatch(selectedBatch.id);
      setDeleteDialogOpen(false);
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'approved':
        return 'success';
      case 'pending':
        return 'warning';
      case 'rejected':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  if (isLoading) {
    return <div className="py-4 text-center">Loading batch transactions...</div>;
  }

  if (batches.length === 0) {
    return (
      <div className="py-8 text-center">
        <p className="text-muted-foreground mb-4">No batch transactions found</p>
        <BatchTransactionDialog accounts={accounts} />
      </div>
    );
  }

  return (
    <>
      <div className="flex justify-end mb-4">
        <BatchTransactionDialog accounts={accounts} />
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Transactions</TableHead>
            <TableHead>Total Amount</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {batches.map((batch) => (
            <TableRow key={batch.id}>
              <TableCell>{format(new Date(batch.transaction_date), 'MMM d, yyyy')}</TableCell>
              <TableCell className="font-medium">{batch.name}</TableCell>
              <TableCell>{batch.transaction_count || 0}</TableCell>
              <TableCell>{formatCurrency(batch.total_amount || 0)}</TableCell>
              <TableCell>
                <Badge variant={getStatusBadgeVariant(batch.status)}>
                  {batch.status.charAt(0).toUpperCase() + batch.status.slice(1)}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleViewDetails(batch)}>
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </DropdownMenuItem>
                    
                    {batch.status === 'pending' && (
                      <DropdownMenuItem onClick={() => handlePostBatch(batch.id)}>
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Post Batch
                      </DropdownMenuItem>
                    )}
                    
                    {batch.status === 'approved' && (
                      <DropdownMenuItem onClick={() => handleReverseBatch(batch.id)}>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Reverse Batch
                      </DropdownMenuItem>
                    )}
                    
                    <DropdownMenuItem 
                      className="text-destructive focus:text-destructive"
                      onClick={() => handleDeleteClick(batch)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Batch Details Dialog */}
      <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Batch Details: {selectedBatch?.name}</DialogTitle>
            <DialogDescription>
              {selectedBatch?.description || 'No description provided'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid grid-cols-3 gap-4 py-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Date</p>
              <p>{selectedBatch && format(new Date(selectedBatch.transaction_date), 'MMMM d, yyyy')}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Status</p>
              <Badge variant={getStatusBadgeVariant(selectedBatch?.status || 'pending')}>
                {selectedBatch?.status.charAt(0).toUpperCase() + selectedBatch?.status.slice(1)}
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Created</p>
              <p>{selectedBatch && format(new Date(selectedBatch.created_at), 'MMM d, yyyy HH:mm')}</p>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Transactions in Batch</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Description</TableHead>
                  <TableHead>Debit Account</TableHead>
                  <TableHead>Credit Account</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {batchTransactions.map((transaction) => {
                  const debitItem = transaction.items.find(item => item.debit > 0);
                  const creditItem = transaction.items.find(item => item.credit > 0);
                  
                  return (
                    <TableRow key={transaction.id}>
                      <TableCell>{transaction.description}</TableCell>
                      <TableCell>{debitItem?.account?.name || 'Unknown'}</TableCell>
                      <TableCell>{creditItem?.account?.name || 'Unknown'}</TableCell>
                      <TableCell>{formatCurrency(debitItem?.debit || 0)}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(transaction.status || 'pending')}>
                          {transaction.status}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  );
                })}
                {batchTransactions.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4">
                      No transactions found in this batch
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          
          <div className="flex justify-end space-x-2 mt-4">
            {selectedBatch?.status === 'pending' && (
              <Button onClick={() => handlePostBatch(selectedBatch.id)}>
                <CheckCircle className="mr-2 h-4 w-4" />
                Post Batch
              </Button>
            )}
            {selectedBatch?.status === 'approved' && (
              <Button variant="outline" onClick={() => handleReverseBatch(selectedBatch.id)}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Reverse Batch
              </Button>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will delete the batch "{selectedBatch?.name}" and all its transactions. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
