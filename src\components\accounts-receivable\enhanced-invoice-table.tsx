import { useState } from 'react';
import { format } from 'date-fns';
import {
  Table, TableBody, TableCell, TableHead, TableHeader, TableRow
} from '@/components/ui/table';
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MoreVertical,
  Eye,
  Mail,
  Trash2,
  CheckCircle,
  AlertTriangle,
  Clock,
  Send,
  XCircle,
  ArrowUpDown
} from 'lucide-react';
import {
  Dialog,
  DialogContent
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Skeleton } from '@/components/ui/skeleton';

import { useEnhancedInvoices, InvoiceSortOptions } from '@/hooks/use-enhanced-invoices';
import { useAuth } from '@/context/AuthContext';
import { getAvailableTransitions } from '@/services/invoice-workflow-service';
import { EnhancedInvoiceDetail } from './enhanced-invoice-detail';

/**
 * Enhanced invoice table component with React Query integration
 */
export function EnhancedInvoiceTable(): JSX.Element {
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);
  const [selectedRows, setSelectedRows] = useState<string[]>([]);

  const { roles } = useAuth();
  const userRole = roles?.[0] || 'viewer';

  const {
    invoices,
    isLoadingInvoices,
    updateStatus,
    sendEmail,
    deleteInvoice,
    setSortOptions,
    checkForOverdueInvoices
  } = useEnhancedInvoices();

  // Handle sort change
  const handleSortChange = (field: InvoiceSortOptions['field']): void => {
    setSortOptions(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // Handle row selection
  const handleRowSelect = (invoiceId: string): void => {
    setSelectedRows(prev => {
      if (prev.includes(invoiceId)) {
        return prev.filter(id => id !== invoiceId);
      } else {
        return [...prev, invoiceId];
      }
    });
  };

  // Handle select all rows
  const handleSelectAll = (): void => {
    if (selectedRows.length === (invoices?.length || 0)) {
      setSelectedRows([]);
    } else {
      setSelectedRows(invoices?.map(invoice => invoice.id) || []);
    }
  };

  // Format currency as UGX
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Get status badge
  const getStatusBadge = (status: string): JSX.Element => {
    switch (status) {
      case 'draft':
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            Draft
          </Badge>
        );
      case 'sent':
        return (
          <Badge variant="secondary" className="flex items-center gap-1">
            <Send className="h-3 w-3" />
            Sent
          </Badge>
        );
      case 'paid':
        return (
          <Badge variant="success" className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            Paid
          </Badge>
        );
      case 'overdue':
        return (
          <Badge variant="warning" className="flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            Overdue
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <XCircle className="h-3 w-3" />
            Cancelled
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Check for overdue invoices when the component mounts
  useState(() => {
    checkForOverdueInvoices();
  });

  // Loading state
  if (isLoadingInvoices) {
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-[200px]" />
          <Skeleton className="h-8 w-[120px]" />
        </div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]"><Skeleton className="h-4 w-4" /></TableHead>
              <TableHead><Skeleton className="h-4 w-[100px]" /></TableHead>
              <TableHead><Skeleton className="h-4 w-[150px]" /></TableHead>
              <TableHead><Skeleton className="h-4 w-[100px]" /></TableHead>
              <TableHead><Skeleton className="h-4 w-[100px]" /></TableHead>
              <TableHead><Skeleton className="h-4 w-[100px]" /></TableHead>
              <TableHead><Skeleton className="h-4 w-[40px]" /></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array(5).fill(0).map((_, index) => (
              <TableRow key={index}>
                <TableCell><Skeleton className="h-4 w-4" /></TableCell>
                <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
                <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                <TableCell><Skeleton className="h-8 w-8 rounded-full" /></TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  // Empty state
  if (!invoices || invoices.length === 0) {
    return (
      <div className="text-center py-8 border rounded-md">
        <h3 className="text-lg font-medium">No invoices found</h3>
        <p className="text-muted-foreground mt-2">Create your first invoice to get started</p>
      </div>
    );
  }

  return (
    <div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]">
                <Checkbox
                  checked={selectedRows.length === invoices.length && invoices.length > 0}
                  onCheckedChange={handleSelectAll}
                  aria-label="Select all"
                />
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  className="flex items-center gap-1 p-0 h-auto font-medium"
                  onClick={() => handleSortChange('invoice_number')}
                >
                  Invoice #
                  <ArrowUpDown className="h-3 w-3" />
                </Button>
              </TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  className="flex items-center gap-1 p-0 h-auto font-medium"
                  onClick={() => handleSortChange('issue_date')}
                >
                  Issue Date
                  <ArrowUpDown className="h-3 w-3" />
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  className="flex items-center gap-1 p-0 h-auto font-medium"
                  onClick={() => handleSortChange('due_date')}
                >
                  Due Date
                  <ArrowUpDown className="h-3 w-3" />
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  className="flex items-center gap-1 p-0 h-auto font-medium"
                  onClick={() => handleSortChange('amount')}
                >
                  Amount
                  <ArrowUpDown className="h-3 w-3" />
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  className="flex items-center gap-1 p-0 h-auto font-medium"
                  onClick={() => handleSortChange('status')}
                >
                  Status
                  <ArrowUpDown className="h-3 w-3" />
                </Button>
              </TableHead>
              <TableHead className="w-[60px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {invoices.map((invoice) => (
              <TableRow key={invoice.id} className={invoice._isOptimistic ? 'opacity-50' : ''}>
                <TableCell>
                  <Checkbox
                    checked={selectedRows.includes(invoice.id)}
                    onCheckedChange={() => handleRowSelect(invoice.id)}
                    aria-label={`Select invoice ${invoice.invoice_number}`}
                    disabled={invoice._isOptimistic}
                  />
                </TableCell>
                <TableCell className="font-medium">{invoice.invoice_number}</TableCell>
                <TableCell>{invoice.customers?.name}</TableCell>
                <TableCell>{format(new Date(invoice.issue_date), 'MMM d, yyyy')}</TableCell>
                <TableCell>{format(new Date(invoice.due_date), 'MMM d, yyyy')}</TableCell>
                <TableCell>{formatCurrency(invoice.total_amount)}</TableCell>
                <TableCell>{getStatusBadge(invoice.status)}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setSelectedInvoiceId(invoice.id)}>
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>

                      {invoice.status === 'draft' && (
                        <DropdownMenuItem onClick={() => sendEmail(invoice.id)}>
                          <Mail className="mr-2 h-4 w-4" />
                          Send Invoice
                        </DropdownMenuItem>
                      )}

                      {getAvailableTransitions(invoice.status, userRole).map((transition) => (
                        <DropdownMenuItem
                          key={transition.to}
                          onClick={() => updateStatus({ invoiceId: invoice.id, status: transition.to })}
                        >
                          {transition.to === 'paid' && <CheckCircle className="mr-2 h-4 w-4" />}
                          {transition.to === 'cancelled' && <XCircle className="mr-2 h-4 w-4" />}
                          {transition.to === 'sent' && <Send className="mr-2 h-4 w-4" />}
                          {transition.to === 'overdue' && <AlertTriangle className="mr-2 h-4 w-4" />}
                          {transition.to === 'draft' && <Clock className="mr-2 h-4 w-4" />}
                          {transition.label}
                        </DropdownMenuItem>
                      ))}

                      <DropdownMenuItem
                        className="text-destructive focus:text-destructive"
                        onClick={() => deleteInvoice(invoice.id)}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <Dialog open={!!selectedInvoiceId} onOpenChange={(open) => !open && setSelectedInvoiceId(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          {selectedInvoiceId && (
            <EnhancedInvoiceDetail
              invoiceId={selectedInvoiceId}
              onClose={() => setSelectedInvoiceId(null)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
