# Kaya Finance Hooks Documentation

This document provides documentation for the core hooks used in the Kaya Finance application.

## Table of Contents

- [usePermissions](#usepermissions)
- [useInvoices](#useinvoices)
- [useAuth](#useauth)
- [useAccounts](#useaccounts)
- [useTransactions](#usetransactions)

## usePermissions

Hook for checking user permissions based on roles.

This hook provides utility functions to check if the current user has specific roles or permissions within the application.

### Example

```tsx
const { canCreateInvoices, isAdmin } = usePermissions();

if (canCreateInvoices()) {
  // Show create invoice button
}
```

### Returns

Object containing role and permission check functions:

| Function | Description | Return Type |
|----------|-------------|-------------|
| `hasRole(role)` | Check if the user has a specific role | `boolean` |
| `isAdmin()` | Check if the user has the admin role | `boolean` |
| `isAccountant()` | Check if the user has the accountant role | `boolean` |
| `isManager()` | Check if the user has the manager role | `boolean` |
| `isViewer()` | Check if the user has the viewer role | `boolean` |
| `hasAnyRole(allowedRoles)` | Check if the user has any of the specified roles | `boolean` |
| `canManageUsers()` | Check if the user can manage users (admin only) | `boolean` |
| `canManageCompany()` | Check if the user can manage company settings (admin only) | `boolean` |
| `canApproveTransactions()` | Check if the user can approve transactions (admin or accountant) | `boolean` |
| `canCreateInvoices()` | Check if the user can create invoices (admin, accountant, or manager) | `boolean` |
| `canDeleteInvoices()` | Check if the user can delete invoices (admin or accountant) | `boolean` |
| `canViewReports()` | Check if the user can view financial reports (admin, accountant, or manager) | `boolean` |

## useInvoices

Hook for managing invoices with React Query.

This hook provides functionality to fetch, create, and update invoices, as well as manage the selected invoice state.

### Example

```tsx
const {
  invoices,
  isLoadingInvoices,
  selectedInvoice,
  setSelectedInvoice,
  createInvoice,
  updateInvoiceStatus
} = useInvoices();
```

### Returns

Object containing invoice data and mutation functions:

| Property | Description | Type |
|----------|-------------|------|
| `invoices` | List of invoices for the current company | `InvoiceWithCustomer[]` |
| `isLoadingInvoices` | Loading state for invoices query | `boolean` |
| `invoicesError` | Error from invoices query | `Error` |
| `selectedInvoice` | Currently selected invoice with details | `InvoiceWithCustomer & { invoice_items: any[] }` |
| `isLoadingInvoice` | Loading state for selected invoice query | `boolean` |
| `selectInvoice` | Function to select an invoice by ID | `(id: string) => void` |
| `createInvoice` | Function to create a new invoice | `(data: { formData: InvoiceFormValues; customerId: string }) => void` |
| `isCreatingInvoice` | Loading state for create invoice mutation | `boolean` |
| `updateInvoiceStatus` | Function to update an invoice's status | `(data: { invoiceId: string; status: string }) => void` |
| `isUpdatingStatus` | Loading state for update status mutation | `boolean` |
| `deleteInvoice` | Function to delete an invoice | `(invoiceId: string) => void` |
| `isDeletingInvoice` | Loading state for delete invoice mutation | `boolean` |

## useAuth

Hook for authentication and user management.

This hook provides functionality for user authentication, profile management, and company selection.

### Returns

Object containing authentication state and functions:

| Property | Description | Type |
|----------|-------------|------|
| `user` | Current authenticated user | `User \| null` |
| `profile` | User profile data | `UserProfile \| null` |
| `roles` | User roles | `UserRole[]` |
| `companies` | Companies associated with the user | `Company[]` |
| `currentCompanyId` | ID of the currently selected company | `string \| null` |
| `session` | Current Supabase session | `Session \| null` |
| `loading` | Loading state for authentication | `boolean` |
| `signIn` | Function to sign in with email and password | `(email: string, password: string) => Promise<void>` |
| `signUp` | Function to create a new account | `(email: string, password: string, firstName: string, lastName: string) => Promise<void>` |
| `signOut` | Function to sign out | `() => Promise<void>` |
| `updateProfile` | Function to update user profile | `(updates: Partial<UserProfile>) => Promise<void>` |
| `refreshProfile` | Function to refresh user profile data | `() => Promise<void>` |
| `setCurrentCompanyId` | Function to change the current company | `(companyId: string \| null) => void` |

## useAccounts

Hook for managing chart of accounts.

This hook provides functionality to fetch and manage accounting accounts.

### Returns

Object containing accounts data and functions:

| Property | Description | Type |
|----------|-------------|------|
| `accounts` | List of accounts | `Account[]` |
| `isLoading` | Loading state for accounts query | `boolean` |
| `error` | Error from accounts query | `Error \| null` |
| `createAccount` | Function to create a new account | `(accountData: Partial<Account>) => Promise<Account \| null>` |
| `updateAccount` | Function to update an account | `(id: string, updates: Partial<Account>) => Promise<boolean>` |
| `deleteAccount` | Function to delete an account | `(id: string) => Promise<boolean>` |

## useTransactions

Hook for managing financial transactions.

This hook provides functionality to fetch and manage financial transactions.

### Returns

Object containing transactions data and functions:

| Property | Description | Type |
|----------|-------------|------|
| `transactions` | List of transactions | `Transaction[]` |
| `isLoading` | Loading state for transactions query | `boolean` |
| `error` | Error from transactions query | `Error \| null` |
| `createTransaction` | Function to create a new transaction | `(transactionData: Partial<Transaction>) => Promise<Transaction \| null>` |
| `updateTransaction` | Function to update a transaction | `(id: string, updates: Partial<Transaction>) => Promise<boolean>` |
| `deleteTransaction` | Function to delete a transaction | `(id: string) => Promise<boolean>` |
