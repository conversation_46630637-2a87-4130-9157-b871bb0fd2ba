import React, { useState, useEffect } from 'react';
import { TypedDashboardWidget, TopCustomersSettings } from '@/types/dashboard';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import { format } from 'date-fns';
import { Customer } from '@/types/index';

interface TopCustomersWidgetProps {
  widget: TypedDashboardWidget;
}

interface CustomerData {
  id: string;
  name: string;
  revenue: number;
  invoiceCount: number;
  lastPurchaseDate: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export function TopCustomersWidget({ widget }: TopCustomersWidgetProps): JSX.Element {
  const settings = widget.widget_settings as TopCustomersSettings;
  const { currentCompanyId } = useAuth();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<CustomerData[]>([]);
  const [error, setError] = useState<string | null>(null);

  const customerLimit = settings.customerLimit || 5;
  const showRevenue = settings.showRevenue !== false;
  const showInvoiceCount = settings.showInvoiceCount !== false;
  const showLastPurchase = settings.showLastPurchase !== false;
  const period = settings.period || 'monthly';
  const useFiscalYear = settings.useFiscalYear !== false;

  useEffect(() => {
    const fetchTopCustomers = async () => {
      if (!currentCompanyId) return;

      try {
        setLoading(true);
        setError(null);

        // Get the date range based on the selected period
        const now = new Date();
        let startDate = new Date();

        if (period === 'monthly') {
          startDate.setMonth(now.getMonth() - 1);
        } else if (period === 'quarterly') {
          startDate.setMonth(now.getMonth() - 3);
        } else if (period === 'yearly') {
          startDate.setFullYear(now.getFullYear() - 1);
        }

        // If using fiscal year, adjust the date range
        if (useFiscalYear) {
          // Fetch company fiscal year settings
          const { data: company, error: companyError } = await supabase
            .from('companies')
            .select('fiscal_year_start')
            .eq('id', currentCompanyId)
            .single();

          if (companyError) {
            console.error('Error fetching fiscal year settings:', companyError);
          } else if (company?.fiscal_year_start) {
            const fiscalYearStart = new Date(company.fiscal_year_start);
            const currentYear = now.getFullYear();

            // Create a date for this year's fiscal start
            const thisYearFiscalStart = new Date(
              currentYear,
              fiscalYearStart.getMonth(),
              fiscalYearStart.getDate()
            );

            // If current date is before this year's fiscal start, use last year's fiscal start
            if (now < thisYearFiscalStart) {
              startDate = new Date(
                currentYear - 1,
                fiscalYearStart.getMonth(),
                fiscalYearStart.getDate()
              );
            } else {
              startDate = thisYearFiscalStart;
            }
          }
        }

        // Format dates for the query
        const startDateStr = startDate.toISOString().split('T')[0];
        const endDateStr = now.toISOString().split('T')[0];

        // First check if the stored procedure exists
        try {
          const { data: customersData, error: customersError } = await supabase
            .rpc('get_top_customers', {
              p_company_id: currentCompanyId,
              p_start_date: startDateStr,
              p_end_date: endDateStr,
              p_limit: customerLimit
            });

          if (customersError) {
            throw customersError;
          }

          if (customersData && customersData.length > 0) {
            setData(customersData);
            return;
          }
        } catch (rpcError) {
          console.log('RPC not available, falling back to direct query');
        }

        // If RPC fails or returns no data, use direct query instead of mock data
        const { data: customers, error: customersQueryError } = await supabase
          .from('customers')
          .select('id, name')
          .eq('company_id', currentCompanyId)
          .eq('is_active', true)
          .limit(customerLimit);

        if (customersQueryError) {
          throw customersQueryError;
        }

        if (!customers || customers.length === 0) {
          setData([]);
          return;
        }

        // For each customer, get their invoices in the date range
        const customerData: CustomerData[] = await Promise.all(
          customers.map(async (customer) => {
            const { data: invoices, error: invoicesError } = await supabase
              .from('invoices')
              .select('id, total_amount, issue_date')
              .eq('company_id', currentCompanyId)
              .eq('customer_id', customer.id)
              .gte('issue_date', startDateStr)
              .lte('issue_date', endDateStr)
              .order('issue_date', { ascending: false });

            if (invoicesError) {
              console.error('Error fetching invoices for customer:', invoicesError);
              return {
                id: customer.id,
                name: customer.name,
                revenue: 0,
                invoiceCount: 0,
                lastPurchaseDate: ''
              };
            }

            const revenue = invoices?.reduce((sum, inv) => sum + (inv.total_amount || 0), 0) || 0;
            const lastPurchaseDate = invoices && invoices.length > 0 ? invoices[0].issue_date : '';

            return {
              id: customer.id,
              name: customer.name,
              revenue,
              invoiceCount: invoices?.length || 0,
              lastPurchaseDate
            };
          })
        );

        // Sort by revenue descending
        customerData.sort((a, b) => b.revenue - a.revenue);
        setData(customerData);
      } catch (err: any) {
        console.error('Error fetching top customers:', err);
        setError(err.message || 'Failed to load customer data');
      } finally {
        setLoading(false);
      }
    };

    fetchTopCustomers();
  }, [currentCompanyId, period, customerLimit, useFiscalYear]);

  // Format currency for display
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format date for display
  const formatDate = (dateString: string): string => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (e) {
      return dateString;
    }
  };

  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const customer = payload[0].payload;
      return (
        <div className="bg-background p-2 border rounded shadow-sm">
          <p className="font-medium">{customer.name}</p>
          {showRevenue && (
            <p className="text-sm">Revenue: {formatCurrency(customer.revenue)}</p>
          )}
          {showInvoiceCount && (
            <p className="text-sm">Invoices: {customer.invoiceCount}</p>
          )}
          {showLastPurchase && (
            <p className="text-sm">Last Purchase: {formatDate(customer.lastPurchaseDate)}</p>
          )}
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Skeleton className="h-[80%] w-[80%] rounded-md" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center text-center p-4">
        <p className="text-sm text-muted-foreground">
          Error loading customer data. Please try again later.
        </p>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="h-full flex items-center justify-center text-center p-4">
        <p className="text-sm text-muted-foreground">
          No customer data available for the selected period.
        </p>
      </div>
    );
  }

  // Render as a chart
  if (showRevenue) {
    return (
      <div className="h-full">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            layout="vertical"
            margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
            <XAxis type="number" />
            <YAxis
              type="category"
              dataKey="name"
              tick={{ fontSize: 12 }}
              width={80}
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar dataKey="revenue" fill="#8884d8">
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>
    );
  }

  // Render as a table if not showing revenue chart
  return (
    <div className="h-full overflow-auto">
      <table className="w-full text-sm">
        <thead>
          <tr className="border-b">
            <th className="text-left py-2 font-medium">Customer</th>
            {showInvoiceCount && (
              <th className="text-right py-2 font-medium">Invoices</th>
            )}
            {showLastPurchase && (
              <th className="text-right py-2 font-medium">Last Purchase</th>
            )}
          </tr>
        </thead>
        <tbody>
          {data.map((customer, index) => (
            <tr key={customer.id} className="border-b">
              <td className="py-2">{customer.name}</td>
              {showInvoiceCount && (
                <td className="text-right py-2">{customer.invoiceCount}</td>
              )}
              {showLastPurchase && (
                <td className="text-right py-2">{formatDate(customer.lastPurchaseDate)}</td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
