import { useState } from 'react';
import { 
  <PERSON>alog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/context/AuthContext';
import RegisterModal from './RegisterModal';

interface LoginModalProps {
  trigger: React.ReactNode;
}

export function LoginModal({ trigger }: LoginModalProps) {
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [showRegister, setShowRegister] = useState<boolean>(false);
  const { signIn } = useAuth();

  const handleSignIn = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault();
    if (loading) return;

    setLoading(true);
    try {
      await signIn(email, password);
      setOpen(false);
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean): void => {
    setOpen(newOpen);
    if (!newOpen) {
      setEmail('');
      setPassword('');
      setShowRegister(false);
    }
  };

  const switchToRegister = (): void => {
    setShowRegister(true);
    setOpen(false);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          {trigger}
        </DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Sign In</DialogTitle>
            <DialogDescription>
              Enter your credentials to access your account
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSignIn} className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="signin-email">Email</Label>
              <Input
                id="signin-email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="signin-password">Password</Label>
                <a href="#" className="text-sm text-secondary hover:underline">Forgot password?</a>
              </div>
              <Input
                id="signin-password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>

            <div className="p-3 bg-muted rounded-md">
              <p className="text-sm font-medium mb-1">Demo Credentials:</p>
              <p className="text-xs text-muted-foreground">Email: <EMAIL></p>
              <p className="text-xs text-muted-foreground">Password: password</p>
            </div>

            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Signing in..." : "Sign in"}
            </Button>

            <div className="text-center text-sm text-muted-foreground">
              Don't have an account?{" "}
              <Button 
                variant="link" 
                className="p-0 h-auto text-secondary hover:underline"
                onClick={switchToRegister}
              >
                Create one
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {showRegister && (
        <RegisterModal 
          trigger={<span style={{ display: 'none' }}></span>} 
          defaultOpen={true}
          onOpenChange={(open) => {
            if (!open) setShowRegister(false);
          }}
        />
      )}
    </>
  );
}