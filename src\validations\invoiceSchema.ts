import { z } from 'zod';

export const invoiceSchema = z.object({
  customer: z.string().min(1, {
    message: "Customer name is required",
  }),
  amount: z.number().min(0, {
    message: "Amount must be a positive number",
  }),
  date: z.date({
    required_error: "Issue date is required",
  }),
  dueDate: z.date({
    required_error: "Due date is required",
  }).superRefine((dueDate, ctx) => {
    // Using superRefine which gives us access to the context
    // We need to cast the ctx to access the input data
    const formData = ctx.path ? ctx : { path: [] };
    const issueDate = formData.path.length ? undefined : (ctx as { data?: { date: Date } }).data?.date;
    
    if (issueDate && dueDate < issueDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Due date cannot be before issue date",
      });
      return false;
    }
    return true;
  }),
  status: z.enum(["draft", "sent", "paid", "overdue", "cancelled"], {
    required_error: "Status is required",
  }),
  email: z.string().email({
    message: "Please enter a valid email address",
  }).optional().or(z.literal('')),
  notes: z.string().optional(),
});

export type InvoiceFormValues = z.infer<typeof invoiceSchema>;
