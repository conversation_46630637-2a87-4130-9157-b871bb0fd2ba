import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Settings, X } from 'lucide-react';
import { TypedDashboardWidget, WidgetSettings } from '@/types/dashboard';
import { FinancialOverviewWidget } from './FinancialOverviewWidget';
import { CashFlowWidget } from './CashFlowWidget';
import { RecentTransactionsWidget } from './RecentTransactionsWidget';
import { AccountBalanceWidget } from './AccountBalanceWidget';
import { ExpenseBreakdownWidget } from './ExpenseBreakdownWidget';
import { TopCustomersWidget } from './TopCustomersWidget';
import { TaxCalendarWidget } from './TaxCalendarWidget';
import { BudgetProgressWidget } from './BudgetProgressWidget';
import { WidgetSettingsDialog } from './WidgetSettingsDialog';

interface WidgetWrapperProps {
  widget: TypedDashboardWidget;
  isEditing: boolean;
  onRemove: () => void;
  onUpdateSettings: (settings: WidgetSettings) => void;
}

export function WidgetWrapper({ 
  widget, 
  isEditing, 
  onRemove,
  onUpdateSettings
}: WidgetWrapperProps): JSX.Element {
  const [showSettings, setShowSettings] = useState(false);

  // Render the appropriate widget based on type
  const renderWidget = () => {
    switch (widget.widget_type) {
      case 'financial_overview':
        return <FinancialOverviewWidget widget={widget} />;
      case 'cash_flow':
        return <CashFlowWidget widget={widget} />;
      case 'recent_transactions':
        return <RecentTransactionsWidget widget={widget} />;
      case 'account_balance':
        return <AccountBalanceWidget widget={widget} />;
      case 'expense_breakdown':
        return <ExpenseBreakdownWidget widget={widget} />;
      case 'top_customers':
        return <TopCustomersWidget widget={widget} />;
      case 'tax_calendar':
        return <TaxCalendarWidget widget={widget} />;
      case 'budget_progress':
        return <BudgetProgressWidget widget={widget} />;
      default:
        return <div>Unknown widget type</div>;
    }
  };

  const handleSaveSettings = (newSettings: WidgetSettings) => {
    onUpdateSettings(newSettings);
    setShowSettings(false);
  };

  return (
    <Card className="h-full overflow-hidden">
      <CardHeader className="p-3 pb-0">
        <div className="flex justify-between items-center">
          <CardTitle className="text-base">{widget.widget_title}</CardTitle>
          {isEditing && (
            <div className="flex gap-1">
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-7 w-7" 
                onClick={() => setShowSettings(true)}
              >
                <Settings className="h-4 w-4" />
              </Button>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-7 w-7 text-destructive hover:text-destructive" 
                onClick={onRemove}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-3 h-[calc(100%-40px)] overflow-auto">
        {renderWidget()}
      </CardContent>

      {showSettings && (
        <WidgetSettingsDialog
          widget={widget}
          onSave={handleSaveSettings}
          onCancel={() => setShowSettings(false)}
        />
      )}
    </Card>
  );
}
