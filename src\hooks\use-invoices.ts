
import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { toast } from '@/hooks/use-toast';
import { InvoiceFormValues } from '@/validations/invoice-schema';
import { createInvoice, updateInvoiceStatus } from '@/services/invoice-service';
import {
  Invoice,
  InvoiceStatus,
  InvoiceWithCustomer,
  Customer,
  TransactionItem
} from '@/types/index';

/**
 * Hook for managing invoices with React Query
 *
 * This hook provides functionality to fetch, create, and update invoices,
 * as well as manage the selected invoice state.
 *
 * @example
 * ```tsx
 * const {
 *   invoices,
 *   isLoadingInvoices,
 *   selectedInvoiceId,
 *   setSelectedInvoiceId,
 *   createInvoiceMutation,
 *   updateInvoiceStatusMutation
 * } = useInvoices();
 * ```
 *
 * @returns Object containing invoice data and mutation functions
 */
export const useInvoices = () => {
  const { currentCompanyId, user } = useAuth();
  const queryClient = useQueryClient();
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);

  /**
   * Query to fetch all invoices for the current company
   */
  const {
    data: invoices,
    isLoading: isLoadingInvoices,
    error: invoicesError,
  } = useQuery<InvoiceWithCustomer[]>({
    queryKey: ['invoices', currentCompanyId],
    queryFn: async () => {
      if (!currentCompanyId) return [];

      const { data, error } = await supabase
        .from('invoices')
        .select(`
          *,
          customers (
            name, email
          )
        `)
        .eq('company_id', currentCompanyId)
        .order('issue_date', { ascending: false });

      if (error) throw error;
      return data as InvoiceWithCustomer[];
    },
    enabled: !!currentCompanyId,
  });

  /**
   * Query to fetch a single invoice by ID with related data
   */
  const {
    data: selectedInvoice,
    isLoading: isLoadingInvoice,
  } = useQuery<Invoice & {
    customers: Pick<Customer, 'id' | 'name' | 'email' | 'phone'>,
    invoice_items: Array<{
      id: string;
      description: string;
      quantity: number;
      unit_price: number;
      tax_rate: number;
      amount: number;
    }>
  } | null>({
    queryKey: ['invoice', selectedInvoiceId],
    queryFn: async () => {
      if (!selectedInvoiceId) return null;

      const { data, error } = await supabase
        .from('invoices')
        .select(`
          *,
          customers (
            id, name, email, phone
          ),
          invoice_items (
            id, description, quantity, unit_price, tax_rate, amount
          )
        `)
        .eq('id', selectedInvoiceId)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!selectedInvoiceId,
  });

  /**
   * Mutation for creating a new invoice
   *
   * @param invoiceData - Object containing form data and customer ID
   * @returns The created invoice
   */
  const createInvoiceMutation = useMutation<
    Invoice | null,
    Error,
    { formData: InvoiceFormValues; customerId: string }
  >({
    mutationFn: async (invoiceData: {
      formData: InvoiceFormValues;
      customerId: string;
    }) => {
      if (!currentCompanyId || !user) throw new Error("Authentication required");

      return await createInvoice(
        invoiceData.formData,
        currentCompanyId,
        invoiceData.customerId,
        user.id
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      toast({
        title: "Invoice created",
        description: "The invoice has been created successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error creating invoice",
        description: error.message || "Failed to create invoice. Please try again.",
        variant: "destructive",
      });
    }
  });

  /**
   * Mutation for updating an invoice's status
   *
   * @param params - Object containing invoice ID and new status
   * @returns The updated invoice
   */
  const updateStatusMutation = useMutation<
    boolean,
    Error,
    { invoiceId: string; status: InvoiceStatus }
  >({
    mutationFn: async ({
      invoiceId,
      status
    }: {
      invoiceId: string;
      status: InvoiceStatus
    }) => {
      return await updateInvoiceStatus(invoiceId, status);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      if (selectedInvoiceId) {
        queryClient.invalidateQueries({ queryKey: ['invoice', selectedInvoiceId] });
      }
      toast({
        title: "Invoice status updated",
        description: `The invoice status has been updated to ${variables.status}.`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error updating invoice status",
        description: error.message || "Failed to update invoice status. Please try again.",
        variant: "destructive",
      });
    }
  });

  /**
   * Mutation for deleting an invoice and its line items
   *
   * @param invoiceId - ID of the invoice to delete
   * @returns True if deletion was successful
   */
  const deleteInvoiceMutation = useMutation<boolean, Error, string>({
    mutationFn: async (invoiceId: string) => {
      // First delete invoice items
      const { error: itemsError } = await supabase
        .from('invoice_items')
        .delete()
        .eq('invoice_id', invoiceId);

      if (itemsError) throw itemsError;

      // Then delete the invoice
      const { error } = await supabase
        .from('invoices')
        .delete()
        .eq('id', invoiceId);

      if (error) throw error;
      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      if (selectedInvoiceId) {
        setSelectedInvoiceId(null);
      }
      toast({
        title: "Invoice deleted",
        description: "The invoice has been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error deleting invoice",
        description: error.message || "Failed to delete invoice. Please try again.",
        variant: "destructive",
      });
    }
  });

  /**
   * Return object containing invoice data and mutation functions
   */
  return {
    // Invoice list data
    invoices,
    isLoadingInvoices,
    invoicesError,

    // Selected invoice data
    selectedInvoice,
    isLoadingInvoice,
    selectInvoice: setSelectedInvoiceId,

    // Invoice mutations
    createInvoice: createInvoiceMutation.mutate,
    isCreatingInvoice: createInvoiceMutation.isPending,
    updateInvoiceStatus: updateStatusMutation.mutate,
    isUpdatingStatus: updateStatusMutation.isPending,
    deleteInvoice: deleteInvoiceMutation.mutate,
    isDeletingInvoice: deleteInvoiceMutation.isPending,
  };
};
