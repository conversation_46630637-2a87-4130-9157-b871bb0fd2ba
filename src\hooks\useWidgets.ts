import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import {
  TypedDashboardWidget,
  WidgetType,
  WidgetPosition,
  WidgetSettings
} from '@/types/dashboard';
import { useToast } from '@/hooks/use-toast';

interface UseWidgetsReturn {
  widgets: TypedDashboardWidget[];
  loading: boolean;
  error: string | null;
  addWidget: (widgetType: WidgetType) => Promise<void>;
  removeWidget: (widgetId: string) => Promise<void>;
  updateWidgetSettings: (widgetId: string, settings: WidgetSettings) => Promise<void>;
  saveWidgetPositions: (positions: { id: string; position: WidgetPosition }[]) => Promise<void>;
}

/**
 * Hook for managing dashboard widgets
 */
export function useWidgets(): UseWidgetsReturn {
  const [widgets, setWidgets] = useState<TypedDashboardWidget[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user, currentCompanyId } = useAuth();
  const { toast } = useToast();

  // Default widget titles by type
  const defaultWidgetTitles: Record<WidgetType, string> = {
    financial_overview: 'Financial Overview',
    cash_flow: 'Cash Flow',
    recent_transactions: 'Recent Transactions',
    account_balance: 'Account Balance',
    expense_breakdown: 'Expense Breakdown',
    top_customers: 'Top Customers',
    tax_calendar: 'Tax Calendar',
    budget_progress: 'Budget Progress'
  };

  // Default widget settings by type
  const getDefaultWidgetSettings = (widgetType: WidgetType): WidgetSettings => {
    switch (widgetType) {
      case 'financial_overview':
        return {
          showRevenue: true,
          showExpenses: true,
          showProfit: true,
          showTrend: true,
          period: 'monthly',
          useFiscalYear: true
        };
      case 'cash_flow':
        return {
          showInflow: true,
          showOutflow: true,
          showNet: true,
          chartType: 'bar',
          period: 'monthly',
          useFiscalYear: true
        };
      case 'recent_transactions':
        return {
          limit: 5,
          showAmount: true,
          showDate: true,
          showCategory: true,
          transactionType: 'all'
        };
      case 'account_balance':
        return {
          showChart: true,
          period: 'monthly',
          useFiscalYear: true
        };
      case 'expense_breakdown':
        return {
          chartType: 'pie',
          showLegend: true,
          showPercentages: true,
          categoryLimit: 5,
          period: 'monthly',
          useFiscalYear: true
        };
      case 'top_customers':
        return {
          customerLimit: 5,
          showRevenue: true,
          showInvoiceCount: true,
          showLastPurchase: true,
          period: 'monthly',
          useFiscalYear: true
        };
      case 'tax_calendar':
        return {
          taxTypes: ['vat', 'paye', 'withholding'],
          showDeadlines: true,
          showAmounts: true,
          daysAhead: 30
        };
      case 'budget_progress':
        return {
          showVariance: true,
          showPercentage: true,
          showChart: true,
          period: 'monthly',
          useFiscalYear: true
        };
      default:
        return {};
    }
  };

  // Get default widget position based on existing widgets
  const getDefaultWidgetPosition = (): WidgetPosition => {
    // Simple algorithm to place new widgets in the next available position
    // For a more sophisticated approach, we could use a grid-based algorithm
    const maxY = widgets.reduce((max, widget) => {
      return Math.max(max, widget.widget_position.y + widget.widget_position.h);
    }, 0);

    return {
      x: 0,
      y: maxY,
      w: 2,
      h: 2
    };
  };

  // Load layout from localStorage
  const loadLayoutFromLocalStorage = (): TypedDashboardWidget[] | null => {
    try {
      const savedLayout = localStorage.getItem('dashboard_layout');
      if (savedLayout) {
        const layoutData = JSON.parse(savedLayout);

        // Check if the layout belongs to the current user and company
        if (layoutData && layoutData.length > 0) {
          // Create widgets from layout data
          const widgets = layoutData.map((item: { id: string; type: string; position: WidgetPosition }) => {
            const widgetType = item.type as WidgetType;
            return {
              id: item.id,
              user_id: user?.id || '',
              company_id: currentCompanyId || '',
              widget_type: widgetType,
              widget_title: defaultWidgetTitles[widgetType],
              widget_position: item.position,
              widget_settings: getDefaultWidgetSettings(widgetType),
              is_visible: true,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };
          });

          return widgets;
        }
      }
    } catch (error) {
      console.error('Error loading layout from localStorage:', error);
    }

    return null;
  };

  // Fetch widgets from the database
  useEffect(() => {
    const fetchWidgets = async () => {
      if (!user || !currentCompanyId) {
        setWidgets([]);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // First try to load from localStorage
        const localWidgets = loadLayoutFromLocalStorage();
        if (localWidgets) {
          setWidgets(localWidgets);
          setLoading(false);
          return;
        }

        // If no localStorage data, try to load from database
        const { data, error } = await supabase
          .from('dashboard_widgets')
          .select('*')
          .eq('user_id', user.id)
          .eq('company_id', currentCompanyId)
          .eq('is_visible', true)
          .order('widget_position->y', { ascending: true });

        if (error) {
          // If the table doesn't exist, use default widgets
          if (error.code === '42P01') { // PostgreSQL code for undefined_table
            console.log('Dashboard widgets table does not exist yet, using default widgets');
            const defaultWidgets = [
              {
                id: 'default-financial-overview',
                user_id: user.id,
                company_id: currentCompanyId,
                widget_type: 'financial_overview' as WidgetType,
                widget_title: 'Financial Overview',
                widget_position: { x: 0, y: 0, w: 2, h: 1 },
                widget_settings: getDefaultWidgetSettings('financial_overview'),
                is_visible: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              },
              {
                id: 'default-cash-flow',
                user_id: user.id,
                company_id: currentCompanyId,
                widget_type: 'cash_flow' as WidgetType,
                widget_title: 'Cash Flow',
                widget_position: { x: 0, y: 1, w: 2, h: 2 },
                widget_settings: getDefaultWidgetSettings('cash_flow'),
                is_visible: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              },
              {
                id: 'default-recent-transactions',
                user_id: user.id,
                company_id: currentCompanyId,
                widget_type: 'recent_transactions' as WidgetType,
                widget_title: 'Recent Transactions',
                widget_position: { x: 2, y: 0, w: 2, h: 1 },
                widget_settings: getDefaultWidgetSettings('recent_transactions'),
                is_visible: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              },
              {
                id: 'default-account-balance',
                user_id: user.id,
                company_id: currentCompanyId,
                widget_type: 'account_balance' as WidgetType,
                widget_title: 'Account Balance',
                widget_position: { x: 2, y: 1, w: 2, h: 2 },
                widget_settings: getDefaultWidgetSettings('account_balance'),
                is_visible: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              }
            ];
            setWidgets(defaultWidgets);
          } else {
            throw error;
          }
        } else {
          // Convert database records to typed widgets
          const typedWidgets: TypedDashboardWidget[] = data.map(widget => ({
            ...widget,
            widget_settings: widget.widget_settings || {}
          }));

          setWidgets(typedWidgets);
        }
      } catch (err) {
        console.error('Error fetching widgets:', err);
        const errorMessage = err instanceof Error
          ? err.message
          : 'Failed to load widgets';
        setError(errorMessage);

        // If there's any other error, set empty widgets array
        setWidgets([]);
      } finally {
        setLoading(false);
      }
    };

    fetchWidgets();
  }, [user, currentCompanyId]);

  // Add a new widget
  const addWidget = async (widgetType: WidgetType): Promise<void> => {
    if (!user || !currentCompanyId) {
      toast({
        title: 'Error',
        description: 'You must be logged in to add widgets',
        variant: 'destructive',
      });
      return;
    }

    try {
      const defaultPosition = getDefaultWidgetPosition();
      const defaultSettings = getDefaultWidgetSettings(widgetType);
      const widgetTitle = defaultWidgetTitles[widgetType];

      // Try to insert into the database
      try {
        const { data, error } = await supabase
          .from('dashboard_widgets')
          .insert({
            user_id: user.id,
            company_id: currentCompanyId,
            widget_type: widgetType,
            widget_title: widgetTitle,
            widget_position: defaultPosition,
            widget_settings: defaultSettings,
            is_visible: true
          })
          .select()
          .single();

        if (error) {
          // If table doesn't exist, just add to local state
          if (error.code === '42P01') {
            const newWidget = {
              id: `local-${widgetType}-${Date.now()}`,
              user_id: user.id,
              company_id: currentCompanyId,
              widget_type: widgetType,
              widget_title: widgetTitle,
              widget_position: defaultPosition,
              widget_settings: defaultSettings,
              is_visible: true,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };

            // Add the new widget to the state
            setWidgets([...widgets, newWidget as TypedDashboardWidget]);
          } else {
            throw error;
          }
        } else {
          // Add the new widget to the state
          setWidgets([...widgets, data as TypedDashboardWidget]);
        }
      } catch (dbError) {
        // If there's a database error, just add to local state
        console.error('Database error, adding widget to local state only:', dbError);

        const newWidget: TypedDashboardWidget = {
          id: `local-${widgetType}-${Date.now()}`,
          user_id: user.id,
          company_id: currentCompanyId,
          widget_type: widgetType,
          widget_title: widgetTitle,
          widget_position: defaultPosition,
          widget_settings: defaultSettings,
          is_visible: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Add the new widget to the state
        setWidgets([...widgets, newWidget]);
      }

      toast({
        title: 'Widget Added',
        description: `${widgetTitle} widget has been added to your dashboard`,
      });
    } catch (err) {
      console.error('Error adding widget:', err);
      const errorMessage = err instanceof Error
        ? err.message
        : 'Failed to add widget';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  // Remove a widget
  const removeWidget = async (widgetId: string): Promise<void> => {
    try {
      // Check if it's a local widget (not in the database)
      const isLocalWidget = widgetId.startsWith('local-') || widgetId.startsWith('default-');

      if (!isLocalWidget) {
        try {
          const { error } = await supabase
            .from('dashboard_widgets')
            .delete()
            .eq('id', widgetId);

          if (error && error.code !== '42P01') {
            throw error;
          }
        } catch (dbError) {
          console.error('Database error when removing widget, proceeding with local removal:', dbError);
        }
      }

      // Remove the widget from the state regardless of database operation
      setWidgets(widgets.filter(widget => widget.id !== widgetId));

      toast({
        title: 'Widget Removed',
        description: 'Widget has been removed from your dashboard',
      });
    } catch (err) {
      console.error('Error removing widget:', err);

      // Still remove from local state even if there was an error
      setWidgets(widgets.filter(widget => widget.id !== widgetId));

      toast({
        title: 'Warning',
        description: 'Widget removed locally, but there was an error updating the database',
        variant: 'destructive',
      });
    }
  };

  // Update widget settings
  const updateWidgetSettings = async (widgetId: string, settings: WidgetSettings): Promise<void> => {
    try {
      // Check if it's a local widget (not in the database)
      const isLocalWidget = widgetId.startsWith('local-') || widgetId.startsWith('default-');

      if (!isLocalWidget) {
        try {
          const { error } = await supabase
            .from('dashboard_widgets')
            .update({
              widget_settings: settings,
              updated_at: new Date().toISOString()
            })
            .eq('id', widgetId);

          if (error && error.code !== '42P01') {
            throw error;
          }
        } catch (dbError) {
          console.error('Database error when updating widget settings, proceeding with local update:', dbError);
        }
      }

      // Update the widget in the state regardless of database operation
      setWidgets(widgets.map(widget => {
        if (widget.id === widgetId) {
          return {
            ...widget,
            widget_settings: settings,
            updated_at: new Date().toISOString()
          };
        }
        return widget;
      }));

      toast({
        title: 'Widget Updated',
        description: 'Widget settings have been updated',
      });
    } catch (err) {
      console.error('Error updating widget settings:', err);
      const errorMessage = err instanceof Error
        ? err.message
        : 'Failed to update widget settings';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  // Save widget positions
  const saveWidgetPositions = async (positions: { id: string; position: WidgetPosition }[]): Promise<void> => {
    try {
      // Separate local and database widgets
      const localPositions = positions.filter(p =>
        p.id.startsWith('local-') || p.id.startsWith('default-')
      );

      const dbPositions = positions.filter(p =>
        !p.id.startsWith('local-') && !p.id.startsWith('default-')
      );

      // Try to update database widgets
      if (dbPositions.length > 0) {
        try {
          // Update each widget position in the database
          const updates = dbPositions.map(({ id, position }) =>
            supabase
              .from('dashboard_widgets')
              .update({
                widget_position: position,
                updated_at: new Date().toISOString()
              })
              .eq('id', id)
          );

          // Wait for all updates to complete
          await Promise.all(updates);
        } catch (dbError) {
          console.error('Database error when saving positions, proceeding with local update:', dbError);
        }
      }

      // Update widgets in the state regardless of database operation
      setWidgets(widgets.map(widget => {
        const positionUpdate = positions.find(p => p.id === widget.id);
        if (positionUpdate) {
          return {
            ...widget,
            widget_position: positionUpdate.position,
            updated_at: new Date().toISOString()
          };
        }
        return widget;
      }));

      // Save to localStorage as a backup
      try {
        const layoutData = widgets.map(widget => ({
          id: widget.id,
          type: widget.widget_type,
          position: widget.widget_position
        }));

        localStorage.setItem('dashboard_layout', JSON.stringify(layoutData));
      } catch (storageError) {
        console.error('Error saving layout to localStorage:', storageError);
      }

      toast({
        title: 'Layout Saved',
        description: 'Dashboard layout has been saved',
      });
    } catch (err: any) {
      console.error('Error saving widget positions:', err);
      toast({
        title: 'Error',
        description: err.message || 'Failed to save dashboard layout',
        variant: 'destructive',
      });
    }
  };

  return {
    widgets,
    loading,
    error,
    addWidget,
    removeWidget,
    updateWidgetSettings,
    saveWidgetPositions
  };
}
