import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';

/**
 * Component that handles automatic redirection to onboarding when needed
 * This component should be placed within the Router context to handle navigation
 */
export const OnboardingRedirect: React.FC = () => {
  const { needsOnboarding, user, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Only redirect if user is authenticated, not loading, and needs onboarding
    if (user && !loading && needsOnboarding) {
      console.log('User needs onboarding, redirecting...');
      navigate('/onboarding', { replace: true });
    }
  }, [user, loading, needsOnboarding, navigate]);

  // This component doesn't render anything
  return null;
};

export default OnboardingRedirect;
