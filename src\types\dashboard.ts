import { Tables, TablesInsert, TablesUpdate } from './database';

/**
 * Dashboard widget types
 */
export type WidgetType = 
  | 'financial_overview'  // Financial overview with key metrics
  | 'cash_flow'           // Cash flow visualization
  | 'recent_transactions' // Recent transactions display
  | 'account_balance'     // Account balance summary
  | 'expense_breakdown'   // Expense breakdown by category
  | 'top_customers'       // Top customers by revenue
  | 'tax_calendar'        // Tax calendar with upcoming deadlines
  | 'budget_progress';    // Budget vs. actual progress

/**
 * Widget position and size in the grid
 */
export interface WidgetPosition {
  x: number; // Column position
  y: number; // Row position
  w: number; // Width in grid units
  h: number; // Height in grid units
}

/**
 * Base widget settings interface
 */
export interface BaseWidgetSettings {
  period?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  limit?: number;
  useFiscalYear?: boolean;
}

/**
 * Financial overview widget settings
 */
export interface FinancialOverviewSettings extends BaseWidgetSettings {
  showRevenue?: boolean;
  showExpenses?: boolean;
  showProfit?: boolean;
  showTrend?: boolean;
}

/**
 * Cash flow widget settings
 */
export interface CashFlowSettings extends BaseWidgetSettings {
  showInflow?: boolean;
  showOutflow?: boolean;
  showNet?: boolean;
  chartType?: 'bar' | 'line';
}

/**
 * Recent transactions widget settings
 */
export interface RecentTransactionsSettings extends BaseWidgetSettings {
  showAmount?: boolean;
  showDate?: boolean;
  showCategory?: boolean;
  transactionType?: 'all' | 'income' | 'expense';
}

/**
 * Account balance widget settings
 */
export interface AccountBalanceSettings extends BaseWidgetSettings {
  showChart?: boolean;
  accountTypes?: string[];
}

/**
 * Expense breakdown widget settings
 */
export interface ExpenseBreakdownSettings extends BaseWidgetSettings {
  chartType?: 'pie' | 'bar';
  showLegend?: boolean;
  showPercentages?: boolean;
  categoryLimit?: number;
}

/**
 * Top customers widget settings
 */
export interface TopCustomersSettings extends BaseWidgetSettings {
  showRevenue?: boolean;
  showInvoiceCount?: boolean;
  showLastPurchase?: boolean;
  customerLimit?: number;
}

/**
 * Tax calendar widget settings
 */
export interface TaxCalendarSettings extends BaseWidgetSettings {
  taxTypes?: ('vat' | 'paye' | 'withholding' | 'corporate' | 'local_service')[];
  showDeadlines?: boolean;
  showAmounts?: boolean;
  daysAhead?: number;
}

/**
 * Budget progress widget settings
 */
export interface BudgetProgressSettings extends BaseWidgetSettings {
  budgetCategories?: string[];
  showVariance?: boolean;
  showPercentage?: boolean;
  showChart?: boolean;
}

/**
 * Union type of all widget settings
 */
export type WidgetSettings = 
  | FinancialOverviewSettings
  | CashFlowSettings
  | RecentTransactionsSettings
  | AccountBalanceSettings
  | ExpenseBreakdownSettings
  | TopCustomersSettings
  | TaxCalendarSettings
  | BudgetProgressSettings;

/**
 * Dashboard widget database type
 */
export type DashboardWidget = Tables<'dashboard_widgets'>;
export type DashboardWidgetInsert = TablesInsert<'dashboard_widgets'>;
export type DashboardWidgetUpdate = TablesUpdate<'dashboard_widgets'>;

/**
 * Dashboard widget with typed settings
 */
export interface TypedDashboardWidget<T extends WidgetSettings = WidgetSettings> extends Omit<DashboardWidget, 'widget_settings'> {
  widget_settings: T;
}

/**
 * Dashboard layout type stored in user_preferences
 */
export interface DashboardLayout {
  widgets: {
    id: string;
    type: WidgetType;
    position: WidgetPosition;
  }[];
}

/**
 * Widget component props
 */
export interface WidgetProps {
  widget: TypedDashboardWidget;
  onEdit?: () => void;
  onRemove?: () => void;
  onResize?: (position: WidgetPosition) => void;
  isEditing?: boolean;
}

/**
 * Widget settings form props
 */
export interface WidgetSettingsFormProps<T extends WidgetSettings = WidgetSettings> {
  settings: T;
  onSave: (settings: T) => void;
  onCancel: () => void;
}
