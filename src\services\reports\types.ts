import { AccountCategory } from "@/types/index";

/**
 * Account summary item for reports
 */
export interface AccountSummaryItem {
  name: string;
  amount: number;
  account_id?: string;
}

/**
 * Account category summary for reports
 */
export interface AccountCategorySummary {
  category: AccountCategory;
  total: number;
  items: AccountSummaryItem[];
}

/**
 * Balance Sheet Report Data
 */
export interface BalanceSheetData {
  assets: AccountCategorySummary;
  liabilities: AccountCategorySummary;
  equity: AccountCategorySummary;
  asOfDate: string;
}

/**
 * Income/Expense category summary for reports
 */
export interface IncomeExpenseSummary {
  total: number;
  items: AccountSummaryItem[];
}

/**
 * Profit and Loss Report Data
 */
export interface ProfitAndLossData {
  revenue: IncomeExpenseSummary;
  expenses: IncomeExpenseSummary;
  netIncome: number;
  startDate: string;
  endDate: string;
}

/**
 * Cash Flow Activity Summary
 */
export interface CashFlowActivitySummary {
  total: number;
  items: Array<{ name: string; amount: number; transaction_id?: string }>;
}

/**
 * Period comparison data for cash flow
 */
export interface PeriodComparisonData {
  currentPeriodTotal: number;
  previousPeriodTotal: number;
  percentageChange: number;
}

/**
 * Cash Flow Report Data
 */
export interface CashFlowData {
  operatingActivities: CashFlowActivitySummary;
  investingActivities: CashFlowActivitySummary;
  financingActivities: CashFlowActivitySummary;
  netCashFlow: number;
  startDate: string;
  endDate: string;
  previousPeriod?: {
    operatingActivities: PeriodComparisonData;
    investingActivities: PeriodComparisonData;
    financingActivities: PeriodComparisonData;
    netCashFlow: PeriodComparisonData;
    startDate: string;
    endDate: string;
  };
}

/**
 * Monthly VAT Summary
 */
export interface MonthlyVatSummary {
  month: string;
  collected: number;
  paid: number;
  net: number;
  month_number?: number;
  year?: number;
  // Additional fields for Uganda VAT return form
  exempt_sales?: number;
  zero_rated_sales?: number;
  standard_rated_sales?: number;
  imports?: number;
  adjustments?: number;
}

/**
 * Monthly Tax Summary
 */
export interface MonthlyTaxSummary {
  month: string;
  amount: number;
  month_number?: number;
  year?: number;
}

/**
 * PAYE Tax Bracket
 */
export interface PayeTaxBracket {
  min: number;
  max: number | null;
  rate: number;
  description: string;
}

/**
 * Monthly PAYE Summary with detailed breakdown
 */
export interface MonthlyPayeSummary extends MonthlyTaxSummary {
  employees_count?: number;
  bracket_breakdown?: Array<{
    bracket: PayeTaxBracket;
    amount: number;
    employees_count: number;
  }>;
}

/**
 * Corporate Income Tax Summary
 */
export interface CorporateIncomeTaxSummary {
  gross_income: number;
  allowable_expenses: number;
  taxable_income: number;
  tax_payable: number;
  tax_rate: number;
  tax_credits?: number;
  final_tax_payable: number;
}

/**
 * Local Service Tax Summary
 */
export interface LocalServiceTaxSummary {
  total: number;
  employees_count: number;
  items: Array<{
    income_range: string;
    tax_amount: number;
    employees_count: number;
  }>;
}

/**
 * Excise Duty Summary
 */
export interface ExciseDutySummary {
  total: number;
  items: Array<{
    product_category: string;
    quantity: number;
    value: number;
    duty_rate: number;
    duty_amount: number;
  }>;
}

/**
 * VAT Report Data
 */
export interface VatReportData {
  collected: number;
  paid: number;
  net: number;
  items: MonthlyVatSummary[];
  // Additional fields for Uganda VAT return form
  vat_registration_number?: string;
  tax_period?: string;
  due_date?: string;
  filing_status?: 'original' | 'amended';
}

/**
 * Tax Category Report Data
 */
export interface TaxCategoryReportData {
  total: number;
  items: MonthlyTaxSummary[];
}

/**
 * PAYE Report Data with detailed breakdown
 */
export interface PayeReportData extends TaxCategoryReportData {
  total_employees: number;
  items: MonthlyPayeSummary[];
  tax_brackets: PayeTaxBracket[];
}

/**
 * Withholding Tax Report Data with detailed breakdown
 */
export interface WithholdingTaxReportData extends TaxCategoryReportData {
  resident_rate: number;
  non_resident_rate: number;
  categories: Array<{
    name: string;
    amount: number;
    rate: number;
  }>;
}

/**
 * Tax Report Data
 */
export interface TaxReportData {
  vat: VatReportData;
  paye: PayeReportData;
  withholding: WithholdingTaxReportData;
  corporate_income_tax?: CorporateIncomeTaxSummary;
  local_service_tax?: LocalServiceTaxSummary;
  excise_duty?: ExciseDutySummary;
  year: number;
  quarter?: number;
}
