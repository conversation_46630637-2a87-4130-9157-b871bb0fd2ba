import React, { useState } from 'react';
import { format } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { CheckCircle, XCircle, AlertCircle, Clock, Eye } from 'lucide-react';
import { useTransactionApprovals, TransactionApproval } from '@/hooks/useTransactionApprovals';
import { TransactionWithAccounts } from '@/types/index';

export const TransactionApprovalDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('pending');
  const [selectedTransaction, setSelectedTransaction] = useState<TransactionWithAccounts | null>(null);
  const [approvalNotes, setApprovalNotes] = useState('');
  const [rejectionNotes, setRejectionNotes] = useState('');
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [rejectionDialogOpen, setRejectionDialogOpen] = useState(false);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);

  const {
    pendingApprovals,
    approvalHistory,
    notifications,
    isLoading,
    approveTransaction,
    rejectTransaction,
    markNotificationAsRead,
  } = useTransactionApprovals();

  const handleApprove = async () => {
    if (selectedTransaction) {
      const success = await approveTransaction(selectedTransaction.id, approvalNotes);
      if (success) {
        setApprovalDialogOpen(false);
        setApprovalNotes('');
        setSelectedTransaction(null);
      }
    }
  };

  const handleReject = async () => {
    if (selectedTransaction) {
      const success = await rejectTransaction(selectedTransaction.id, rejectionNotes);
      if (success) {
        setRejectionDialogOpen(false);
        setRejectionNotes('');
        setSelectedTransaction(null);
      }
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Calculate transaction amount from items
  const getTransactionAmount = (transaction: TransactionWithAccounts): number => {
    if (!transaction.items || transaction.items.length === 0) return 0;
    return transaction.items.reduce((sum, item) => sum + (item.debit || 0), 0);
  };

  const renderPendingApprovals = () => {
    if (isLoading) {
      return (
        <div className="py-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading pending approvals...</p>
        </div>
      );
    }

    if (pendingApprovals.length === 0) {
      return (
        <div className="py-8 text-center">
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-2" />
          <p className="text-muted-foreground">No transactions pending approval</p>
        </div>
      );
    }

    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Progress</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {pendingApprovals.map((transaction) => (
            <TableRow key={transaction.id}>
              <TableCell>{format(new Date(transaction.transaction_date), 'MMM d, yyyy')}</TableCell>
              <TableCell>{transaction.description}</TableCell>
              <TableCell>{formatCurrency(getTransactionAmount(transaction))}</TableCell>
              <TableCell>
                <div className="flex items-center">
                  <span className="mr-2">{transaction.approval_level || 0}/{transaction.approval_threshold || 1}</span>
                  <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-primary" 
                      style={{ 
                        width: `${((transaction.approval_level || 0) / (transaction.approval_threshold || 1)) * 100}%` 
                      }}
                    ></div>
                  </div>
                </div>
              </TableCell>
              <TableCell className="text-right space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedTransaction(transaction);
                    setDetailsDialogOpen(true);
                  }}
                >
                  <Eye className="h-4 w-4 mr-1" />
                  Details
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => {
                    setSelectedTransaction(transaction);
                    setApprovalDialogOpen(true);
                  }}
                >
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Approve
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => {
                    setSelectedTransaction(transaction);
                    setRejectionDialogOpen(true);
                  }}
                >
                  <XCircle className="h-4 w-4 mr-1" />
                  Reject
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };

  const renderApprovalHistory = () => {
    if (approvalHistory.length === 0) {
      return (
        <div className="py-8 text-center">
          <Clock className="h-12 w-12 text-gray-400 mx-auto mb-2" />
          <p className="text-muted-foreground">No approval history found</p>
        </div>
      );
    }

    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Approved By</TableHead>
            <TableHead>Notes</TableHead>
            <TableHead>Level</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {approvalHistory.map((approval) => (
            <TableRow key={approval.id}>
              <TableCell>{format(new Date(approval.approved_at), 'MMM d, yyyy HH:mm')}</TableCell>
              <TableCell>
                {approval.user?.user_metadata?.full_name || approval.user?.email || 'Unknown'}
              </TableCell>
              <TableCell>{approval.notes || 'No notes'}</TableCell>
              <TableCell>Level {approval.approval_level}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };

  const renderNotifications = () => {
    if (notifications.length === 0) {
      return (
        <div className="py-8 text-center">
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-2" />
          <p className="text-muted-foreground">No unread notifications</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {notifications.map((notification) => (
          <Card key={notification.id}>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle className="text-base">
                  Transaction Requires Approval
                </CardTitle>
                <Badge variant="outline">
                  {format(new Date(notification.created_at), 'MMM d, yyyy')}
                </Badge>
              </div>
              <CardDescription>
                {notification.transaction?.description || 'Transaction details'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => markNotificationAsRead(notification.id)}
                >
                  Mark as Read
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Transaction Approval Dashboard</h2>
        <Badge variant="outline" className="text-sm">
          {pendingApprovals.length} Pending Approvals
        </Badge>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="pending" className="flex items-center">
            <AlertCircle className="mr-2 h-4 w-4" />
            Pending Approvals
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center">
            <Clock className="mr-2 h-4 w-4" />
            Approval History
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center">
            <Badge variant="secondary" className="mr-2">
              {notifications.length}
            </Badge>
            Notifications
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pending">
          {renderPendingApprovals()}
        </TabsContent>

        <TabsContent value="history">
          {renderApprovalHistory()}
        </TabsContent>

        <TabsContent value="notifications">
          {renderNotifications()}
        </TabsContent>
      </Tabs>

      {/* Approval Dialog */}
      <Dialog open={approvalDialogOpen} onOpenChange={setApprovalDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Transaction</DialogTitle>
            <DialogDescription>
              Review the transaction details before approving.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Date</Label>
                <div className="font-medium">
                  {selectedTransaction && format(new Date(selectedTransaction.transaction_date), 'MMM d, yyyy')}
                </div>
              </div>
              <div>
                <Label>Amount</Label>
                <div className="font-medium">
                  {selectedTransaction && formatCurrency(getTransactionAmount(selectedTransaction))}
                </div>
              </div>
            </div>
            <div>
              <Label>Description</Label>
              <div className="font-medium">
                {selectedTransaction?.description}
              </div>
            </div>
            <div>
              <Label htmlFor="approval-notes">Approval Notes (Optional)</Label>
              <Textarea
                id="approval-notes"
                placeholder="Add any notes about this approval"
                value={approvalNotes}
                onChange={(e) => setApprovalNotes(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setApprovalDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleApprove}>
              Approve Transaction
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rejection Dialog */}
      <AlertDialog open={rejectionDialogOpen} onOpenChange={setRejectionDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reject Transaction</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to reject this transaction? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4">
            <Label htmlFor="rejection-notes">Rejection Reason</Label>
            <Textarea
              id="rejection-notes"
              placeholder="Please provide a reason for rejection"
              value={rejectionNotes}
              onChange={(e) => setRejectionNotes(e.target.value)}
              className="mt-2"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleReject} className="bg-destructive text-destructive-foreground">
              Reject Transaction
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Transaction Details Dialog */}
      <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Transaction Details</DialogTitle>
            <DialogDescription>
              Review the complete transaction information.
            </DialogDescription>
          </DialogHeader>
          {selectedTransaction && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Date</Label>
                  <div className="font-medium">
                    {format(new Date(selectedTransaction.transaction_date), 'MMM d, yyyy')}
                  </div>
                </div>
                <div>
                  <Label>Reference</Label>
                  <div className="font-medium">
                    {selectedTransaction.reference || 'N/A'}
                  </div>
                </div>
                <div>
                  <Label>Description</Label>
                  <div className="font-medium">
                    {selectedTransaction.description}
                  </div>
                </div>
                <div>
                  <Label>Status</Label>
                  <div className="font-medium">
                    <Badge variant={selectedTransaction.status === 'approved' ? 'success' : 'secondary'}>
                      {selectedTransaction.status}
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label>Approval Progress</Label>
                  <div className="font-medium flex items-center mt-1">
                    <span className="mr-2">{selectedTransaction.approval_level || 0}/{selectedTransaction.approval_threshold || 1}</span>
                    <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-primary" 
                        style={{ 
                          width: `${((selectedTransaction.approval_level || 0) / (selectedTransaction.approval_threshold || 1)) * 100}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <Label>Transaction Items</Label>
                <Table className="mt-2">
                  <TableHeader>
                    <TableRow>
                      <TableHead>Account</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead className="text-right">Debit</TableHead>
                      <TableHead className="text-right">Credit</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {selectedTransaction.items?.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.account?.code} - {item.account?.name}</TableCell>
                        <TableCell>{item.description || selectedTransaction.description}</TableCell>
                        <TableCell className="text-right">{item.debit ? formatCurrency(item.debit) : ''}</TableCell>
                        <TableCell className="text-right">{item.credit ? formatCurrency(item.credit) : ''}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
