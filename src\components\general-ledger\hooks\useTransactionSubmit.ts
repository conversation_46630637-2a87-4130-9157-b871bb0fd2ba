
import { useState } from 'react';
import { toast } from '@/hooks/use-toast';
import {
  TransactionWithAccounts,
  Transaction,
  TransactionItem,
  Account
} from '@/types/index';
import { TransactionFormValues, transactionFormToInsert } from '../types';
import { formatUGX } from '../utils/transactionUtils';
import { UseFormReturn } from 'react-hook-form';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';

interface UseTransactionSubmitProps {
  onTransactionComplete: (transaction: TransactionWithAccounts) => void;
  onDialogClose: () => void;
  editTransaction?: TransactionWithAccounts;
  form: UseFormReturn<TransactionFormValues>;
}

// Type for transaction item with nested account
interface TransactionItemWithAccount extends TransactionItem {
  accounts: Pick<Account, 'id' | 'name' | 'code'>;
}

// Type for transaction with nested items
interface TransactionWithItems extends Transaction {
  transaction_items: TransactionItemWithAccount[];
}

export const useTransactionSubmit = ({
  onTransactionComplete,
  onDialogClose,
  editTransaction,
  form
}: UseTransactionSubmitProps): {
  isLoading: boolean;
  handleSubmit: (data: TransactionFormValues) => Promise<void>;
  handleFormSubmit: () => void;
} => {
  const { user, currentCompanyId } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (data: TransactionFormValues): Promise<void> => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "You must be logged in to perform this action.",
        variant: "destructive",
      });
      return;
    }

    if (!currentCompanyId) {
      toast({
        title: "Company Required",
        description: "You must select a company to perform this action.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      if (editTransaction) {
        // Update existing transaction
        await updateTransaction(data, editTransaction.id);
      } else {
        // Create new transaction
        await createTransaction(data, currentCompanyId, user.id);
      }
    } catch (error) {
      console.error('Error saving transaction:', error);
      setIsLoading(false);

      const errorMessage = error instanceof Error
        ? error.message
        : "Failed to save transaction. Please try again.";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const createTransaction = async (data: TransactionFormValues, companyId: string, userId: string): Promise<void> => {
    // Convert form data to database insert format
    const { transaction, items } = transactionFormToInsert(data, companyId, userId);

    // Insert the transaction
    const { data: newTransaction, error: transactionError } = await supabase
      .from('transactions')
      .insert(transaction)
      .select()
      .single();

    if (transactionError) throw transactionError;

    // Add transaction_id to each item
    const itemsWithTransactionId = items.map(item => ({
      ...item,
      transaction_id: newTransaction.id
    }));

    // Insert the transaction items
    const { error: itemsError } = await supabase
      .from('transaction_items')
      .insert(itemsWithTransactionId);

    if (itemsError) throw itemsError;

    // Fetch the complete transaction with account details
    const { data: completeTransaction, error: fetchError } = await supabase
      .from('transactions')
      .select(`
        id,
        transaction_date,
        reference,
        description,
        status,
        created_at,
        transaction_items (
          id,
          account_id,
          description,
          debit,
          credit,
          accounts (
            id,
            name,
            code
          )
        )
      `)
      .eq('id', newTransaction.id)
      .single();

    if (fetchError) throw fetchError;

    // Type assertion for the complete transaction
    const typedTransaction = completeTransaction as unknown as TransactionWithItems;

    // Format the transaction for state update
    const formattedItems = typedTransaction.transaction_items.map(ti => ({
      ...ti,
      account: ti.accounts
    }));

    const formattedTransaction: TransactionWithAccounts = {
      ...typedTransaction,
      date: typedTransaction.transaction_date, // Map transaction_date to date for UI consistency
      items: formattedItems
    };

    setIsLoading(false);

    toast({
      title: "Transaction Added",
      description: `Added new transaction for ${formatUGX(data.amount)}`,
    });

    onTransactionComplete(formattedTransaction);
    onDialogClose();
  };

  const updateTransaction = async (data: TransactionFormValues, transactionId: string): Promise<void> => {
    // Update the transaction record
    const { error: transactionError } = await supabase
      .from('transactions')
      .update({
        transaction_date: data.date.toISOString().split('T')[0],
        description: data.description,
        reference: data.reference || null,
        ...(data.status && { status: data.status })
      })
      .eq('id', transactionId);

    if (transactionError) throw transactionError;

    // Get existing transaction items
    const { data: existingItems, error: itemsQueryError } = await supabase
      .from('transaction_items')
      .select('id, account_id')
      .eq('transaction_id', transactionId);

    if (itemsQueryError) throw itemsQueryError;

    // Find debit and credit items
    const debitItem = existingItems.find(item =>
      item.account_id === data.debitAccount
    );

    const creditItem = existingItems.find(item =>
      item.account_id === data.creditAccount
    );

    // Update or create debit item
    if (debitItem) {
      const { error: debitUpdateError } = await supabase
        .from('transaction_items')
        .update({
          debit: data.amount,
          credit: null,
          description: data.description
        })
        .eq('id', debitItem.id);

      if (debitUpdateError) throw debitUpdateError;
    } else {
      const { error: debitInsertError } = await supabase
        .from('transaction_items')
        .insert({
          transaction_id: transactionId,
          account_id: data.debitAccount,
          debit: data.amount,
          credit: null,
          description: data.description
        });

      if (debitInsertError) throw debitInsertError;
    }

    // Update or create credit item
    if (creditItem) {
      const { error: creditUpdateError } = await supabase
        .from('transaction_items')
        .update({
          debit: null,
          credit: data.amount,
          description: data.description
        })
        .eq('id', creditItem.id);

      if (creditUpdateError) throw creditUpdateError;
    } else {
      const { error: creditInsertError } = await supabase
        .from('transaction_items')
        .insert({
          transaction_id: transactionId,
          account_id: data.creditAccount,
          debit: null,
          credit: data.amount,
          description: data.description
        });

      if (creditInsertError) throw creditInsertError;
    }

    // Fetch the updated transaction with all its details
    const { data: updatedTransaction, error: fetchError } = await supabase
      .from('transactions')
      .select(`
        id,
        transaction_date,
        reference,
        description,
        status,
        created_at,
        transaction_items (
          id,
          account_id,
          description,
          debit,
          credit,
          accounts (
            id,
            name,
            code
          )
        )
      `)
      .eq('id', transactionId)
      .single();

    if (fetchError) throw fetchError;

    // Type assertion for the updated transaction
    const typedTransaction = updatedTransaction as unknown as TransactionWithItems;

    // Format the transaction for state update
    const formattedItems = typedTransaction.transaction_items.map(ti => ({
      ...ti,
      account: ti.accounts
    }));

    const formattedTransaction: TransactionWithAccounts = {
      ...typedTransaction,
      date: typedTransaction.transaction_date, // Map transaction_date to date for UI consistency
      items: formattedItems
    };

    setIsLoading(false);

    toast({
      title: "Transaction Updated",
      description: `Updated transaction for ${formatUGX(data.amount)}`,
    });

    onTransactionComplete(formattedTransaction);
    onDialogClose();
  };

  const handleFormSubmit = (): void => {
    form.handleSubmit(handleSubmit)();
  };

  return {
    isLoading,
    handleSubmit,
    handleFormSubmit
  };
};
