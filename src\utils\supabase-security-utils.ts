/**
 * Utility functions for enhancing Supabase security
 * 
 * These functions help ensure proper permission checks and error handling
 * for Supabase API calls.
 */

import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { usePermissions } from '@/hooks/usePermissions';
import { UserRole } from '@/types/auth';
import { SupabaseClient, PostgrestError } from '@supabase/supabase-js';

/**
 * Type for permission check function
 */
type PermissionCheckFn = () => boolean;

/**
 * Interface for secure query options
 */
interface SecureQueryOptions {
  permissionCheck?: PermissionCheckFn;
  requiredRoles?: UserRole[];
  errorMessage?: string;
}

/**
 * Create a secure Supabase query that checks permissions before execution
 * 
 * @param queryFn The Supabase query function to execute
 * @param options Security options for the query
 * @returns A function that executes the query with permission checks
 */
export const createSecureQuery = <T, P extends unknown[]>(
  queryFn: (supabase: SupabaseClient, ...params: P) => Promise<{ data: T | null; error: PostgrestError | null }>,
  options?: SecureQueryOptions
) => {
  return async (...params: P): Promise<T | null> => {
    // Check permissions if a permission check function is provided
    if (options?.permissionCheck && !options.permissionCheck()) {
      console.error('Permission denied for Supabase query');
      throw new Error(options.errorMessage || 'Permission denied');
    }
    
    try {
      const { data, error } = await queryFn(supabase, ...params);
      
      if (error) {
        console.error('Supabase query error:', error);
        throw error;
      }
      
      return data;
    } catch (err) {
      console.error('Unexpected error in Supabase query:', err);
      throw err;
    }
  };
};

/**
 * Hook to create secure Supabase queries with role-based access control
 */
export const useSecureSupabase = () => {
  const { roles } = useAuth();
  const { hasAnyRole, isAdmin } = usePermissions();
  
  /**
   * Create a secure Supabase query with role-based access control
   * 
   * @param queryFn The Supabase query function to execute
   * @param requiredRoles Roles that are allowed to execute the query
   * @param errorMessage Custom error message for permission denied
   * @returns A function that executes the query with role-based permission checks
   */
  const createRoleBasedQuery = <T, P extends unknown[]>(
    queryFn: (supabase: SupabaseClient, ...params: P) => Promise<{ data: T | null; error: PostgrestError | null }>,
    requiredRoles?: UserRole[],
    errorMessage?: string
  ) => {
    return async (...params: P): Promise<T | null> => {
      // Admin can do anything
      if (isAdmin()) {
        try {
          const { data, error } = await queryFn(supabase, ...params);
          
          if (error) {
            console.error('Supabase query error:', error);
            throw error;
          }
          
          return data;
        } catch (err) {
          console.error('Unexpected error in Supabase query:', err);
          throw err;
        }
      }
      
      // Check if user has any of the required roles
      if (requiredRoles && requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {
        console.error('Permission denied: Required roles not met');
        throw new Error(errorMessage || 'Permission denied: Insufficient privileges');
      }
      
      try {
        const { data, error } = await queryFn(supabase, ...params);
        
        if (error) {
          console.error('Supabase query error:', error);
          throw error;
        }
        
        return data;
      } catch (err) {
        console.error('Unexpected error in Supabase query:', err);
        throw err;
      }
    };
  };
  
  return {
    createRoleBasedQuery,
  };
};

/**
 * Audit a Supabase query for potential security issues
 * 
 * @param queryString The Supabase query string to audit
 * @returns An array of potential security issues
 */
export const auditSupabaseQuery = (queryString: string): string[] => {
  const issues: string[] = [];
  
  // Check for missing company_id filter
  if (
    queryString.includes('.select(') &&
    !queryString.includes('.eq(\'company_id\'') &&
    !queryString.includes('.match({ company_id:')
  ) {
    issues.push('Missing company_id filter: This query may leak data across companies');
  }
  
  // Check for missing permission checks
  if (
    (queryString.includes('.insert(') ||
     queryString.includes('.update(') ||
     queryString.includes('.delete(')) &&
    !queryString.includes('canManage') &&
    !queryString.includes('hasRole') &&
    !queryString.includes('isAdmin') &&
    !queryString.includes('hasAnyRole')
  ) {
    issues.push('Missing permission check: This mutation lacks role-based access control');
  }
  
  // Check for direct auth.user() access without validation
  if (queryString.includes('auth.user()') && !queryString.includes('if (!auth.user())')) {
    issues.push('Unchecked auth.user() access: Validate user authentication before accessing user data');
  }
  
  return issues;
};

/**
 * Create a secure Supabase query with retry logic for network failures
 * 
 * @param queryFn The Supabase query function to execute
 * @param maxRetries Maximum number of retry attempts
 * @param baseDelay Base delay in milliseconds between retries
 * @returns A function that executes the query with retry logic
 */
export const createRetryableQuery = <T, P extends unknown[]>(
  queryFn: (supabase: SupabaseClient, ...params: P) => Promise<{ data: T | null; error: PostgrestError | null }>,
  maxRetries: number = 3,
  baseDelay: number = 1000
) => {
  return async (...params: P): Promise<T | null> => {
    let lastError: PostgrestError | Error | null = null;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const { data, error } = await queryFn(supabase, ...params);
        
        if (error) {
          // Only retry on network-related errors
          if (
            error.code === 'NETWORK_ERROR' ||
            error.code === 'CONNECTION_ERROR' ||
            error.message.includes('network') ||
            error.message.includes('connection')
          ) {
            lastError = error;
            
            // Exponential backoff with jitter
            const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
          
          // For other errors, throw immediately
          console.error('Supabase query error:', error);
          throw error;
        }
        
        return data;
      } catch (err) {
        lastError = err instanceof Error ? err : new Error(String(err));
        
        // Only retry on network-related errors
        if (
          err instanceof Error &&
          (err.message.includes('network') || err.message.includes('connection'))
        ) {
          // Exponential backoff with jitter
          const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
        
        // For other errors, throw immediately
        console.error('Unexpected error in Supabase query:', err);
        throw err;
      }
    }
    
    // If we've exhausted all retries
    console.error(`Failed after ${maxRetries} retries:`, lastError);
    throw lastError;
  };
};
