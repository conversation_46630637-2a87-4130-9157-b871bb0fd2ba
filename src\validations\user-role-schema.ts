
import * as z from 'zod';
import { UserRole } from '@/types/auth';

export const inviteUserSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
});

export const roleAssignmentSchema = z.object({
  userId: z.string().uuid(),
  role: z.enum(['admin', 'accountant', 'manager', 'viewer'] as const),
});

export type InviteUserFormValues = z.infer<typeof inviteUserSchema>;
export type RoleAssignmentValues = z.infer<typeof roleAssignmentSchema>;

export const isValidRole = (role: string): role is UserRole => {
  return ['admin', 'accountant', 'manager', 'viewer'].includes(role);
};
