
# Kaya Finance - Cloud-based Accounting for SMEs

A comprehensive accounting SaaS application designed for small and medium-sized enterprises, with support for local tax regulations and accounting standards.

## Features

- **General Ledger with Ugandan Chart of Accounts**
- **Accounts Payable & Receivable Management**
- **Budget Approval Workflow**
- **Financial Reports (Balance Sheet, P&L, Cash Flow)**
- **Tax Management (VAT, PAYE, Withholding Tax)**
- **Role-based Authentication and Security**
- **Mobile-responsive Design**
- **CSV Export for Transactions and Reports**

## Tech Stack

- **Frontend**: React, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Supabase (Auth, PostgreSQL Database, Functions)
- **State Management**: React Query for server state, React Context for global app state
- **Forms**: React Hook Form with Zod validation
- **Testing**: Jest + React Testing Library

## Development Setup

### Prerequisites

- Node.js (v16 or newer)
- npm or yarn
- Supabase account

### Installation

1. Clone the repository
```sh
git clone <repository-url>
cd kaya-finance-flow
```

2. Install dependencies
```sh
npm install
# or
yarn install
```

3. Set up environment variables by creating a `.env.local` file in the root directory:
```
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_APP_ENV=development
```

4. Start the development server
```sh
npm run dev
# or
yarn dev
```

The application will be available at `http://localhost:8080`.

### Setting up Supabase

1. Create a new Supabase project at [https://supabase.com](https://supabase.com)

2. Get your project URL and anon key from the Supabase dashboard:
   - Go to Project Settings > API
   - Copy the URL and anon key to your `.env.local` file

3. Run the database setup scripts:
   - Navigate to the SQL Editor in your Supabase dashboard
   - Run the scripts in the `supabase/migrations` directory in sequence

4. Set up Row Level Security (RLS) policies:
   - Run the RLS policy scripts in the `supabase/policies` directory
   - These policies ensure proper data access control based on user roles

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `VITE_SUPABASE_URL` | Your Supabase project URL | Yes |
| `VITE_SUPABASE_ANON_KEY` | Your Supabase anonymous key | Yes |
| `VITE_APP_ENV` | Application environment (development, staging, production) | Yes |
| `VITE_API_TIMEOUT` | API request timeout in milliseconds (default: 30000) | No |
| `VITE_SESSION_EXPIRY` | Session expiry time in seconds (default: 3600) | No |

## Database Schema

The application uses the following tables:

- `users` - User accounts and authentication
- `companies` - Company information
- `chart_of_accounts` - Chart of Accounts based on Ugandan accounting standards
- `transactions` - Financial transactions
- `transaction_items` - Individual transaction line items
- `invoices` - Accounts payable and receivable invoices
- `payments` - Invoice payments
- `budgets` - Budget requests
- `budget_approvals` - Approval workflow for budgets
- `tax_rates` - Various tax rates for VAT, PAYE, and withholding taxes

## Testing

The project uses Jest and React Testing Library for testing. Tests are located in the `src/__tests__` directory.

Run tests with:
```sh
npm test               # Run all tests
npm run test:watch     # Run tests in watch mode
npm run test:coverage  # Run tests with coverage report
```

## Deployment

### Production Build

Create a production build with:
```sh
npm run build
# or
yarn build
```

The build artifacts will be stored in the `dist/` directory.

### Deployment Options

#### Vercel

1. Connect your GitHub repository to Vercel
2. Set up the environment variables in the Vercel dashboard
3. Deploy with the following settings:
   - Framework Preset: Vite
   - Build Command: `npm run build`
   - Output Directory: `dist`

#### Netlify

1. Connect your GitHub repository to Netlify
2. Set up the environment variables in the Netlify dashboard
3. Deploy with the following settings:
   - Build Command: `npm run build`
   - Publish Directory: `dist`

#### Docker

A Dockerfile is provided for containerized deployment:

```sh
# Build the Docker image
docker build -t ugaaccounts-pro .

# Run the container
docker run -p 8080:80 ugaaccounts-pro
```

## Project Structure

```
/
├── public/            # Static assets
├── src/
│   ├── __tests__/     # Test files
│   ├── components/    # UI Components
│   │   ├── layout/    # Layout components
│   │   └── ui/        # UI components from shadcn/ui
│   ├── context/       # React context providers
│   ├── hooks/         # Custom React hooks
│   ├── integrations/  # External service integrations
│   ├── lib/           # Utility functions and helpers
│   ├── models/        # TypeScript interfaces and types
│   ├── pages/         # Page components
│   ├── services/      # Business logic services
│   ├── utils/         # Utility functions
│   ├── validations/   # Zod validation schemas
│   ├── App.tsx        # Main application component with routing
│   └── main.tsx       # Entry point
├── supabase/
│   ├── migrations/    # Database migration scripts
│   └── policies/      # RLS policy scripts
├── .env.example       # Example environment variables
├── jest.config.js     # Jest configuration
├── package.json       # Project dependencies and scripts
├── tsconfig.json      # TypeScript configuration
└── vite.config.ts     # Vite configuration
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/my-feature`
3. Commit your changes: `git commit -am 'Add my feature'`
4. Push to the branch: `git push origin feature/my-feature`
5. Submit a pull request

## License

[MIT](https://choosealicense.com/licenses/mit/)
