
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { QuickActions } from "@/components/dashboard/QuickActions";
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { OverviewTab } from "@/components/dashboard/OverviewTab";
import { PerformanceTab } from "@/components/dashboard/PerformanceTab";
import { WidgetGrid } from "@/components/dashboard/WidgetGrid";
import { useState } from "react";

const Dashboard = (): React.JSX.Element => {
  const [dashboardView, setDashboardView] = useState<'classic' | 'custom'>('custom');

  return (
    <div>
      <DashboardHeader
        dashboardView={dashboardView}
        onViewChange={setDashboardView}
      />

      <QuickActions />

      {dashboardView === 'classic' ? (
        <div className="my-6">
          <Tabs className="w-full" defaultValue="overview">
            <TabsList className="mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              <OverviewTab />
            </TabsContent>

            <TabsContent value="performance">
              <PerformanceTab />
            </TabsContent>
          </Tabs>
        </div>
      ) : (
        <div className="my-6">
          <WidgetGrid className="w-full" />
        </div>
      )}
    </div>
  );
};

export default Dashboard;
