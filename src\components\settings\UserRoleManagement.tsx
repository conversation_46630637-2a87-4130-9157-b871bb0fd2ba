
import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { UserRole } from '@/types/auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Search, UserPlus, Shield, AlertCircle } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRoleManagement } from '@/hooks/useRoleManagement';
import { inviteUserSchema } from '@/validations/user-role-schema';

type UserWithRoles = {
  id: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  roles: UserRole[];
}

export function UserRoleManagement() {
  const { roles } = useAuth();
  const [users, setUsers] = useState<UserWithRoles[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const { toast } = useToast();
  const { fetchCompanyUsers, assignUserRole, removeUserRole } = useRoleManagement();

  const form = useForm<z.infer<typeof inviteUserSchema>>({
    resolver: zodResolver(inviteUserSchema),
    defaultValues: {
      email: '',
    },
  });

  // Available roles for assignment
  const availableRoles: UserRole[] = ['admin', 'accountant', 'manager', 'viewer'];
  
  // Check if user is superadmin (for role assignment permissions)
  const isSuperAdmin = roles.includes('admin');

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      const companyUsers = await fetchCompanyUsers();
      
      // Get company ID for the single company
      const { data: company, error: companyError } = await supabase
        .from('companies')
        .select('id')
        .limit(1)
        .single();
        
      if (companyError) {
        console.error('Error fetching company:', companyError);
        toast({
          title: 'Error',
          description: 'Failed to load company information',
          variant: 'destructive'
        });
        return;
      }
      
      const companyId = company.id;
      
      // Fetch user profiles
      if (companyUsers.length > 0) {
        const userIds = companyUsers.map(u => u.userId);
        
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, first_name, last_name')
          .in('id', userIds);
  
        if (profilesError) {
          console.error('Error fetching profiles:', profilesError);
        }
        
        // Map profiles to users
        const usersWithDetails: UserWithRoles[] = companyUsers.map(cu => {
          const profile = profiles?.find(p => p.id === cu.userId);
          
          return {
            id: cu.userId,
            email: `${cu.userId}@example.com`, // Placeholder (would need auth.users data)
            firstName: profile?.first_name || null,
            lastName: profile?.last_name || null,
            roles: cu.roles
          };
        });
        
        setUsers(usersWithDetails);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: 'Error',
        description: 'Failed to load users',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRoleToggle = async (userId: string, role: UserRole, hasRole: boolean) => {
    // Check permissions for role assignment
    // Only superadmin can assign admin role
    // Admin can only assign accountant role
    if (role === 'admin' && !isSuperAdmin) {
      toast({
        title: 'Permission Denied',
        description: 'Only superadmins can manage admin roles',
        variant: 'destructive'
      });
      return;
    }
    
    if (!isSuperAdmin && role !== 'accountant') {
      toast({
        title: 'Permission Denied',
        description: 'You can only manage accountant roles',
        variant: 'destructive'
      });
      return;
    }
    
    try {
      let success = false;
      
      if (hasRole) {
        // Remove role
        success = await removeUserRole(userId, role);
      } else {
        // Add role
        success = await assignUserRole(userId, role);
      }
      
      if (success) {
        // Update local state
        setUsers(users.map(user => 
          user.id === userId 
            ? { 
                ...user, 
                roles: hasRole 
                  ? user.roles.filter(r => r !== role) 
                  : [...user.roles, role] 
              } 
            : user
        ));
      }
    } catch (error) {
      console.error('Error updating role:', error);
    }
  };

  const inviteUser = async (values: z.infer<typeof inviteUserSchema>) => {
    try {
      // In a real implementation, you would:
      // 1. Check if user exists in auth.users
      // 2. If not, create an invitation
      // 3. Send email with signup link
      
      toast({
        title: 'Invitation Sent',
        description: `Invitation email sent to ${values.email}`,
      });
      
      setIsInviteDialogOpen(false);
      form.reset();
    } catch (error) {
      console.error('Error inviting user:', error);
      toast({
        title: 'Error',
        description: 'Failed to send invitation',
        variant: 'destructive'
      });
    }
  };

  const filteredUsers = users.filter(user => {
    const searchLower = searchQuery.toLowerCase();
    return (
      user.email?.toLowerCase().includes(searchLower) ||
      user.firstName?.toLowerCase().includes(searchLower) ||
      user.lastName?.toLowerCase().includes(searchLower)
    );
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>User Management</CardTitle>
        <CardDescription>Manage users and assign roles</CardDescription>
        <div className="flex items-center justify-between mt-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search users..."
              className="pl-8 w-[250px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button onClick={() => setIsInviteDialogOpen(true)}>
            <UserPlus className="mr-2 h-4 w-4" />
            Invite User
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center p-4">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : (
          <>
            <div className="bg-muted/50 rounded-md p-3 mb-4 flex items-center space-x-2">
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
              <p className="text-sm text-muted-foreground">
                {isSuperAdmin 
                  ? "As a superadmin, you can assign any role to users." 
                  : "As an admin, you can only assign the accountant role to users."}
              </p>
            </div>
            
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    {availableRoles.map(role => (
                      <TableHead key={role} className="text-center">
                        <div className="flex flex-col items-center">
                          <span>{role.charAt(0).toUpperCase() + role.slice(1)}</span>
                          {role === 'admin' && !isSuperAdmin && (
                            <Shield className="h-3 w-3 text-muted-foreground mt-1" />
                          )}
                          {!isSuperAdmin && role !== 'accountant' && role !== 'admin' && (
                            <Shield className="h-3 w-3 text-muted-foreground mt-1" />
                          )}
                        </div>
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.length > 0 ? (
                    filteredUsers.map(user => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {user.firstName} {user.lastName}
                            </div>
                            <div className="text-sm text-muted-foreground">{user.email}</div>
                          </div>
                        </TableCell>
                        {availableRoles.map(role => (
                          <TableCell key={role} className="text-center">
                            <Checkbox
                              checked={user.roles.includes(role)}
                              disabled={
                                // Disable if not superadmin and role is admin
                                (role === 'admin' && !isSuperAdmin) ||
                                // Disable if not superadmin and role is not accountant
                                (!isSuperAdmin && role !== 'accountant')
                              }
                              onCheckedChange={(checked) => {
                                handleRoleToggle(user.id, role, user.roles.includes(role));
                              }}
                            />
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={availableRoles.length + 1} className="text-center">
                        No users found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </>
        )}
      </CardContent>

      {/* Invite User Dialog */}
      <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Invite New User</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(inviteUser)} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsInviteDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Send Invitation</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
