
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { 
  CalendarPlus, 
  CalendarX, 
  MoreHorizontal,
  CalendarCheck
} from 'lucide-react';
import { StatusBadge } from './StatusBadge';
import { PriorityIndicator } from './PriorityIndicator';

type BudgetRequest = {
  id: string;
  department: string;
  title: string;
  amount: number;
  submittedBy: string;
  submittedDate: string;
  status: string;
  priority: string;
};

type BudgetRequestsTableProps = {
  requests: BudgetRequest[];
  formatCurrency: (amount: number) => string;
};

export const BudgetRequestsTable = ({ requests, formatCurrency }: BudgetRequestsTableProps) => {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[120px]">ID</TableHead>
            <TableHead>Details</TableHead>
            <TableHead>Department</TableHead>
            <TableHead className="hidden md:table-cell">Priority</TableHead>
            <TableHead className="hidden md:table-cell">Submitted By</TableHead>
            <TableHead className="hidden md:table-cell">Date</TableHead>
            <TableHead className="text-right">Amount</TableHead>
            <TableHead className="w-[100px] text-center">Status</TableHead>
            <TableHead className="w-[40px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {requests.length > 0 ? (
            requests.map((request) => (
              <TableRow key={request.id}>
                <TableCell className="font-mono text-xs">{request.id}</TableCell>
                <TableCell>
                  <div className="font-medium">{request.title}</div>
                  <div className="md:hidden text-xs text-muted-foreground mt-1">
                    {request.submittedBy} · {request.submittedDate}
                  </div>
                </TableCell>
                <TableCell>{request.department}</TableCell>
                <TableCell className="hidden md:table-cell">
                  <PriorityIndicator priority={request.priority} />
                </TableCell>
                <TableCell className="hidden md:table-cell">{request.submittedBy}</TableCell>
                <TableCell className="hidden md:table-cell">{request.submittedDate}</TableCell>
                <TableCell className="text-right">{formatCurrency(request.amount)}</TableCell>
                <TableCell className="text-center">
                  <StatusBadge status={request.status} />
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Actions</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>View Details</DropdownMenuItem>
                      {request.status === 'pending' && (
                        <>
                          <DropdownMenuItem className="text-green-700">
                            <CalendarCheck className="mr-2 h-4 w-4" /> Approve
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-red-700">
                            <CalendarX className="mr-2 h-4 w-4" /> Reject
                          </DropdownMenuItem>
                        </>
                      )}
                      {request.status === 'approved' && (
                        <DropdownMenuItem className="text-amber-700">
                          <CalendarPlus className="mr-2 h-4 w-4" /> Request Revision
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={9} className="h-24 text-center">
                No budget requests found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};
