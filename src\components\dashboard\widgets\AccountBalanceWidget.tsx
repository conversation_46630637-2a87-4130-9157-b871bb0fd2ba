import React, { useState, useEffect } from 'react';
import { TypedDashboardWidget, AccountBalanceSettings } from '@/types/dashboard';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { format } from 'date-fns';

interface AccountBalanceWidgetProps {
  widget: TypedDashboardWidget;
}

interface AccountBalance {
  id: string;
  name: string;
  balance: number;
  type: string;
}

interface BalanceHistory {
  date: string;
  balance: number;
}

export function AccountBalanceWidget({ widget }: AccountBalanceWidgetProps): JSX.Element {
  const settings = widget.widget_settings as AccountBalanceSettings;
  const { currentCompanyId } = useAuth();
  const [loading, setLoading] = useState(true);
  const [accounts, setAccounts] = useState<AccountBalance[]>([]);
  const [balanceHistory, setBalanceHistory] = useState<BalanceHistory[]>([]);
  const [error, setError] = useState<string | null>(null);

  const showChart = settings.showChart !== false;
  const period = settings.period || 'monthly';
  const useFiscalYear = settings.useFiscalYear !== false;
  const accountTypes = settings.accountTypes || [];

  useEffect(() => {
    const fetchAccountData = async () => {
      if (!currentCompanyId) return;

      try {
        setLoading(true);
        setError(null);

        // Get the date range based on the selected period
        const now = new Date();
        let startDate = new Date();

        if (period === 'weekly') {
          startDate.setDate(now.getDate() - 28); // 4 weeks
        } else if (period === 'monthly') {
          startDate.setMonth(now.getMonth() - 6); // 6 months
        } else if (period === 'quarterly') {
          startDate.setMonth(now.getMonth() - 12); // 4 quarters
        } else if (period === 'yearly') {
          startDate.setFullYear(now.getFullYear() - 3); // 3 years
        }

        // If using fiscal year, adjust the date range
        if (useFiscalYear) {
          // Fetch company fiscal year settings
          const { data: company, error: companyError } = await supabase
            .from('companies')
            .select('fiscal_year_start')
            .eq('id', currentCompanyId)
            .single();

          if (companyError) {
            console.error('Error fetching fiscal year settings:', companyError);
          } else if (company?.fiscal_year_start) {
            const fiscalYearStart = new Date(company.fiscal_year_start);
            const currentYear = now.getFullYear();

            // Create a date for this year's fiscal start
            const thisYearFiscalStart = new Date(
              currentYear,
              fiscalYearStart.getMonth(),
              fiscalYearStart.getDate()
            );

            // If current date is before this year's fiscal start, use last year's fiscal start
            if (now < thisYearFiscalStart) {
              startDate = new Date(
                currentYear - 1,
                fiscalYearStart.getMonth(),
                fiscalYearStart.getDate()
              );
            } else {
              startDate = thisYearFiscalStart;
            }
          }
        }

        // Format dates for the query
        const startDateStr = startDate.toISOString().split('T')[0];
        const endDateStr = now.toISOString().split('T')[0];

        // Fetch account balances
        let query = supabase
          .from('accounts')
          .select('*')
          .eq('company_id', currentCompanyId);

        // Filter by account types if specified
        if (accountTypes.length > 0) {
          query = query.in('type', accountTypes);
        }

        const { data: accountsData, error: accountsError } = await query;

        if (accountsError) {
          throw accountsError;
        }

        // Fetch balance history
        const { data: historyData, error: historyError } = await supabase
          .rpc('get_account_balance_history', {
            p_company_id: currentCompanyId,
            p_start_date: startDateStr,
            p_end_date: endDateStr,
            p_period: period
          });

        if (historyError) {
          console.error('Error fetching balance history:', historyError);
        }

        // Process account data
        if (!accountsData || accountsData.length === 0) {
          setAccounts([]);
        } else {
          // Transform account data to include calculated balances
          const accountsWithBalances = await Promise.all(accountsData.map(async (account) => {
            // Get transaction items for this account to calculate balance
            const { data: transactionItems, error: itemsError } = await supabase
              .from('transaction_items')
              .select(`
                id,
                debit,
                credit,
                transaction_id,
                transactions (
                  id,
                  transaction_date
                )
              `)
              .eq('account_id', account.id)
              .order('transactions.transaction_date', { ascending: false });

            if (itemsError) {
              console.error(`Error fetching transactions for account ${account.id}:`, itemsError);
              return {
                id: account.id,
                name: account.name,
                balance: 0,
                type: account.account_type_id || 'unknown'
              };
            }

            // Calculate balance based on debits and credits
            let balance = 0;
            if (transactionItems && transactionItems.length > 0) {
              balance = transactionItems.reduce((sum, item) => {
                return sum + (item.debit || 0) - (item.credit || 0);
              }, 0);
            }

            return {
              id: account.id,
              name: account.name,
              balance,
              type: account.account_type_id || 'unknown'
            };
          }));

          setAccounts(accountsWithBalances);
        }

        // Generate balance history if RPC data is not available
        if (!historyData || historyData.length === 0) {
          // Get all transactions to generate balance history
          const { data: transactions, error: txError } = await supabase
            .from('transactions')
            .select(`
              id,
              transaction_date,
              transaction_items (
                id,
                debit,
                credit
              )
            `)
            .eq('company_id', currentCompanyId)
            .gte('transaction_date', startDateStr)
            .lte('transaction_date', endDateStr)
            .order('transaction_date', { ascending: true });

          if (txError) {
            console.error('Error fetching transactions for balance history:', txError);
            setBalanceHistory([]);
          } else if (!transactions || transactions.length === 0) {
            setBalanceHistory([]);
          } else {
            // Group transactions by month/period
            const balanceByPeriod = new Map<string, number>();
            let runningBalance = 0;

            // Get starting balance (sum of all transactions before start date)
            const { data: priorTransactions, error: priorTxError } = await supabase
              .from('transactions')
              .select(`
                id,
                transaction_items (
                  id,
                  debit,
                  credit
                )
              `)
              .eq('company_id', currentCompanyId)
              .lt('transaction_date', startDateStr);

            if (!priorTxError && priorTransactions) {
              priorTransactions.forEach(tx => {
                if (!tx.transaction_items) return;

                tx.transaction_items.forEach(item => {
                  runningBalance += (item.debit || 0) - (item.credit || 0);
                });
              });
            }

            // Process transactions by period
            transactions.forEach(tx => {
              if (!tx.transaction_date || !tx.transaction_items) return;

              // Format date based on period
              let periodKey: string;
              const txDate = new Date(tx.transaction_date);

              if (period === 'weekly') {
                // Get the week number
                const weekNum = Math.floor(txDate.getDate() / 7) + 1;
                periodKey = `${txDate.getFullYear()}-${txDate.getMonth() + 1}-W${weekNum}`;
              } else if (period === 'monthly') {
                periodKey = `${txDate.getFullYear()}-${txDate.getMonth() + 1}`;
              } else if (period === 'quarterly') {
                const quarter = Math.floor(txDate.getMonth() / 3) + 1;
                periodKey = `${txDate.getFullYear()}-Q${quarter}`;
              } else {
                periodKey = `${txDate.getFullYear()}`;
              }

              // Calculate transaction impact on balance
              let txAmount = 0;
              tx.transaction_items.forEach(item => {
                txAmount += (item.debit || 0) - (item.credit || 0);
              });

              runningBalance += txAmount;

              // Store the balance for this period
              balanceByPeriod.set(periodKey, runningBalance);
            });

            // Convert to array and format for chart
            const historyArray = Array.from(balanceByPeriod.entries()).map(([date, balance]) => ({
              date,
              balance
            }));

            setBalanceHistory(historyArray);
          }
        } else {
          setBalanceHistory(historyData);
        }
      } catch (err: any) {
        console.error('Error fetching account data:', err);
        setError(err.message || 'Failed to load account data');
      } finally {
        setLoading(false);
      }
    };

    fetchAccountData();
  }, [currentCompanyId, period, useFiscalYear, accountTypes.join(',')]);

  // Format currency for display
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format date for display
  const formatDate = (dateString: string): string => {
    try {
      return format(new Date(dateString), 'MMM yyyy');
    } catch (e) {
      return dateString;
    }
  };

  // Calculate total balance
  const totalBalance = accounts.reduce((sum, account) => sum + account.balance, 0);

  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background p-2 border rounded shadow-sm">
          <p className="font-medium">{formatDate(label)}</p>
          <p className="text-sm">
            Balance: {formatCurrency(payload[0].value)}
          </p>
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Skeleton className="h-[80%] w-[80%] rounded-md" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center text-center p-4">
        <p className="text-sm text-muted-foreground">
          Error loading account data. Please try again later.
        </p>
      </div>
    );
  }

  if (accounts.length === 0) {
    return (
      <div className="h-full flex items-center justify-center text-center p-4">
        <p className="text-sm text-muted-foreground">
          No accounts found.
        </p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <div className="mb-4">
        <h3 className="text-sm font-medium mb-1">Total Balance</h3>
        <p className="text-2xl font-bold">{formatCurrency(totalBalance)}</p>
      </div>

      {showChart && balanceHistory.length > 0 ? (
        <div className="flex-1">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={balanceHistory}
              margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis
                dataKey="date"
                tickFormatter={(value) => formatDate(value)}
                tick={{ fontSize: 10 }}
              />
              <YAxis
                tickFormatter={(value) => formatCurrency(value).replace('UGX', '')}
                tick={{ fontSize: 10 }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line
                type="monotone"
                dataKey="balance"
                stroke="#3b82f6"
                activeDot={{ r: 8 }}
                dot={{ r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      ) : (
        <div className="flex-1 overflow-auto">
          <h3 className="text-sm font-medium mb-2">Accounts</h3>
          <ul className="space-y-2">
            {accounts.map((account) => (
              <li key={account.id} className="flex justify-between items-center p-2 rounded-md hover:bg-muted/50">
                <div>
                  <p className="font-medium text-sm">{account.name}</p>
                  <p className="text-xs text-muted-foreground capitalize">{account.type}</p>
                </div>
                <p className={`font-medium ${account.balance < 0 ? 'text-red-600' : ''}`}>
                  {formatCurrency(account.balance)}
                </p>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
