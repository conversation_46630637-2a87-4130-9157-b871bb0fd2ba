import { z } from 'zod';
import { InvoiceInsert, InvoiceStatus, Constants } from '@/types/index';

/**
 * Tax rate validation for Uganda (VAT is currently 18%)
 */
const ugandanTaxRates = {
  standard: 18, // Standard VAT rate
  reduced: 0,   // Some essential goods
  exempt: 0,    // Exempt goods and services
};

// Currency code validation
const currencyCodes = ['UGX', 'USD', 'EUR', 'GBP', 'KES', 'TZS', 'RWF'] as const;

// Invoice item schema
const invoiceItemSchema = z.object({
  description: z.string()
    .min(1, { message: "Item description is required" })
    .max(200, { message: "Description must be less than 200 characters" }),

  quantity: z.number()
    .positive({ message: "Quantity must be positive" })
    .max(9999999, { message: "Quantity is too large" }),

  price: z.number()
    .nonnegative({ message: "Price must be non-negative" })
    .max(1000000000, { message: "Price is too large" }),

  taxable: z.boolean().default(true),

  discount: z.number()
    .min(0, { message: "Discount cannot be negative" })
    .max(100, { message: "Discount cannot exceed 100%" })
    .default(0),

  unit: z.string()
    .max(20, { message: "Unit must be less than 20 characters" })
    .optional(),
});

// Main invoice schema
export const invoiceSchema = z.object({
  // Basic invoice information
  invoice_number: z.string()
    .min(1, { message: "Invoice number is required" })
    .max(50, { message: "Invoice number must be less than 50 characters" })
    .regex(/^[A-Za-z0-9\-/]+$/, {
      message: "Invoice number can only contain letters, numbers, hyphens, and forward slashes"
    }),

  customer: z.string()
    .min(1, { message: "Customer name is required" })
    .max(100, { message: "Customer name must be less than 100 characters" }),

  customer_id: z.string().uuid({ message: "Invalid customer ID" }).optional(),

  // Financial information
  amount: z.number()
    .min(0, { message: "Amount must be a positive number" })
    .max(1000000000, { message: "Amount is too large" }),

  currency: z.enum(currencyCodes, {
    required_error: "Currency is required",
    invalid_type_error: "Invalid currency code"
  }).default('UGX'),

  // Date information
  date: z.date({
    required_error: "Issue date is required",
    invalid_type_error: "Invalid issue date format"
  }),

  dueDate: z.date({
    required_error: "Due date is required",
    invalid_type_error: "Invalid due date format"
  }).superRefine((dueDate, ctx) => {
    // We need to access the form data in a type-safe way
    // Accessing form data through proper Zod context API
    const formData = ctx.path.length === 0 ? ctx : undefined;
    const issueDate = formData ? (ctx as { data?: { date: Date } }).data?.date : undefined;

    if (issueDate && dueDate < issueDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Due date cannot be before issue date",
      });
      return false;
    }

    // Due date should not be more than 1 year in the future
    const oneYearFromNow = new Date();
    oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);

    if (dueDate > oneYearFromNow) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Due date cannot be more than 1 year in the future",
      });
      return false;
    }

    return true;
  }),

  // Status information - using the database enum
  status: z.enum(Constants.public.Enums.invoice_status as [InvoiceStatus, ...InvoiceStatus[]], {
    required_error: "Status is required",
    invalid_type_error: "Invalid invoice status"
  }).default('draft'),

  // Contact information
  email: z.string()
    .email({ message: "Please enter a valid email address" })
    .max(100, { message: "Email must be less than 100 characters" })
    .optional()
    .or(z.literal('')),

  phone: z.string()
    .regex(/^(\+\d{1,3})?[\s.-]?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/, {
      message: "Please enter a valid phone number"
    })
    .optional()
    .or(z.literal('')),

  // Additional information
  notes: z.string()
    .max(1000, { message: "Notes must be less than 1000 characters" })
    .optional(),

  terms: z.string()
    .max(1000, { message: "Terms must be less than 1000 characters" })
    .optional(),

  // Tax information
  taxRate: z.number()
    .min(0, { message: "Tax rate cannot be negative" })
    .max(100, { message: "Tax rate cannot exceed 100%" })
    .default(ugandanTaxRates.standard),

  taxType: z.enum(["standard", "reduced", "exempt"], {
    invalid_type_error: "Invalid tax type"
  }).default("standard"),

  taxIdentificationNumber: z.string()
    .max(50, { message: "Tax ID must be less than 50 characters" })
    .optional()
    .or(z.literal('')),

  // Line items
  items: z.array(invoiceItemSchema)
    .min(1, { message: "At least one item is required" })
    .default([]),

  // Payment information
  paymentTerms: z.string()
    .max(100, { message: "Payment terms must be less than 100 characters" })
    .optional(),

  paymentInstructions: z.string()
    .max(500, { message: "Payment instructions must be less than 500 characters" })
    .optional(),
});

/**
 * Type for invoice form values derived from the Zod schema
 */
export type InvoiceFormValues = z.infer<typeof invoiceSchema>;

/**
 * Convert form values to database insert type
 *
 * @param values Form values from the invoice form
 * @param companyId ID of the company
 * @param userId ID of the user creating the invoice
 * @returns Invoice insert object ready for database insertion
 */
export function invoiceFormToInsert(
  values: InvoiceFormValues,
  companyId: string,
  userId: string
): InvoiceInsert {
  return {
    invoice_number: values.invoice_number,
    company_id: companyId,
    customer_id: values.customer_id,
    amount: values.amount,
    tax_amount: values.tax_amount || calculateInvoiceTax(values.amount, values.taxRate),
    total_amount: values.total_amount || calculateInvoiceTotal(values.amount, values.tax_amount || calculateInvoiceTax(values.amount, values.taxRate)),
    currency: values.currency,
    exchange_rate: values.exchange_rate || 1,
    issue_date: values.issue_date.toISOString().split('T')[0],
    due_date: values.dueDate.toISOString().split('T')[0],
    status: values.status,
    notes: values.notes || null,
    created_by: userId,
  };
}

// Helper functions for tax calculations
export const calculateItemTotal = (quantity: number, price: number): number => {
  return quantity * price;
};

export const calculateTax = (amount: number, taxRate: number): number => {
  return (amount * taxRate) / 100;
};

export const calculateInvoiceSubtotal = (items: InvoiceFormValues['items']): number => {
  return items.reduce((sum, item) => sum + calculateItemTotal(item.quantity, item.price), 0);
};

export const calculateInvoiceTax = (subtotal: number, taxRate: number): number => {
  return calculateTax(subtotal, taxRate);
};

export const calculateInvoiceTotal = (subtotal: number, tax: number): number => {
  return subtotal + tax;
};

export const getUgandanTaxRate = (type: "standard" | "reduced" | "exempt"): number => {
  return ugandanTaxRates[type];
};
