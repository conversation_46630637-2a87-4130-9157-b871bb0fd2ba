
import { Card, CardHeader, CardDescription, CardTitle } from '@/components/ui/card';
import { Invoice } from '@/types/invoice';

interface SummaryCardsProps {
  invoices: Invoice[];
}

// Helper function
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

export const SummaryCards = ({ invoices }: SummaryCardsProps): React.JSX.Element => {
  // Calculate summary metrics
  const totalReceivables = invoices.reduce((sum, inv) => sum + inv.amount, 0);
  const overdueAmount = invoices
    .filter(inv => inv.status === 'overdue')
    .reduce((sum, inv) => sum + inv.amount, 0);
  const paidAmount = invoices
    .filter(inv => inv.status === 'paid')
    .reduce((sum, inv) => sum + inv.amount, 0);
  const pendingAmount = invoices
    .filter(inv => inv.status === 'pending')
    .reduce((sum, inv) => sum + inv.amount, 0);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardHeader className="pb-2">
          <CardDescription>Total Receivables</CardDescription>
          <CardTitle className="text-2xl">{formatCurrency(totalReceivables)}</CardTitle>
        </CardHeader>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardDescription>Overdue</CardDescription>
          <CardTitle className="text-2xl text-red-600">{formatCurrency(overdueAmount)}</CardTitle>
        </CardHeader>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardDescription>Pending</CardDescription>
          <CardTitle className="text-2xl text-yellow-600">{formatCurrency(pendingAmount)}</CardTitle>
        </CardHeader>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardDescription>Paid</CardDescription>
          <CardTitle className="text-2xl text-green-600">{formatCurrency(paidAmount)}</CardTitle>
        </CardHeader>
      </Card>
    </div>
  );
};
