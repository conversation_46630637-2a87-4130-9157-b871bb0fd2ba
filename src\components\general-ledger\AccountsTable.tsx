
import React from 'react';
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from '@/components/ui/table';
import { AccountWithBalance } from '@/types/index';

// Utility function to format currency in UGX
const formatUGX = (amount: number): string => {
  return new Intl.NumberFormat('en-UG', {
    style: 'currency',
    currency: 'UGX',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

interface AccountsTableProps {
  accounts: AccountWithBalance[];
  searchTerm: string;
}

const AccountsTable: React.FC<AccountsTableProps> = ({
  accounts,
  searchTerm
}): React.JSX.Element => {
  // Filter accounts based on search term
  const filteredAccounts = accounts.filter(account =>
    account.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (account.account_types?.name && account.account_types.name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Helper function to get account type display name
  const getAccountTypeName = (account: AccountWithBalance): string => {
    return account.account_types?.name || 'Unknown';
  };

  // Helper function to get normal balance (debit/credit)
  const getNormalBalance = (account: AccountWithBalance): string => {
    return account.account_types?.normal_balance || 'Unknown';
  };

  return (
    <div className="overflow-x-auto w-full">
      <Table className="w-full">
        <TableHeader>
          <TableRow>
            <TableHead className="w-[100px]">Account #</TableHead>
            <TableHead>Account Name</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Normal Balance</TableHead>
            <TableHead className="text-right">Balance</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredAccounts.map((account) => (
            <TableRow key={account.id}>
              <TableCell className="font-medium">{account.code}</TableCell>
              <TableCell>{account.name}</TableCell>
              <TableCell>{getAccountTypeName(account)}</TableCell>
              <TableCell className="capitalize">{getNormalBalance(account)}</TableCell>
              <TableCell className="text-right font-medium">
                {formatUGX(account.balance)}
              </TableCell>
            </TableRow>
          ))}
          {filteredAccounts.length === 0 && (
            <TableRow>
              <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                No accounts found matching your search criteria
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default AccountsTable;
