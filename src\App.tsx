import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from '@/components/ui/toaster';
import { Toaster as Sonner } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import { ThemeProvider } from '@/components/theme-provider';
import { AuthProvider, useAuth } from '@/context/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { SessionTimeoutHandler } from '@/components/auth/SessionTimeoutHandler';
import OnboardingRedirect from '@/components/auth/OnboardingRedirect';
import { ErrorBoundary } from '@/components/error/ErrorBoundary';
import DashboardLayout from '@/components/layout/DashboardLayout';
import LoadingScreen from '@/components/ui/LoadingScreen';
import Auth from '@/pages/Auth';
import Dashboard from '@/pages/Dashboard';
import GeneralLedger from '@/pages/GeneralLedger';
import AccountsPayable from '@/pages/AccountsPayable';
import AccountsReceivable from '@/pages/AccountsReceivable';
import Budgets from '@/pages/Budgets';
import Reports from '@/pages/reports';
import Settings from '@/pages/Settings';
import Profile from '@/pages/Profile';
import NotFound from '@/pages/NotFound';
import Unauthorized from '@/pages/Unauthorized';
import Onboarding from '@/pages/Onboarding';
import GuestLanding from '@/pages/GuestLanding';
import Terms from '@/pages/Terms';
import PrivacyPolicy from '@/pages/PrivacyPolicy';

// Route configuration
const ROUTES = {
  PUBLIC: [
    { path: '/', element: <GuestLanding /> },
    { path: '/login', element: <Auth /> },
    { path: '/terms', element: <Terms /> },
    { path: '/privacy', element: <PrivacyPolicy /> },
    { path: '/unauthorized', element: <Unauthorized /> },
  ],
  PROTECTED: [
    { path: '/onboarding', element: <Onboarding /> },
    { path: '/dashboard', element: <Dashboard /> },
    { path: '/general-ledger', element: <GeneralLedger /> },
    { path: '/accounts-payable', element: <AccountsPayable /> },
    { path: '/accounts-receivable', element: <AccountsReceivable /> },
    { path: '/budgets', element: <Budgets /> },
    { path: '/reports', element: <Reports /> },
    { path: '/profile', element: <Profile /> },
  ],
  ADMIN: [
    { path: '/settings', element: <Settings /> }
  ]
};

// Query client configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000,
      refetchOnWindowFocus: false,
    },
  },
});

const logError = (error: Error, errorInfo: React.ErrorInfo): void => {
  console.error('Application error:', error);
  console.error('Component stack:', errorInfo.componentStack);
  // TODO: Add error reporting service integration
};

const AppRoutes = (): JSX.Element => {
  const { loading } = useAuth();

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <>
      <SessionTimeoutHandler
        timeout={30 * 60 * 1000}
        warningTime={2 * 60 * 1000}
      />
      <OnboardingRedirect />
      <Routes>
        {/* Public routes */}
        {ROUTES.PUBLIC.map((route) => (
          <Route
            key={route.path}
            path={route.path}
            element={<ErrorBoundary onError={logError}>{route.element}</ErrorBoundary>}
          />
        ))}

        {/* Protected routes */}
        <Route element={<ProtectedRoute />}>
          <Route element={<DashboardLayout />}>
            {ROUTES.PROTECTED.map((route) => (
              <Route
                key={route.path}
                path={route.path}
                element={<ErrorBoundary onError={logError}>{route.element}</ErrorBoundary>}
              />
            ))}

            {/* Admin-only routes */}
            <Route element={<ProtectedRoute requiredRoles={['admin']} />}>
              {ROUTES.ADMIN.map((route) => (
                <Route
                  key={route.path}
                  path={route.path}
                  element={<ErrorBoundary onError={logError}>{route.element}</ErrorBoundary>}
                />
              ))}
            </Route>
          </Route>
        </Route>

        {/* Catch-all route */}
        <Route path="*" element={<ErrorBoundary onError={logError}><NotFound /></ErrorBoundary>} />
      </Routes>
    </>
  );
};

const App = (): JSX.Element => {
  return (
    <ErrorBoundary onError={logError}>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
          <TooltipProvider>
            <AuthProvider>
              <BrowserRouter>
                <AppRoutes />
              </BrowserRouter>
            </AuthProvider>
            <Toaster />
            <Sonner />
          </TooltipProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;