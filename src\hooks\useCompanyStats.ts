
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { useToast } from './use-toast';

export interface CompanyStats {
  totalRevenue: number;
  payablesDue: number;
  receivables: number;
  activeClients: number;
  revenueChangePercent: number;
  pendingInvoices: number;
  outstandingInvoices: number;
  newClients: number;
}

export const useCompanyStats = (): { stats: CompanyStats; isLoading: boolean } => {
  const [stats, setStats] = useState<CompanyStats>({
    totalRevenue: 0,
    payablesDue: 0,
    receivables: 0,
    activeClients: 0,
    revenueChangePercent: 0,
    pendingInvoices: 0,
    outstandingInvoices: 0,
    newClients: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const { currentCompanyId } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    const fetchCompanyStats = async (): Promise<void> => {
      if (!currentCompanyId) return;

      try {
        setIsLoading(true);

        // Fetch revenue data (sum of credit_amount from journal entries for Revenue accounts)
        const { data: revenueData, error: revenueError } = await supabase
          .from('account_balances')
          .select('balance')
          .eq('company_id', currentCompanyId)
          .eq('category', 'Revenue');

        if (revenueError) throw revenueError;

        // Calculate total revenue
        const totalRevenue = revenueData?.reduce((sum, item) => sum + item.balance, 0) || 0;

        // Fetch accounts payable data
        const { data: payablesData, error: payablesError } = await supabase
          .from('bills')
          .select('total_amount')
          .eq('company_id', currentCompanyId)
          .in('status', ['pending', 'approved', 'overdue']);

        if (payablesError) throw payablesError;

        // Calculate total payables
        const payablesDue = payablesData?.reduce((sum, item) => sum + item.total_amount, 0) || 0;

        // Fetch accounts receivable data
        const { data: receivablesData, error: receivablesError } = await supabase
          .from('invoices')
          .select('total_amount')
          .eq('company_id', currentCompanyId)
          .in('status', ['sent', 'overdue']);

        if (receivablesError) throw receivablesError;

        // Calculate total receivables
        const receivables = receivablesData?.reduce((sum, item) => sum + item.total_amount, 0) || 0;

        // Count pending invoices
        const pendingInvoices = payablesData?.length || 0;

        // Count outstanding invoices
        const outstandingInvoices = receivablesData?.length || 0;

        // Fetch active clients count
        const { count: activeClients, error: clientsError } = await supabase
          .from('customers')
          .select('id', { count: 'exact', head: true })
          .eq('company_id', currentCompanyId)
          .eq('is_active', true);

        if (clientsError) throw clientsError;

        // Count new clients (registered in the last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const thirtyDaysAgoStr = thirtyDaysAgo.toISOString();

        const { count: newClients, error: newClientsError } = await supabase
          .from('customers')
          .select('id', { count: 'exact', head: true })
          .eq('company_id', currentCompanyId)
          .gt('created_at', thirtyDaysAgoStr);

        if (newClientsError) throw newClientsError;

        // Mock revenue change percentage (in a real app, would compare to previous period)
        // In a full implementation, we would compare current period to previous period
        const revenueChangePercent = 20.1;

        setStats({
          totalRevenue,
          payablesDue,
          receivables,
          activeClients: activeClients || 0,
          revenueChangePercent,
          pendingInvoices,
          outstandingInvoices,
          newClients: newClients || 0,
        });
      } catch (error) {
        console.error('Error fetching company statistics:', error);
        toast({
          title: "Error loading dashboard data",
          description: "Could not fetch company statistics. Please try again later.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCompanyStats();
  }, [currentCompanyId, toast]);

  return { stats, isLoading };
};
