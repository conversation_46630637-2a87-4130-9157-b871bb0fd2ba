import { useState } from 'react';
import { format } from 'date-fns';
import { Printer, X } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RecordBillPaymentDialog } from './record-bill-payment-dialog';
import { BillApprovalDialog } from './bill-approval-dialog';
import { BillItems } from './bill-items';
import { BillStatusBadge } from './bill-status-badge';

interface BillDetailProps {
  bill: any; // Replace with BillWithRelations type
  onClose: () => void;
  onRecordPayment: (billId: string, paymentData: {
    amount: number;
    paymentDate: Date;
    paymentMethod: string;
    reference?: string;
    notes?: string;
  }) => void;
  onApproveBill: (billId: string, notes: string) => Promise<void>;
  onRejectBill: (billId: string, notes: string) => Promise<void>;
  isRecordingPayment: boolean;
  isProcessingApproval: boolean;
}

/**
 * Component to display bill details
 */
export function BillDetail({
  bill,
  onClose,
  onRecordPayment,
  onApproveBill,
  onRejectBill,
  isRecordingPayment,
  isProcessingApproval
}: BillDetailProps) {
  const [activeTab, setActiveTab] = useState('details');

  // Format currency as UGX
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Calculate total paid amount
  const totalPaid = bill.bill_payments
    ? bill.bill_payments.reduce((sum: number, payment: any) => sum + payment.amount, 0)
    : 0;

  // Calculate remaining amount
  const remainingAmount = bill.total_amount - totalPaid;

  // Handle print
  const handlePrint = () => {
    window.print();
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle>Bill #{bill.bill_number}</DialogTitle>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" onClick={handlePrint}>
              <Printer className="h-4 w-4" />
            </Button>
            <DialogClose asChild>
              <Button variant="outline" size="icon">
                <X className="h-4 w-4" />
              </Button>
            </DialogClose>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="payments">Payments</TabsTrigger>
            <TabsTrigger value="history">Approval History</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="p-0">
            <CardContent className="pt-6">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">Vendor</h3>
                  <p className="text-lg font-medium">{bill.vendor?.name || 'Unknown Vendor'}</p>
                  {bill.vendor?.email && <p className="text-sm">{bill.vendor.email}</p>}
                  {bill.vendor?.phone && <p className="text-sm">{bill.vendor.phone}</p>}
                </div>

                <div className="text-right">
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">Bill Information</h3>
                  <p className="text-lg font-medium">#{bill.bill_number}</p>
                  <p className="text-sm">
                    Issue Date: {format(new Date(bill.issue_date), 'MMM d, yyyy')}
                  </p>
                  <p className="text-sm">
                    Due Date: {format(new Date(bill.due_date), 'MMM d, yyyy')}
                  </p>
                </div>
              </div>

              <div className="mt-8">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Bill Items</h3>
                <BillItems items={bill.bill_items || []} formatCurrency={formatCurrency} />
              </div>

              <div className="mt-6 flex justify-between">
                <div>
                  <div className="flex items-center space-x-2">
                    <BillStatusBadge status={bill.status} />

                    {bill.status === 'pending' && (
                      <BillApprovalDialog
                        billId={bill.id}
                        billNumber={bill.bill_number}
                        onApprove={onApproveBill}
                        onReject={onRejectBill}
                        isSubmitting={isProcessingApproval}
                      />
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className="font-medium">Subtotal:</span>
                      <span>{formatCurrency(bill.amount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Tax:</span>
                      <span>{formatCurrency(bill.tax_amount)}</span>
                    </div>
                    <div className="flex justify-between text-lg font-bold">
                      <span>Total:</span>
                      <span>{formatCurrency(bill.total_amount)}</span>
                    </div>
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <span>Paid:</span>
                      <span>{formatCurrency(totalPaid)}</span>
                    </div>
                    <div className="flex justify-between font-medium">
                      <span>Balance:</span>
                      <span>{formatCurrency(remainingAmount)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {bill.notes && (
                <div className="mt-6">
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">Notes</h3>
                  <p className="text-sm bg-muted p-3 rounded-md">{bill.notes}</p>
                </div>
              )}
            </CardContent>
          </TabsContent>

          <TabsContent value="payments" className="p-0">
            <CardContent className="pt-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">Payment History</h3>
                {(bill.status === 'approved' || bill.status === 'overdue') && (
                  <RecordBillPaymentDialog
                    bill={bill}
                    onRecordPayment={onRecordPayment}
                    isSubmitting={isRecordingPayment}
                  />
                )}
              </div>

              {bill.bill_payments && bill.bill_payments.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Method</TableHead>
                      <TableHead>Reference</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {bill.bill_payments.map((payment: any) => (
                      <TableRow key={payment.id}>
                        <TableCell>{format(new Date(payment.payment_date), 'MMM d, yyyy')}</TableCell>
                        <TableCell className="capitalize">{payment.payment_method.replace('_', ' ')}</TableCell>
                        <TableCell>{payment.reference || '-'}</TableCell>
                        <TableCell className="text-right">{formatCurrency(payment.amount)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <Card>
                  <CardContent className="py-8">
                    <p className="text-center text-muted-foreground">No payments recorded yet</p>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </TabsContent>

          <TabsContent value="history" className="p-0">
            <CardContent className="pt-6">
              <h3 className="text-lg font-medium mb-4">Approval History</h3>

              {bill.bill_approval_history && bill.bill_approval_history.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>From Status</TableHead>
                      <TableHead>To Status</TableHead>
                      <TableHead>Changed By</TableHead>
                      <TableHead>Notes</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {bill.bill_approval_history.map((history: any) => (
                      <TableRow key={history.id}>
                        <TableCell>{format(new Date(history.created_at), 'MMM d, yyyy')}</TableCell>
                        <TableCell className="capitalize">{history.from_status}</TableCell>
                        <TableCell className="capitalize">{history.to_status}</TableCell>
                        <TableCell>{history.user?.email || history.changed_by}</TableCell>
                        <TableCell>{history.notes || '-'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <Card>
                  <CardContent className="py-8">
                    <p className="text-center text-muted-foreground">No approval history available</p>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
