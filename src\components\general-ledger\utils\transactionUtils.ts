
/**
 * Utility functions for transactions
 */

import { format } from 'date-fns';
import { TransactionWithAccounts, TransactionInsert, TransactionItemInsert } from '@/types/index';
import { TransactionFormValues } from '../types';

// Helper function to format UGX currency
export const formatUGX = (amount: number): string => {
  return new Intl.NumberFormat('en-UG', {
    style: 'currency',
    currency: 'UGX',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

/**
 * Calculate the total amount of a transaction
 *
 * @param transaction The transaction to calculate the total for
 * @returns The total amount of the transaction
 */
export const calculateTransactionTotal = (transaction: TransactionWithAccounts): number => {
  return transaction.items.reduce((sum, item) => sum + (item.debit || 0), 0);
};

/**
 * Get the debit account from a transaction
 *
 * @param transaction The transaction to get the debit account from
 * @returns The debit account ID or undefined if not found
 */
export const getDebitAccountId = (transaction: TransactionWithAccounts): string | undefined => {
  const debitItem = transaction.items.find(item => item.debit && item.debit > 0);
  return debitItem?.account_id;
};

/**
 * Get the credit account from a transaction
 *
 * @param transaction The transaction to get the credit account from
 * @returns The credit account ID or undefined if not found
 */
export const getCreditAccountId = (transaction: TransactionWithAccounts): string | undefined => {
  const creditItem = transaction.items.find(item => item.credit && item.credit > 0);
  return creditItem?.account_id;
};

/**
 * Format a transaction for display in a form
 *
 * @param transaction The transaction to format
 * @returns Form values for the transaction
 */
export const transactionToFormValues = (transaction: TransactionWithAccounts): TransactionFormValues => {
  const debitItem = transaction.items.find(item => item.debit && item.debit > 0);
  const creditItem = transaction.items.find(item => item.credit && item.credit > 0);

  return {
    date: transaction.date ? new Date(transaction.date) : new Date(),
    description: transaction.description || '',
    debitAccount: debitItem?.account_id || '',
    creditAccount: creditItem?.account_id || '',
    amount: debitItem?.debit || creditItem?.credit || 0,
    reference: transaction.reference || '',
    status: transaction.status as 'pending' | 'approved' | 'rejected' | undefined,
    currency: transaction.currency || 'UGX',
  };
};
