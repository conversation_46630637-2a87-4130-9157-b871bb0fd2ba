import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  FileText,
  DollarSign,
  BarChart3,
  Settings,
  Calendar,
  FileCheck,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarFooter,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarSeparator
} from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';

interface EnhancedSidebarProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

/**
 * Enhanced sidebar component with Supabase dashboard-like structure
 */
const EnhancedSidebar = ({ open }: EnhancedSidebarProps): React.JSX.Element => {
  const location = useLocation();
  const currentPath = location.pathname;

  // Navigation items grouped by category
  const navigationGroups = [
    {
      label: 'Dashboard',
      items: [
        { name: 'Dashboard', href: '/dashboard', icon: Home },
      ]
    },
    {
      label: 'Accounting',
      items: [
        { name: 'General Ledger', href: '/general-ledger', icon: FileText },
        { name: 'Accounts Payable', href: '/accounts-payable', icon: DollarSign },
        { name: 'Accounts Receivable', href: '/accounts-receivable', icon: Calendar },
        { name: 'Budgets', href: '/budgets', icon: FileCheck },
      ]
    },
    {
      label: 'Reports & Analysis',
      items: [
        { name: 'Reports', href: '/reports', icon: BarChart3 },
      ]
    },
    {
      label: 'Administration',
      items: [
        { name: 'Settings', href: '/settings', icon: Settings },
      ]
    }
  ];

  const isActive = (href: string): boolean => {
    return currentPath === href;
  };

  return (
    <Sidebar>
      <SidebarHeader className="border-b border-sidebar-border">
        <div className="flex items-center h-16 px-4">
          <div className="text-xl font-semibold text-sidebar-foreground">
            Kaya <span className="text-secondary">Finance</span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-2 py-2">
        {navigationGroups.map((group, index) => (
          <SidebarGroup key={group.label}>
            <SidebarGroupLabel className="px-2 py-1.5 text-xs font-medium text-sidebar-foreground/70">
              {group.label}
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {group.items.map((item) => (
                  <SidebarMenuItem key={item.name}>
                    <Link to={item.href} className="w-full">
                      <SidebarMenuButton
                        isActive={isActive(item.href)}
                        tooltip={item.name}
                        className="w-full justify-start"
                      >
                        <item.icon className="h-4 w-4 mr-3" />
                        <span>{item.name}</span>
                        {isActive(item.href) && (
                          <ChevronRight className="ml-auto h-4 w-4 opacity-70" />
                        )}
                      </SidebarMenuButton>
                    </Link>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
            {index < navigationGroups.length - 1 && <SidebarSeparator className="my-2" />}
          </SidebarGroup>
        ))}
      </SidebarContent>
    </Sidebar>
  );
};

export default EnhancedSidebar;
