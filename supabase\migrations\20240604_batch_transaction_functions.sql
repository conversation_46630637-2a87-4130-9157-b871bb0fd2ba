-- Create function to get transaction batches with statistics
CREATE OR REPLACE FUNCTION get_transaction_batches_with_stats(p_company_id UUID)
RETURNS TABLE (
  id UUID,
  company_id UUID,
  name TEXT,
  description TEXT,
  transaction_date DATE,
  status TEXT,
  created_by <PERSON>UI<PERSON>,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  deleted_at TIMESTAMPTZ,
  transaction_count BIGINT,
  total_amount NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    tb.id,
    tb.company_id,
    tb.name,
    tb.description,
    tb.transaction_date,
    tb.status,
    tb.created_by,
    tb.created_at,
    tb.updated_at,
    tb.deleted_at,
    COUNT(t.id)::BIGINT AS transaction_count,
    COALESCE(SUM(
      (SELECT SUM(ti.debit) 
       FROM transaction_items ti 
       WHERE ti.transaction_id = t.id)
    ), 0) AS total_amount
  FROM 
    transaction_batches tb
  LEFT JOIN 
    transactions t ON tb.id = t.batch_id AND t.deleted_at IS NULL
  WHERE 
    tb.company_id = p_company_id
    AND tb.deleted_at IS NULL
  GROUP BY 
    tb.id
  ORDER BY 
    tb.transaction_date DESC, tb.created_at DESC;
END;
$$ LANGUAGE plpgsql;
