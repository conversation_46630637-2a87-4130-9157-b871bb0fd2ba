import React, { useState, useEffect } from 'react';
import { TypedDashboardWidget, CashFlowSettings } from '@/types/dashboard';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { format, eachMonthOfInterval, eachQuarterOfInterval, eachYearOfInterval } from 'date-fns';
import { BarChart, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';

// Helper function to get period labels based on date range and period type
const getPeriodLabels = (startDate: Date, endDate: Date, period: string): string[] => {
  if (period === 'monthly') {
    return eachMonthOfInterval({ start: startDate, end: endDate })
      .map(date => format(date, 'MMM'));
  } else if (period === 'quarterly') {
    return eachQuarterOfInterval({ start: startDate, end: endDate })
      .map(date => {
        const quarter = Math.floor(date.getMonth() / 3) + 1;
        return `Q${quarter}`;
      });
  } else if (period === 'yearly') {
    return eachYearOfInterval({ start: startDate, end: endDate })
      .map(date => format(date, 'yyyy'));
  }
  return [];
};

interface CashFlowWidgetProps {
  widget: TypedDashboardWidget;
}

interface CashFlowData {
  name: string;
  inflow: number;
  outflow: number;
  net: number;
}

export function CashFlowWidget({ widget }: CashFlowWidgetProps): JSX.Element {
  const settings = widget.widget_settings as CashFlowSettings;
  const { currentCompanyId } = useAuth();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<CashFlowData[]>([]);
  const [error, setError] = useState<string | null>(null);

  const showInflow = settings.showInflow !== false;
  const showOutflow = settings.showOutflow !== false;
  const showNet = settings.showNet !== false;
  const chartType = settings.chartType || 'bar';
  const period = settings.period || 'monthly';
  const useFiscalYear = settings.useFiscalYear !== false;

  useEffect(() => {
    const fetchCashFlowData = async () => {
      if (!currentCompanyId) return;

      try {
        setLoading(true);
        setError(null);

        // Get the date range based on the selected period
        const now = new Date();
        let startDate = new Date();
        let periodFormat = '';

        if (period === 'weekly') {
          startDate.setDate(now.getDate() - 28); // 4 weeks
          periodFormat = 'week';
        } else if (period === 'monthly') {
          startDate.setMonth(now.getMonth() - 6); // 6 months
          periodFormat = 'month';
        } else if (period === 'quarterly') {
          startDate.setMonth(now.getMonth() - 12); // 4 quarters
          periodFormat = 'quarter';
        } else if (period === 'yearly') {
          startDate.setFullYear(now.getFullYear() - 3); // 3 years
          periodFormat = 'year';
        }

        // If using fiscal year, adjust the date range
        if (useFiscalYear) {
          // Fetch company fiscal year settings
          const { data: company, error: companyError } = await supabase
            .from('companies')
            .select('fiscal_year_start')
            .eq('id', currentCompanyId)
            .single();

          if (companyError) {
            console.error('Error fetching fiscal year settings:', companyError);
          } else if (company?.fiscal_year_start) {
            const fiscalYearStart = new Date(company.fiscal_year_start);
            const currentYear = now.getFullYear();

            // Create a date for this year's fiscal start
            const thisYearFiscalStart = new Date(
              currentYear,
              fiscalYearStart.getMonth(),
              fiscalYearStart.getDate()
            );

            // If current date is before this year's fiscal start, use last year's fiscal start
            if (now < thisYearFiscalStart) {
              startDate = new Date(
                currentYear - 1,
                fiscalYearStart.getMonth(),
                fiscalYearStart.getDate()
              );
            } else {
              startDate = thisYearFiscalStart;
            }
          }
        }

        // Format dates for the query
        const startDateStr = startDate.toISOString().split('T')[0];
        const endDateStr = now.toISOString().split('T')[0];

        // Fetch cash flow data
        // First try to use the RPC if it exists
        try {
          const { data: cashFlowData, error: cashFlowError } = await supabase
            .rpc('get_cash_flow_data', {
              p_company_id: currentCompanyId,
              p_start_date: startDateStr,
              p_end_date: endDateStr,
              p_period: periodFormat
            });

          if (cashFlowError) {
            throw cashFlowError;
          }

          if (cashFlowData && cashFlowData.length > 0) {
            setData(cashFlowData);
            return;
          }
        } catch (rpcError) {
          console.log('RPC not available, falling back to direct query');
        }

        // If RPC fails or returns no data, use direct query
        // Get transactions for the period
        const { data: transactions, error: transactionsError } = await supabase
          .from('transactions')
          .select(`
            id,
            transaction_date,
            transaction_items (
              id,
              debit,
              credit,
              account_id,
              accounts (
                id,
                name,
                account_type_id,
                account_types (
                  id,
                  name,
                  normal_balance
                )
              )
            )
          `)
          .eq('company_id', currentCompanyId)
          .gte('transaction_date', startDateStr)
          .lte('transaction_date', endDateStr)
          .order('transaction_date', { ascending: true });

        if (transactionsError) {
          throw transactionsError;
        }

        if (!transactions || transactions.length === 0) {
          setData([]);
          return;
        }

        // Group transactions by period (month, quarter, year)
        const periodMap = new Map<string, { inflow: number, outflow: number, net: number }>();

        // Initialize periods
        const periodLabels = getPeriodLabels(startDate, endDate, period);
        periodLabels.forEach(label => {
          periodMap.set(label, { inflow: 0, outflow: 0, net: 0 });
        });

        // Process transactions
        transactions.forEach(transaction => {
          const date = new Date(transaction.transaction_date);
          let periodLabel = '';

          if (period === 'monthly') {
            periodLabel = format(date, 'MMM');
          } else if (period === 'quarterly') {
            const quarter = Math.floor(date.getMonth() / 3) + 1;
            periodLabel = `Q${quarter}`;
          } else if (period === 'yearly') {
            periodLabel = format(date, 'yyyy');
          }

          if (!periodMap.has(periodLabel)) {
            return; // Skip if period not in our range
          }

          const periodData = periodMap.get(periodLabel)!;

          // Calculate inflow and outflow from transaction items
          if (transaction.transaction_items && transaction.transaction_items.length > 0) {
            transaction.transaction_items.forEach(item => {
              if (!item.accounts || !item.accounts.account_types) return;

              const accountType = item.accounts.account_types.name.toLowerCase();
              const normalBalance = item.accounts.account_types.normal_balance.toLowerCase();

              // Determine if this is an inflow or outflow
              if (accountType.includes('revenue') || accountType.includes('income')) {
                // Revenue/Income is an inflow
                periodData.inflow += (item.credit || 0);
              } else if (accountType.includes('expense')) {
                // Expense is an outflow
                periodData.outflow += (item.debit || 0);
              }
            });
          }
        });

        // Calculate net for each period and convert to array
        const result = Array.from(periodMap.entries()).map(([name, data]) => {
          const net = data.inflow - data.outflow;
          return {
            name,
            inflow: data.inflow,
            outflow: data.outflow,
            net
          };
        });

        setData(result);
      } catch (err: any) {
        console.error('Error fetching cash flow data:', err);
        setError(err.message || 'Failed to load cash flow data');
      } finally {
        setLoading(false);
      }
    };

    fetchCashFlowData();
  }, [currentCompanyId, period, useFiscalYear]);

  // Format currency for display
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background p-2 border rounded shadow-sm">
          <p className="font-medium">{label}</p>
          {showInflow && payload[0] && (
            <p className="text-sm text-blue-600">
              Inflow: {formatCurrency(payload[0].value)}
            </p>
          )}
          {showOutflow && payload[1] && (
            <p className="text-sm text-red-600">
              Outflow: {formatCurrency(payload[1].value)}
            </p>
          )}
          {showNet && payload[2] && (
            <p className="text-sm text-green-600">
              Net: {formatCurrency(payload[2].value)}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Skeleton className="h-[80%] w-[80%] rounded-md" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center text-center p-4">
        <p className="text-sm text-muted-foreground">
          Error loading cash flow data. Please try again later.
        </p>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="h-full flex items-center justify-center text-center p-4">
        <p className="text-sm text-muted-foreground">
          No cash flow data available for the selected period.
        </p>
      </div>
    );
  }

  return (
    <div className="h-full">
      <ResponsiveContainer width="100%" height="100%">
        {chartType === 'bar' ? (
          <BarChart
            data={data}
            margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
          >
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip content={<CustomTooltip />} />
            {showInflow && <Bar dataKey="inflow" name="Inflow" fill="#3b82f6" />}
            {showOutflow && <Bar dataKey="outflow" name="Outflow" fill="#ef4444" />}
            {showNet && <Bar dataKey="net" name="Net" fill="#10b981" />}
            <Legend />
          </BarChart>
        ) : (
          <LineChart
            data={data}
            margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
          >
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip content={<CustomTooltip />} />
            {showInflow && <Line type="monotone" dataKey="inflow" name="Inflow" stroke="#3b82f6" activeDot={{ r: 8 }} />}
            {showOutflow && <Line type="monotone" dataKey="outflow" name="Outflow" stroke="#ef4444" activeDot={{ r: 8 }} />}
            {showNet && <Line type="monotone" dataKey="net" name="Net" stroke="#10b981" activeDot={{ r: 8 }} />}
            <Legend />
          </LineChart>
        )}
      </ResponsiveContainer>
    </div>
  );
}
