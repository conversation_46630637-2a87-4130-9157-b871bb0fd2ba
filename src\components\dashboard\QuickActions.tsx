
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CircleDollarSign, Calendar, ChartBar, FileText } from "lucide-react";
import { CreateInvoiceDialog } from "@/components/accounts-receivable/CreateInvoiceDialog";
import { useNavigate } from "react-router-dom";

interface Action {
  id: string;
  label: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  href: string;
  color?: string;
  description?: string;
}

const actions: Action[] = [
  {
    id: "record-payment",
    label: "Record Payment",
    icon: CircleDollarSign,
    href: "/accounts-receivable?action=payment",
    color: "text-green-500",
    description: "Record incoming payments",
  },
  {
    id: "create-invoice",
    label: "Create Invoice",
    icon: FileText,
    href: "#",
    color: "text-blue-500",
    description: "Generate a new invoice",
  },
  {
    id: "schedule-payment",
    label: "Schedule Payment",
    icon: Calendar,
    href: "/accounts-payable?action=schedule",
    color: "text-amber-500",
    description: "Schedule outgoing payments",
  },
  {
    id: "view-reports",
    label: "View Reports",
    icon: ChartBar,
    href: "/reports",
    color: "text-purple-500",
    description: "Access financial reports",
  },
];

export const QuickActions = (): JSX.Element => {
  const navigate = useNavigate();

  const handleActionClick = (href: string, id: string): void => {
    if (id === "create-invoice") {
      // Do nothing, the dialog will handle this
      return;
    }
    navigate(href);
  };

  return (
    <Card className="bg-gradient-to-r from-slate-50 to-slate-100 border-t-4 border-t-primary shadow-md">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-semibold">Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
          {actions.map((action) => {
            if (action.id === "create-invoice") {
              return (
                <Card key={action.id} className="overflow-hidden border-l-4 hover:shadow-md transition-shadow duration-200" style={{ borderLeftColor: action.color ? action.color.replace('text-', 'var(--') + ')' : undefined }}>
                  <CardContent className="p-4">
                    <div className="flex flex-col h-full">
                      <div className={`rounded-full w-10 h-10 flex items-center justify-center mb-3 ${action.color || ''} bg-opacity-10 bg-current`}>
                        <action.icon className="h-5 w-5" />
                      </div>
                      <h3 className="font-medium mb-1">{action.label}</h3>
                      {action.description && (
                        <p className="text-xs text-muted-foreground mb-3">{action.description}</p>
                      )}
                      <div className="mt-auto">
                        <CreateInvoiceDialog />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            }

            return (
              <Card
                key={action.id}
                className="overflow-hidden border-l-4 hover:shadow-md transition-shadow duration-200 cursor-pointer"
                style={{ borderLeftColor: action.color ? action.color.replace('text-', 'var(--') + ')' : undefined }}
                onClick={() => handleActionClick(action.href, action.id)}
              >
                <CardContent className="p-4">
                  <div className="flex flex-col h-full">
                    <div className={`rounded-full w-10 h-10 flex items-center justify-center mb-3 ${action.color || ''} bg-opacity-10 bg-current`}>
                      <action.icon className="h-5 w-5" />
                    </div>
                    <h3 className="font-medium mb-1">{action.label}</h3>
                    {action.description && (
                      <p className="text-xs text-muted-foreground mb-3">{action.description}</p>
                    )}
                    <div className="mt-auto">
                      <Button variant="ghost" size="sm" className="p-0 h-auto text-xs font-normal justify-start hover:bg-transparent hover:underline">
                        Get Started →
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
