/**
 * Credit Note Form Component
 * 
 * This component provides a form for creating credit notes against existing invoices.
 */

import { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { CalendarIcon, Plus, Trash2 } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';
import { InvoiceWithRelations } from '@/hooks/use-enhanced-invoices';
import { CreditNoteFormValues, createCreditNote } from '@/services/credit-note-service';

// Credit note form schema
const creditNoteSchema = z.object({
  invoice_id: z.string(),
  reason: z.string().min(1, 'Reason is required'),
  notes: z.string().optional(),
  issue_date: z.date(),
  items: z.array(
    z.object({
      description: z.string().min(1, 'Description is required'),
      quantity: z.number().positive('Quantity must be positive'),
      unit_price: z.number().nonnegative('Price must be non-negative'),
      tax_rate: z.number().nonnegative('Tax rate must be non-negative'),
      invoice_item_id: z.string().optional(),
      selected: z.boolean().optional(),
    })
  ).min(1, 'At least one item is required'),
});

interface CreditNoteFormProps {
  invoice: InvoiceWithRelations;
  onSuccess: () => void;
  onCancel: () => void;
}

export function CreditNoteForm({
  invoice,
  onSuccess,
  onCancel
}: CreditNoteFormProps): JSX.Element {
  const { currentCompanyId, user } = useAuth();
  const [subtotal, setSubtotal] = useState(0);
  const [taxAmount, setTaxAmount] = useState(0);
  const [total, setTotal] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with invoice items
  const form = useForm<z.infer<typeof creditNoteSchema>>({
    resolver: zodResolver(creditNoteSchema),
    defaultValues: {
      invoice_id: invoice.id,
      reason: '',
      notes: '',
      issue_date: new Date(),
      items: invoice.invoice_items.map(item => ({
        description: item.description,
        quantity: 0, // Default to 0 quantity
        unit_price: item.unit_price,
        tax_rate: item.tax_rate,
        invoice_item_id: item.id,
        selected: false,
      })),
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  // Update totals whenever items change
  useEffect(() => {
    const items = form.getValues('items');
    
    const calculatedSubtotal = items.reduce(
      (sum, item) => sum + (item.quantity * item.unit_price),
      0
    );
    
    const calculatedTaxAmount = items.reduce(
      (sum, item) => sum + (item.quantity * item.unit_price * (item.tax_rate / 100)),
      0
    );
    
    const calculatedTotal = calculatedSubtotal + calculatedTaxAmount;

    setSubtotal(calculatedSubtotal);
    setTaxAmount(calculatedTaxAmount);
    setTotal(calculatedTotal);
  }, [form, form.watch('items')]);

  // Handle form submission
  const onSubmit = async (data: z.infer<typeof creditNoteSchema>) => {
    if (!currentCompanyId || !user) {
      console.error('User or company ID not available');
      return;
    }

    setIsSubmitting(true);

    try {
      // Filter out items with quantity = 0
      const filteredItems = data.items.filter(item => item.quantity > 0);
      
      if (filteredItems.length === 0) {
        form.setError('items', {
          type: 'manual',
          message: 'At least one item must have a quantity greater than 0',
        });
        setIsSubmitting(false);
        return;
      }

      const creditNoteData: CreditNoteFormValues = {
        invoice_id: data.invoice_id,
        reason: data.reason,
        notes: data.notes,
        issue_date: data.issue_date,
        items: filteredItems.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          tax_rate: item.tax_rate,
          invoice_item_id: item.invoice_item_id,
        })),
      };

      const result = await createCreditNote(
        creditNoteData,
        currentCompanyId,
        invoice.customers.id,
        user.id
      );

      if (result) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error creating credit note:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle selecting all items
  const handleSelectAll = (select: boolean) => {
    const items = form.getValues('items');
    
    items.forEach((item, index) => {
      // Set selected state
      form.setValue(`items.${index}.selected`, select);
      
      // If selecting, set quantity to max from invoice
      if (select) {
        const invoiceItem = invoice.invoice_items.find(i => i.id === item.invoice_item_id);
        if (invoiceItem) {
          form.setValue(`items.${index}.quantity`, invoiceItem.quantity);
        }
      } else {
        // If deselecting, set quantity to 0
        form.setValue(`items.${index}.quantity`, 0);
      }
    });
    
    // Trigger form validation
    form.trigger('items');
  };

  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: invoice.currency || 'UGX',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="issue_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Issue Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date < new Date("1900-01-01")
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="reason"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Reason for Credit Note</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., Returned goods, pricing error" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Additional Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Any additional details about this credit note"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div>
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium">Credit Note Items</h3>
            <div className="space-x-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => handleSelectAll(true)}
              >
                Select All
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => handleSelectAll(false)}
              >
                Clear All
              </Button>
            </div>
          </div>

          <div className="border rounded-md p-4">
            <div className="grid grid-cols-12 gap-2 mb-2 font-medium text-sm">
              <div className="col-span-1">Select</div>
              <div className="col-span-5">Description</div>
              <div className="col-span-2">Quantity</div>
              <div className="col-span-2">Unit Price</div>
              <div className="col-span-1">Tax Rate</div>
              <div className="col-span-1">Total</div>
            </div>

            {fields.map((field, index) => (
              <div key={field.id} className="grid grid-cols-12 gap-2 items-center py-2 border-t">
                <div className="col-span-1">
                  <Checkbox
                    checked={form.watch(`items.${index}.selected`)}
                    onCheckedChange={(checked) => {
                      form.setValue(`items.${index}.selected`, !!checked);
                      
                      // If checked, set quantity to max from invoice
                      if (checked) {
                        const invoiceItem = invoice.invoice_items.find(i => i.id === field.invoice_item_id);
                        if (invoiceItem) {
                          form.setValue(`items.${index}.quantity`, invoiceItem.quantity);
                          form.trigger(`items.${index}.quantity`);
                        }
                      } else {
                        // If unchecked, set quantity to 0
                        form.setValue(`items.${index}.quantity`, 0);
                        form.trigger(`items.${index}.quantity`);
                      }
                    }}
                  />
                </div>
                <div className="col-span-5">
                  <Input
                    {...form.register(`items.${index}.description`)}
                    readOnly
                  />
                </div>
                <div className="col-span-2">
                  <Input
                    type="number"
                    min="0"
                    step="1"
                    {...form.register(`items.${index}.quantity`, {
                      valueAsNumber: true,
                    })}
                  />
                </div>
                <div className="col-span-2">
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    {...form.register(`items.${index}.unit_price`, {
                      valueAsNumber: true,
                    })}
                    readOnly
                  />
                </div>
                <div className="col-span-1">
                  <Input
                    type="number"
                    min="0"
                    step="0.1"
                    {...form.register(`items.${index}.tax_rate`, {
                      valueAsNumber: true,
                    })}
                    readOnly
                  />
                </div>
                <div className="col-span-1 text-right">
                  {formatCurrency(
                    form.watch(`items.${index}.quantity`) *
                    form.watch(`items.${index}.unit_price`)
                  )}
                </div>
              </div>
            ))}

            <div className="mt-4 text-right space-y-1">
              <div className="text-sm">
                Subtotal: <span className="font-medium">{formatCurrency(subtotal)}</span>
              </div>
              <div className="text-sm">
                Tax: <span className="font-medium">{formatCurrency(taxAmount)}</span>
              </div>
              <div className="text-base font-bold">
                Total: <span>{formatCurrency(total)}</span>
              </div>
            </div>
          </div>
        </div>

        <FormMessage>
          {form.formState.errors.items?.message}
        </FormMessage>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Creating...' : 'Create Credit Note'}
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}
