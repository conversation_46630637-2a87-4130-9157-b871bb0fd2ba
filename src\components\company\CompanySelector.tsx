
import { useState, useEffect } from 'react';
import { CheckIcon, ChevronDown, Building } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Company } from '@/types/auth';

export default function CompanySelector() {
  const { companies, currentCompanyId, setCurrentCompanyId, refreshProfile } = useAuth();
  const [currentCompany, setCurrentCompany] = useState<string>('Select Company');
  const { toast } = useToast();

  useEffect(() => {
    updateCurrentCompanyDisplay();
  }, [companies, currentCompanyId]);

  // Update display based on current company selection
  const updateCurrentCompanyDisplay = () => {
    if (currentCompanyId && companies.length > 0) {
      const company = companies.find(c => c.id === currentCompanyId);
      if (company) {
        setCurrentCompany(company.name);
      }
    } else if (companies.length > 0) {
      setCurrentCompany(companies[0].name);
      setCurrentCompanyId(companies[0].id);
    }
  };

  // Handle company selection change
  const handleCompanyChange = async (company: Company) => {
    try {
      setCurrentCompanyId(company.id);
      setCurrentCompany(company.name);
      
      // Refresh profile data after changing company
      await refreshProfile();
      
      toast({
        title: "Company changed",
        description: `You are now working in ${company.name}`,
      });
    } catch (error) {
      console.error("Error changing company:", error);
      toast({
        title: "Error",
        description: "Failed to change company. Please try again.",
        variant: "destructive"
      });
    }
  };

  if (companies.length === 0) {
    return null;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2">
          <Building className="h-4 w-4" />
          <span className="max-w-[150px] truncate">{currentCompany}</span>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[250px]">
        <DropdownMenuLabel>Your Companies</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {companies.map((company) => (
          <DropdownMenuItem 
            key={company.id}
            className="flex items-center justify-between cursor-pointer" 
            onClick={() => handleCompanyChange(company)}
          >
            <div className="flex items-center">
              <Building className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="truncate">{company.name}</span>
            </div>
            {company.id === currentCompanyId && (
              <CheckIcon className="h-4 w-4 text-primary" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
