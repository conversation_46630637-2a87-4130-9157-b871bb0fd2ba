import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Layers } from 'lucide-react';
import { BatchTransactionForm } from './BatchTransactionForm';
import { AccountWithBalance } from '@/types/index';
import { z } from 'zod';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';

// Define the schema for a single transaction in the batch
const batchItemSchema = z.object({
  description: z.string(),
  debitAccount: z.string(),
  creditAccount: z.string(),
  amount: z.number(),
  reference: z.string().optional(),
});

// Define the schema for the entire batch
const batchTransactionSchema = z.object({
  date: z.date(),
  batchName: z.string(),
  batchDescription: z.string().optional(),
  transactions: z.array(batchItemSchema),
});

type BatchTransactionFormValues = z.infer<typeof batchTransactionSchema>;

interface BatchTransactionDialogProps {
  accounts: AccountWithBalance[];
  onSuccess?: () => void;
  trigger?: React.ReactNode;
}

export const BatchTransactionDialog: React.FC<BatchTransactionDialogProps> = ({
  accounts,
  onSuccess,
  trigger,
}) => {
  const [open, setOpen] = React.useState(false);
  const { toast } = useToast();
  const { user, currentCompanyId } = useAuth();

  const handleSubmit = async (data: BatchTransactionFormValues) => {
    if (!currentCompanyId || !user) {
      toast({
        title: 'Error',
        description: 'User or company information is missing',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Create a batch record
      const { data: batchData, error: batchError } = await supabase
        .from('transaction_batches')
        .insert({
          company_id: currentCompanyId,
          created_by: user.id,
          name: data.batchName,
          description: data.batchDescription || null,
          transaction_date: data.date.toISOString().split('T')[0],
          status: 'pending',
        })
        .select()
        .single();

      if (batchError) throw batchError;

      // Create transactions for the batch
      for (const transaction of data.transactions) {
        // Create transaction record
        const { data: transactionData, error: transactionError } = await supabase
          .from('transactions')
          .insert({
            company_id: currentCompanyId,
            created_by: user.id,
            transaction_date: data.date.toISOString().split('T')[0],
            description: transaction.description,
            reference: transaction.reference || null,
            batch_id: batchData.id,
            status: 'pending',
          })
          .select()
          .single();

        if (transactionError) throw transactionError;

        // Create transaction items (debit and credit entries)
        const transactionItems = [
          {
            transaction_id: transactionData.id,
            account_id: transaction.debitAccount,
            description: transaction.description,
            debit: transaction.amount,
            credit: 0,
          },
          {
            transaction_id: transactionData.id,
            account_id: transaction.creditAccount,
            description: transaction.description,
            debit: 0,
            credit: transaction.amount,
          },
        ];

        const { error: itemsError } = await supabase
          .from('transaction_items')
          .insert(transactionItems);

        if (itemsError) throw itemsError;
      }

      toast({
        title: 'Success',
        description: `Batch "${data.batchName}" with ${data.transactions.length} transactions created successfully`,
      });

      setOpen(false);
      if (onSuccess) onSuccess();
    } catch (error) {
      console.error('Error creating batch transaction:', error);
      toast({
        title: 'Error',
        description: 'Failed to create batch transaction',
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {trigger ? (
        <DialogTrigger asChild>
          {trigger}
        </DialogTrigger>
      ) : (
        <DialogTrigger asChild>
          <Button>
            <Layers className="mr-2 h-4 w-4" />
            Create Batch Transaction
          </Button>
        </DialogTrigger>
      )}
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Batch Transaction</DialogTitle>
          <DialogDescription>
            Create multiple transactions at once as a batch. All transactions in the batch will share the same date.
          </DialogDescription>
        </DialogHeader>
        <BatchTransactionForm
          accounts={accounts}
          onSubmit={handleSubmit}
          onCancel={() => setOpen(false)}
        />
      </DialogContent>
    </Dialog>
  );
};
