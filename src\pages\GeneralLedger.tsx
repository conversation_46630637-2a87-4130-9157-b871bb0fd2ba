
import React, { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Tabs } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { useGeneralLedger } from '@/hooks/useGeneralLedger';
import { Transaction } from '@/models/accountTypes';

// Imported components
import ImportTransactionsDialog from '@/components/general-ledger/ImportTransactionsDialog';
import AddTransactionDialog from '@/components/general-ledger/AddTransactionDialog';
import SearchBar from '@/components/general-ledger/SearchBar';
import LedgerTabs from '@/components/general-ledger/LedgerTabs';
import LedgerContent from '@/components/general-ledger/LedgerContent';

const GeneralLedger = (): React.JSX.Element => {
  const [activeTab, setActiveTab] = useState<'accounts' | 'transactions' | 'recurring' | 'batch' | 'approval'>('accounts');
  const [searchTerm, setSearchTerm] = useState('');
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [addTransactionOpen, setAddTransactionOpen] = useState(false);
  const [editTransaction, setEditTransaction] = useState<Transaction | undefined>(undefined);

  const {
    transactions,
    accounts,
    isLoading,
    handleImportComplete,
    addTransaction,
    updateTransaction,
    deleteTransaction
  } = useGeneralLedger();

  const handleAddTransaction = (): void => {
    setEditTransaction(undefined);
    setAddTransactionOpen(true);
  };

  const handleEditTransaction = (transaction: Transaction): void => {
    setEditTransaction(transaction);
    setAddTransactionOpen(true);
  };

  const handleTransactionComplete = (transaction: Transaction): void => {
    if (editTransaction) {
      updateTransaction(transaction);
    } else {
      addTransaction(transaction);
    }
  };

  const handleImportData = (importedTransactions: any[]): void => {
    const newTransactions = handleImportComplete(importedTransactions);
    if (newTransactions.length > 0) {
      setActiveTab('transactions');
    }
  };

  return (
    <div>
      {/* Page header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="page-title mb-1">General Ledger</h1>
          <p className="text-muted-foreground">
            Manage accounts and transactions
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button onClick={() => setImportDialogOpen(true)} variant="outline">
            Import Transactions
          </Button>
          <Button onClick={handleAddTransaction}>
            Add Transaction
          </Button>
        </div>
      </div>

      {/* Card for tabs and content */}
      <Card className="w-full">
        <CardHeader className="pb-3">
          <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'accounts' | 'transactions')}>
              <LedgerTabs
                activeTab={activeTab}
                onTabChange={setActiveTab}
              />
            </Tabs>

            <SearchBar
              placeholder={`Search ${activeTab === 'accounts' ? 'accounts' : 'transactions'}...`}
              value={searchTerm}
              onChange={setSearchTerm}
            />
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'accounts' | 'transactions')}>
            <LedgerContent
              activeTab={activeTab}
              accounts={accounts}
              transactions={transactions}
              searchTerm={searchTerm}
              isLoading={isLoading}
              onEditTransaction={handleEditTransaction}
              onDeleteTransaction={deleteTransaction}
            />
          </Tabs>
        </CardContent>
      </Card>

      <ImportTransactionsDialog
        open={importDialogOpen}
        onOpenChange={setImportDialogOpen}
        onImportComplete={handleImportData}
      />

      <AddTransactionDialog
        open={addTransactionOpen}
        onOpenChange={setAddTransactionOpen}
        onTransactionComplete={handleTransactionComplete}
        editTransaction={editTransaction}
        accounts={accounts}
      />
    </div>
  );
};

export default GeneralLedger;
