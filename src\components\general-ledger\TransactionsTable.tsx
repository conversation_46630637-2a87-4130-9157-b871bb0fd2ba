
import React, { useState } from 'react';
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from '@/components/ui/table';
import {
  ArrowUp,
  ArrowDown,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Eye,
  AlertCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TransactionWithAccounts, ApprovalStatus } from '@/types/index';
import { useTransactionApprovals } from '@/hooks/useTransactionApprovals';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

// Utility function to format currency in UGX
const formatUGX = (amount: number): string => {
  return new Intl.NumberFormat('en-UG', {
    style: 'currency',
    currency: 'UGX',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

interface TransactionsTableProps {
  transactions: TransactionWithAccounts[];
  searchTerm: string;
  onEditTransaction: (transaction: TransactionWithAccounts) => void;
  onDeleteTransaction: (transactionId: string) => void;
}

const TransactionsTable: React.FC<TransactionsTableProps> = ({
  transactions,
  searchTerm,
  onEditTransaction,
  onDeleteTransaction
}) => {
  const [selectedTransaction, setSelectedTransaction] = useState<TransactionWithAccounts | null>(null);
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [rejectionDialogOpen, setRejectionDialogOpen] = useState(false);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [approvalNotes, setApprovalNotes] = useState('');
  const [rejectionNotes, setRejectionNotes] = useState('');

  const { approveTransaction, rejectTransaction } = useTransactionApprovals();

  // Filter transactions based on search term
  const filteredTransactions = transactions.filter(transaction =>
    transaction.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.date.includes(searchTerm) ||
    (transaction.reference && transaction.reference.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Helper function to get the total debit amount of a transaction
  const getTransactionAmount = (transaction: TransactionWithAccounts): number => {
    return transaction.items.reduce((sum, item) => sum + (item.debit || 0), 0);
  };

  const handleApprove = async () => {
    if (selectedTransaction) {
      const success = await approveTransaction(selectedTransaction.id, approvalNotes);
      if (success) {
        setApprovalDialogOpen(false);
        setApprovalNotes('');
        setSelectedTransaction(null);
      }
    }
  };

  const handleReject = async () => {
    if (selectedTransaction) {
      const success = await rejectTransaction(selectedTransaction.id, rejectionNotes);
      if (success) {
        setRejectionDialogOpen(false);
        setRejectionNotes('');
        setSelectedTransaction(null);
      }
    }
  };

  return (
    <div className="overflow-x-auto w-full">
      <Table className="w-full">
        <TableHeader>
          <TableRow>
            <TableHead className="w-[100px]">ID</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Accounts</TableHead>
            <TableHead>Ref ID</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Approval</TableHead>
            <TableHead className="text-right">Amount</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredTransactions.map((transaction) => {
            // Find debit and credit items
            const debitItems = transaction.items.filter(item => item.debit && item.debit > 0);
            const creditItems = transaction.items.filter(item => item.credit && item.credit > 0);

            return (
              <TableRow key={transaction.id}>
                <TableCell className="font-medium">{transaction.id.substring(0, 8)}</TableCell>
                <TableCell>{transaction.date}</TableCell>
                <TableCell>{transaction.description}</TableCell>
                <TableCell>
                  <div className="flex flex-col gap-1">
                    {debitItems.map(item => (
                      <div key={`debit-${item.id}`} className="flex items-center">
                        <ArrowUp className="mr-2 h-4 w-4 text-red-500" />
                        {item.account?.name || 'Unknown Account'}
                      </div>
                    ))}
                    {creditItems.map(item => (
                      <div key={`credit-${item.id}`} className="flex items-center">
                        <ArrowDown className="mr-2 h-4 w-4 text-green-500" />
                        {item.account?.name || 'Unknown Account'}
                      </div>
                    ))}
                  </div>
                </TableCell>
                <TableCell>{transaction.reference || '-'}</TableCell>
                <TableCell>
                  <Badge variant={
                    transaction.status === 'approved'
                      ? 'success'
                      : transaction.status === 'pending'
                        ? 'warning'
                        : transaction.status === 'rejected'
                          ? 'destructive'
                          : 'secondary'
                  }>
                    {transaction.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  {transaction.status === 'pending' && (
                    <div className="flex items-center">
                      <span className="mr-2">{transaction.approval_level || 0}/{transaction.approval_threshold || 1}</span>
                      <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-primary"
                          style={{
                            width: `${((transaction.approval_level || 0) / (transaction.approval_threshold || 1)) * 100}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  )}
                  {transaction.status === 'approved' && (
                    <span className="text-green-600 flex items-center">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Approved
                    </span>
                  )}
                  {transaction.status === 'rejected' && (
                    <span className="text-red-600 flex items-center">
                      <XCircle className="h-4 w-4 mr-1" />
                      Rejected
                    </span>
                  )}
                </TableCell>
                <TableCell className="text-right font-medium">
                  {formatUGX(getTransactionAmount(transaction))}
                </TableCell>
                <TableCell className="text-right space-x-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      setSelectedTransaction(transaction);
                      setDetailsDialogOpen(true);
                    }}
                  >
                    <Eye className="h-4 w-4" />
                    <span className="sr-only">View</span>
                  </Button>

                  {transaction.status === 'pending' && (
                    <>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setSelectedTransaction(transaction);
                          setApprovalDialogOpen(true);
                        }}
                      >
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="sr-only">Approve</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setSelectedTransaction(transaction);
                          setRejectionDialogOpen(true);
                        }}
                      >
                        <XCircle className="h-4 w-4 text-red-600" />
                        <span className="sr-only">Reject</span>
                      </Button>
                    </>
                  )}

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onEditTransaction(transaction)}
                    disabled={transaction.status === 'approved'} // Can't edit approved transactions
                  >
                    <Edit className="h-4 w-4" />
                    <span className="sr-only">Edit</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onDeleteTransaction(transaction.id)}
                    disabled={transaction.status === 'approved'} // Can't delete approved transactions
                  >
                    <Trash2 className="h-4 w-4" />
                    <span className="sr-only">Delete</span>
                  </Button>
                </TableCell>
              </TableRow>
            );
          })}
          {filteredTransactions.length === 0 && (
            <TableRow>
              <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                No transactions found matching your search criteria
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* Approval Dialog */}
      <Dialog open={approvalDialogOpen} onOpenChange={setApprovalDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Transaction</DialogTitle>
            <DialogDescription>
              Review the transaction details before approving.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Date</Label>
                <div className="font-medium">
                  {selectedTransaction?.date}
                </div>
              </div>
              <div>
                <Label>Amount</Label>
                <div className="font-medium">
                  {selectedTransaction && formatUGX(getTransactionAmount(selectedTransaction))}
                </div>
              </div>
            </div>
            <div>
              <Label>Description</Label>
              <div className="font-medium">
                {selectedTransaction?.description}
              </div>
            </div>
            <div>
              <Label htmlFor="approval-notes">Approval Notes (Optional)</Label>
              <Textarea
                id="approval-notes"
                placeholder="Add any notes about this approval"
                value={approvalNotes}
                onChange={(e) => setApprovalNotes(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setApprovalDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleApprove}>
              Approve Transaction
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rejection Dialog */}
      <Dialog open={rejectionDialogOpen} onOpenChange={setRejectionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Transaction</DialogTitle>
            <DialogDescription>
              Are you sure you want to reject this transaction? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Label htmlFor="rejection-notes">Rejection Reason</Label>
            <Textarea
              id="rejection-notes"
              placeholder="Please provide a reason for rejection"
              value={rejectionNotes}
              onChange={(e) => setRejectionNotes(e.target.value)}
              className="mt-2"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setRejectionDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleReject}>
              Reject Transaction
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Transaction Details Dialog */}
      <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Transaction Details</DialogTitle>
            <DialogDescription>
              Review the complete transaction information.
            </DialogDescription>
          </DialogHeader>
          {selectedTransaction && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Date</Label>
                  <div className="font-medium">
                    {selectedTransaction.date}
                  </div>
                </div>
                <div>
                  <Label>Reference</Label>
                  <div className="font-medium">
                    {selectedTransaction.reference || 'N/A'}
                  </div>
                </div>
                <div>
                  <Label>Description</Label>
                  <div className="font-medium">
                    {selectedTransaction.description}
                  </div>
                </div>
                <div>
                  <Label>Status</Label>
                  <div className="font-medium">
                    <Badge variant={
                      selectedTransaction.status === 'approved'
                        ? 'success'
                        : selectedTransaction.status === 'pending'
                          ? 'warning'
                          : selectedTransaction.status === 'rejected'
                            ? 'destructive'
                            : 'secondary'
                    }>
                      {selectedTransaction.status}
                    </Badge>
                  </div>
                </div>
                {selectedTransaction.status === 'pending' && (
                  <div>
                    <Label>Approval Progress</Label>
                    <div className="font-medium flex items-center mt-1">
                      <span className="mr-2">{selectedTransaction.approval_level || 0}/{selectedTransaction.approval_threshold || 1}</span>
                      <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-primary"
                          style={{
                            width: `${((selectedTransaction.approval_level || 0) / (selectedTransaction.approval_threshold || 1)) * 100}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div>
                <Label>Transaction Items</Label>
                <Table className="mt-2">
                  <TableHeader>
                    <TableRow>
                      <TableHead>Account</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead className="text-right">Debit</TableHead>
                      <TableHead className="text-right">Credit</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {selectedTransaction.items?.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.account?.code} - {item.account?.name}</TableCell>
                        <TableCell>{item.description || selectedTransaction.description}</TableCell>
                        <TableCell className="text-right">{item.debit ? formatUGX(item.debit) : ''}</TableCell>
                        <TableCell className="text-right">{item.credit ? formatUGX(item.credit) : ''}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TransactionsTable;
