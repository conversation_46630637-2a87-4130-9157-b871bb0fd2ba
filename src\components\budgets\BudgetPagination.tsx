
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination";
import React from "react";

type BudgetPaginationProps = {
  currentPage: number;
  totalPages: number;
  pageNumbers: (number | string)[];
  indexOfFirstItem: number;
  indexOfLastItem: number;
  totalItems: number;
  onPageChange: (page: number) => void;
};

export const BudgetPagination = ({
  currentPage,
  totalPages,
  pageNumbers,
  indexOfFirstItem,
  indexOfLastItem,
  totalItems,
  onPageChange
}: BudgetPaginationProps): JSX.Element => {

  const handlePreviousClick = (e: React.MouseEvent<HTMLAnchorElement>): void => {
    e.preventDefault();
    onPageChange(currentPage - 1);
  };

  const handleNextClick = (e: React.MouseEvent<HTMLAnchorElement>): void => {
    e.preventDefault();
    onPageChange(currentPage + 1);
  };

  const handlePageLinkClick = (e: React.MouseEvent<HTMLAnchorElement>, page: number): void => {
    e.preventDefault();
    onPageChange(page);
  };

  return (
    <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
      <div className="text-sm text-muted-foreground">
        Showing {Math.min(indexOfFirstItem + 1, totalItems)} to {Math.min(indexOfLastItem, totalItems)} of {totalItems} budget requests
      </div>

      {totalPages > 1 && (
        <Pagination>
          <PaginationContent>
            {currentPage > 1 && (
              <PaginationItem>
                <PaginationPrevious
                  aria-label="Go to previous page"
                  href="#"
                  onClick={handlePreviousClick}
                />
              </PaginationItem>
            )}

            {pageNumbers.map((page, index) => (
              page === "ellipsis1" || page === "ellipsis2" ? (
                <PaginationItem key={`ellipsis-${index}`}>
                  <PaginationEllipsis />
                </PaginationItem>
              ) : (
                <PaginationItem key={index}>
                  <PaginationLink
                    isActive={currentPage === page}
                    href="#"
                    onClick={(e) => handlePageLinkClick(e, page as number)}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              )
            ))}

            {currentPage < totalPages && (
              <PaginationItem>
                <PaginationNext
                  aria-label="Go to next page"
                  href="#"
                  onClick={handleNextClick}
                />
              </PaginationItem>
            )}
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
};
