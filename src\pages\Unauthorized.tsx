
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON> } from 'react-router-dom';

const Unauthorized = (): React.JSX.Element => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background">
      <div className="text-center max-w-md p-8">
        <h1 className="text-4xl font-bold mb-4 text-primary">Unauthorized Access</h1>
        <p className="text-xl text-muted-foreground mb-8">
          You do not have permission to access this page. Please contact your administrator.
        </p>
        <div className="flex flex-col gap-4">
          <Button asChild>
            <Link to="/dashboard">Go to Dashboard</Link>
          </Button>
          <Button variant="outline">
            <Link to="/login">Sign in with a different account</Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Unauthorized;
