
import * as z from 'zod';
import { CompanyInsert, Constants } from '@/types/index';

/**
 * Validation schema for company form
 *
 * This schema aligns with the Supabase database schema for the companies table
 * and provides user-friendly validation messages.
 */
export const companySchema = z.object({
  // Required fields
  name: z.string()
    .min(2, { message: 'Company name must be at least 2 characters' })
    .max(100, { message: 'Company name must be less than 100 characters' }),

  // Optional company details
  legal_name: z.string()
    .max(100, { message: 'Legal name must be less than 100 characters' })
    .optional()
    .nullable(),

  vat_number: z.string()
    .max(50, { message: 'VAT number must be less than 50 characters' })
    .optional()
    .nullable(),

  company_reg_number: z.string()
    .max(50, { message: 'Registration number must be less than 50 characters' })
    .optional()
    .nullable(),

  // Address information
  address: z.string()
    .max(200, { message: 'Address must be less than 200 characters' })
    .optional()
    .nullable(),

  city: z.string()
    .max(100, { message: 'City must be less than 100 characters' })
    .optional()
    .nullable(),

  country: z.string()
    .min(1, { message: 'Country is required' })
    .max(100, { message: 'Country must be less than 100 characters' })
    .default('Uganda'),

  postal_code: z.string()
    .max(20, { message: 'Postal code must be less than 20 characters' })
    .optional()
    .nullable(),

  // Financial settings
  fiscal_year_start: z.date()
    .optional()
    .nullable()
    .default(() => new Date(new Date().getFullYear(), 0, 1)), // January 1st of current year

  base_currency: z.string()
    .min(3, { message: 'Currency code must be 3 characters' })
    .max(3, { message: 'Currency code must be 3 characters' })
    .default('UGX'),

  // Status
  is_active: z.boolean().default(true),
});

/**
 * Type for company form values derived from the Zod schema
 */
export type CompanyFormValues = z.infer<typeof companySchema>;

/**
 * Convert form values to database insert type
 *
 * @param values Form values from the company form
 * @param userId ID of the user creating the company
 * @returns Company insert object ready for database insertion
 */
export function companyFormToInsert(values: CompanyFormValues, userId: string): CompanyInsert {
  return {
    name: values.name,
    legal_name: values.legal_name || null,
    vat_number: values.vat_number || null,
    company_reg_number: values.company_reg_number || null,
    address: values.address || null,
    city: values.city || null,
    country: values.country,
    postal_code: values.postal_code || null,
    fiscal_year_start: values.fiscal_year_start ? values.fiscal_year_start.toISOString() : null,
    base_currency: values.base_currency,
    is_active: values.is_active,
    created_by: userId,
  };
}
