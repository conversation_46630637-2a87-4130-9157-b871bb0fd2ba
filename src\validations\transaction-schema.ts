import * as z from 'zod';

/**
 * Validation schema for financial transactions
 * 
 * This schema enforces validation rules for financial transactions
 * including dates, amounts, accounts, and descriptions.
 */

// Account types for validation
const accountTypes = ['asset', 'liability', 'equity', 'revenue', 'expense'] as const;

// Transaction types for validation
const transactionTypes = [
  'sale', 
  'purchase', 
  'payment', 
  'receipt', 
  'transfer', 
  'adjustment',
  'depreciation',
  'payroll',
  'tax',
  'other'
] as const;

// Transaction status options
const transactionStatuses = [
  'draft',
  'pending',
  'approved',
  'rejected',
  'posted',
  'voided'
] as const;

// Currency codes for validation
const currencyCodes = ['UGX', 'USD', 'EUR', 'GBP', 'KES', 'TZS', 'RWF'] as const;

// Journal entry schema
export const journalEntrySchema = z.object({
  account_id: z.string().uuid({
    message: "Valid account ID is required"
  }),
  
  description: z.string()
    .max(200, { message: "Description must be less than 200 characters" })
    .optional(),
  
  debit: z.number()
    .nonnegative({ message: "Debit amount cannot be negative" })
    .optional(),
  
  credit: z.number()
    .nonnegative({ message: "Credit amount cannot be negative" })
    .optional(),
    
  tax_rate: z.number()
    .min(0, { message: "Tax rate cannot be negative" })
    .max(100, { message: "Tax rate cannot exceed 100%" })
    .optional(),
    
  project_id: z.string().uuid({ message: "Invalid project ID" }).optional(),
  
  department_id: z.string().uuid({ message: "Invalid department ID" }).optional(),
}).refine(
  (data) => {
    // Either debit or credit must be provided, but not both with non-zero values
    if (data.debit && data.credit && data.debit > 0 && data.credit > 0) {
      return false;
    }
    
    // At least one of debit or credit must be provided
    return (data.debit !== undefined && data.debit > 0) || 
           (data.credit !== undefined && data.credit > 0);
  },
  {
    message: "Either debit or credit must be provided, but not both",
    path: ["debit", "credit"],
  }
);

// Main transaction schema
export const transactionSchema = z.object({
  // Basic transaction information
  transaction_date: z.date({
    required_error: "Transaction date is required",
    invalid_type_error: "Invalid date format"
  }),
  
  posting_date: z.date({
    invalid_type_error: "Invalid posting date format"
  }).optional(),
  
  transaction_type: z.enum(transactionTypes, {
    required_error: "Transaction type is required",
    invalid_type_error: "Invalid transaction type"
  }),
  
  description: z.string()
    .min(5, { message: "Description must be at least 5 characters" })
    .max(200, { message: "Description must be less than 200 characters" }),
  
  reference_number: z.string()
    .max(50, { message: "Reference number must be less than 50 characters" })
    .optional(),
  
  // Financial information
  currency: z.enum(currencyCodes, {
    required_error: "Currency is required",
    invalid_type_error: "Invalid currency code"
  }).default('UGX'),
  
  exchange_rate: z.number()
    .positive({ message: "Exchange rate must be positive" })
    .default(1),
  
  // Status information
  status: z.enum(transactionStatuses, {
    required_error: "Status is required",
    invalid_type_error: "Invalid status"
  }).default('draft'),
  
  // Journal entries (double-entry accounting)
  journal_entries: z.array(journalEntrySchema)
    .min(2, { message: "At least two journal entries are required" })
    .refine(
      (entries) => {
        // Calculate total debits and credits
        const totalDebits = entries.reduce((sum, entry) => sum + (entry.debit || 0), 0);
        const totalCredits = entries.reduce((sum, entry) => sum + (entry.credit || 0), 0);
        
        // Debits must equal credits (within a small rounding error)
        return Math.abs(totalDebits - totalCredits) < 0.01;
      },
      {
        message: "Total debits must equal total credits",
        path: ["journal_entries"],
      }
    ),
  
  // Additional information
  notes: z.string()
    .max(1000, { message: "Notes must be less than 1000 characters" })
    .optional(),
  
  attachments: z.array(
    z.object({
      file_name: z.string().max(100),
      file_url: z.string().url({ message: "Invalid file URL" }),
      file_type: z.string(),
      file_size: z.number().positive(),
    })
  ).optional(),
  
  // Metadata
  created_by: z.string().uuid({ message: "Invalid user ID" }).optional(),
  
  approved_by: z.string().uuid({ message: "Invalid user ID" }).optional(),
  
  company_id: z.string().uuid({ message: "Company ID is required" }),
});

/**
 * Type for transaction form values
 */
export type TransactionFormValues = z.infer<typeof transactionSchema>;

/**
 * Type for journal entry form values
 */
export type JournalEntryFormValues = z.infer<typeof journalEntrySchema>;

/**
 * Simplified transaction schema for basic transactions
 * 
 * This schema is used for simple debit/credit transactions
 * without the need for full journal entries.
 */
export const simpleTransactionSchema = z.object({
  date: z.date({
    required_error: "Transaction date is required",
  }),
  
  description: z.string()
    .min(5, { message: "Description must be at least 5 characters" })
    .max(200, { message: "Description must be less than 200 characters" }),
  
  debitAccount: z.string({
    required_error: "Debit account is required",
  }),
  
  creditAccount: z.string({
    required_error: "Credit account is required",
  }),
  
  amount: z.coerce.number()
    .positive({ message: "Amount must be greater than 0" })
    .lt(**********, { message: "Amount must be less than UGX 1B" }),
  
  reference: z.string().optional(),
  
  status: z.enum(transactionStatuses).default('draft'),
}).refine(
  (data) => data.debitAccount !== data.creditAccount,
  {
    message: "Debit and credit accounts cannot be the same",
    path: ["creditAccount"],
  }
);

/**
 * Type for simple transaction form values
 */
export type SimpleTransactionFormValues = z.infer<typeof simpleTransactionSchema>;

/**
 * Helper function to convert simple transaction to full transaction
 * 
 * @param simpleTransaction Simple transaction form values
 * @param companyId Company ID
 * @returns Full transaction form values
 */
export const convertToFullTransaction = (
  simpleTransaction: SimpleTransactionFormValues,
  companyId: string
): TransactionFormValues => {
  return {
    transaction_date: simpleTransaction.date,
    transaction_type: 'other',
    description: simpleTransaction.description,
    reference_number: simpleTransaction.reference,
    currency: 'UGX',
    exchange_rate: 1,
    status: simpleTransaction.status,
    company_id: companyId,
    journal_entries: [
      {
        account_id: simpleTransaction.debitAccount,
        description: simpleTransaction.description,
        debit: simpleTransaction.amount,
        credit: 0,
      },
      {
        account_id: simpleTransaction.creditAccount,
        description: simpleTransaction.description,
        debit: 0,
        credit: simpleTransaction.amount,
      },
    ],
  };
};
