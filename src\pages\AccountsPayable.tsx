import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FileText,
  Search,
  Filter,
  Calendar,
  Plus,
  RefreshCw,
  Clock
} from 'lucide-react';
import { BillStatusBadge } from '@/components/accounts-payable/bill-status-badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useBills } from '@/hooks/use-bills';
import { BillDetail } from '@/components/accounts-payable/bill-detail';

// Format currency in Ugandan Shillings
const formatUGX = (amount: number): string => {
  return new Intl.NumberFormat('en-UG', {
    style: 'currency',
    currency: 'UGX',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

const AccountsPayable = (): React.JSX.Element => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Use the bills hook to fetch and manage bills
  const {
    bills,
    isLoadingBills,
    billsError,
    refetchBills,
    selectedBill,
    isLoadingBill,
    selectedBillError,
    selectBill,
    filterOptions,
    setFilterOptions,
    handleApproveBill,
    handleRejectBill,
    handleRecordPayment,
    checkOverdueBills,
    isApprovingBill,
    isRejectingBill,
    isRecordingPayment,
    isCheckingOverdueBills,
  } = useBills();

  // Check for overdue bills on component mount
  useEffect(() => {
    checkOverdueBills();
  }, []);

  // Update filter options when search term or status filter changes
  useEffect(() => {
    setFilterOptions({
      ...filterOptions,
      searchTerm,
      status: statusFilter === 'all' ? undefined : statusFilter as any,
    });
  }, [searchTerm, statusFilter]);

  // Calculate stats for bills
  const stats = {
    total: bills?.reduce((sum, bill) => sum + bill.total_amount, 0) || 0,
    pending: bills?.filter(bill => bill.status === 'pending').reduce((sum, bill) => sum + bill.total_amount, 0) || 0,
    overdue: bills?.filter(bill => bill.status === 'overdue').reduce((sum, bill) => sum + bill.total_amount, 0) || 0,
    count: bills?.length || 0,
    pendingCount: bills?.filter(bill => bill.status === 'pending').length || 0,
    overdueCount: bills?.filter(bill => bill.status === 'overdue').length || 0,
    upcomingCount: bills?.filter(bill => {
      const dueDate = new Date(bill.due_date);
      const today = new Date();
      const sevenDaysFromNow = new Date();
      sevenDaysFromNow.setDate(today.getDate() + 7);
      return bill.status === 'approved' && dueDate <= sevenDaysFromNow && dueDate >= today;
    }).length || 0,
    upcomingAmount: bills?.filter(bill => {
      const dueDate = new Date(bill.due_date);
      const today = new Date();
      const sevenDaysFromNow = new Date();
      sevenDaysFromNow.setDate(today.getDate() + 7);
      return bill.status === 'approved' && dueDate <= sevenDaysFromNow && dueDate >= today;
    }).reduce((sum, bill) => sum + bill.total_amount, 0) || 0,
  };

  return (
    <div>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="page-title mb-1">Accounts Payable</h1>
          <p className="text-muted-foreground">Manage vendor bills and payments</p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button onClick={() => refetchBills()} variant="outline" disabled={isLoadingBills}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoadingBills ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={() => checkOverdueBills()} variant="outline" disabled={isCheckingOverdueBills}>
            <Clock className="mr-2 h-4 w-4" />
            Check Overdue
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-muted-foreground text-sm">Total Outstanding</div>
            {isLoadingBills ? (
              <Skeleton className="h-8 w-32 mt-1" />
            ) : (
              <div className="text-2xl font-bold mt-1">{formatUGX(stats.total)}</div>
            )}
            <div className="text-xs text-muted-foreground mt-1">{stats.count} bills</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="text-muted-foreground text-sm">Pending Approval</div>
            {isLoadingBills ? (
              <Skeleton className="h-8 w-32 mt-1" />
            ) : (
              <div className="text-2xl font-bold mt-1 text-yellow-600">{formatUGX(stats.pending)}</div>
            )}
            <div className="text-xs text-muted-foreground mt-1">
              {stats.pendingCount} bills
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="text-muted-foreground text-sm">Overdue</div>
            {isLoadingBills ? (
              <Skeleton className="h-8 w-32 mt-1" />
            ) : (
              <div className="text-2xl font-bold mt-1 text-red-600">{formatUGX(stats.overdue)}</div>
            )}
            <div className="text-xs text-muted-foreground mt-1">
              {stats.overdueCount} bills
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="text-muted-foreground text-sm">Upcoming Payments (7 days)</div>
            {isLoadingBills ? (
              <Skeleton className="h-8 w-32 mt-1" />
            ) : (
              <div className="text-2xl font-bold mt-1">{formatUGX(stats.upcomingAmount)}</div>
            )}
            <div className="text-xs text-muted-foreground mt-1">{stats.upcomingCount} bills due</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <CardTitle>Outstanding Bills</CardTitle>
            <div className="flex flex-wrap items-center gap-2">
              <div className="relative w-full md:w-64">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search bills..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <div className="w-full md:w-40">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="overdue">Overdue</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoadingBills ? (
            <div className="space-y-3">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          ) : bills && bills.length > 0 ? (
            <div className="overflow-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[120px]">Bill #</TableHead>
                    <TableHead>Vendor</TableHead>
                    <TableHead className="hidden md:table-cell">Issue Date</TableHead>
                    <TableHead className="hidden md:table-cell">Due Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                    <TableHead className="w-[100px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {bills.map((bill) => (
                      <TableRow
                        key={bill.id}
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => selectBill(bill.id)}
                      >
                        <TableCell className="font-medium">{bill.bill_number}</TableCell>
                        <TableCell>{bill.vendor?.name || 'Unknown Vendor'}</TableCell>
                        <TableCell className="hidden md:table-cell">{format(new Date(bill.issue_date), 'MMM d, yyyy')}</TableCell>
                        <TableCell className="hidden md:table-cell">{format(new Date(bill.due_date), 'MMM d, yyyy')}</TableCell>
                        <TableCell>
                          <BillStatusBadge status={bill.status} />
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          {formatUGX(bill.total_amount)}
                        </TableCell>
                        <TableCell>
                          <Button size="sm" variant="ghost" onClick={(e) => {
                            e.stopPropagation();
                            selectBill(bill.id);
                          }}>View</Button>
                        </TableCell>
                      </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center">
              <div className="text-center">
                <FileText className="mx-auto h-8 w-8 text-muted-foreground/60 mb-3" />
                <p className="text-muted-foreground">No bills match your search</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Bill Detail Dialog */}
      {selectedBill && (
        <BillDetail
          bill={selectedBill}
          onClose={() => selectBill(null)}
          onRecordPayment={handleRecordPayment}
          onApproveBill={handleApproveBill}
          onRejectBill={handleRejectBill}
          isRecordingPayment={isRecordingPayment}
          isProcessingApproval={isApprovingBill || isRejectingBill}
        />
      )}
    </div>
  );
};

export default AccountsPayable;
