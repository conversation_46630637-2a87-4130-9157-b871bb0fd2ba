import React from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  Di<PERSON>Header, 
  <PERSON>alogTitle, 
  DialogFooter 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  TypedDashboardWidget, 
  WidgetSettings,
  FinancialOverviewSettings,
  CashFlowSettings,
  RecentTransactionsSettings,
  AccountBalanceSettings,
  ExpenseBreakdownSettings,
  TopCustomersSettings,
  TaxCalendarSettings,
  BudgetProgressSettings
} from '@/types/dashboard';
import { useForm } from 'react-hook-form';

interface WidgetSettingsDialogProps {
  widget: TypedDashboardWidget;
  onSave: (settings: WidgetSettings) => void;
  onCancel: () => void;
}

export function WidgetSettingsDialog({ 
  widget, 
  onSave, 
  onCancel 
}: WidgetSettingsDialogProps): JSX.Element {
  // Initialize form with current widget settings
  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm({
    defaultValues: {
      title: widget.widget_title,
      ...widget.widget_settings
    }
  });

  const onSubmit = (data: any) => {
    // Extract title from form data
    const { title, ...settings } = data;
    
    // Update widget title if changed
    if (title !== widget.widget_title) {
      // In a real implementation, we would update the title in the database
      // For now, we'll just include it in the settings
      widget.widget_title = title;
    }
    
    // Save settings
    onSave(settings);
  };

  // Render settings form based on widget type
  const renderSettingsForm = () => {
    switch (widget.widget_type) {
      case 'financial_overview':
        return renderFinancialOverviewSettings();
      case 'cash_flow':
        return renderCashFlowSettings();
      case 'recent_transactions':
        return renderRecentTransactionsSettings();
      case 'account_balance':
        return renderAccountBalanceSettings();
      case 'expense_breakdown':
        return renderExpenseBreakdownSettings();
      case 'top_customers':
        return renderTopCustomersSettings();
      case 'tax_calendar':
        return renderTaxCalendarSettings();
      case 'budget_progress':
        return renderBudgetProgressSettings();
      default:
        return <div>No settings available for this widget type</div>;
    }
  };

  // Financial Overview Widget Settings
  const renderFinancialOverviewSettings = () => {
    const settings = widget.widget_settings as FinancialOverviewSettings;
    
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label htmlFor="showRevenue">Show Revenue</Label>
          <Switch 
            id="showRevenue" 
            checked={settings.showRevenue} 
            onCheckedChange={(checked) => setValue('showRevenue', checked)}
            {...register('showRevenue')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showExpenses">Show Expenses</Label>
          <Switch 
            id="showExpenses" 
            checked={settings.showExpenses} 
            onCheckedChange={(checked) => setValue('showExpenses', checked)}
            {...register('showExpenses')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showProfit">Show Profit</Label>
          <Switch 
            id="showProfit" 
            checked={settings.showProfit} 
            onCheckedChange={(checked) => setValue('showProfit', checked)}
            {...register('showProfit')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showTrend">Show Trend</Label>
          <Switch 
            id="showTrend" 
            checked={settings.showTrend} 
            onCheckedChange={(checked) => setValue('showTrend', checked)}
            {...register('showTrend')}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="period">Time Period</Label>
          <Select 
            value={watch('period') || 'monthly'} 
            onValueChange={(value) => setValue('period', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="useFiscalYear">Use Fiscal Year</Label>
          <Switch 
            id="useFiscalYear" 
            checked={settings.useFiscalYear} 
            onCheckedChange={(checked) => setValue('useFiscalYear', checked)}
            {...register('useFiscalYear')}
          />
        </div>
      </div>
    );
  };

  // Cash Flow Widget Settings
  const renderCashFlowSettings = () => {
    const settings = widget.widget_settings as CashFlowSettings;
    
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label htmlFor="showInflow">Show Inflow</Label>
          <Switch 
            id="showInflow" 
            checked={settings.showInflow} 
            onCheckedChange={(checked) => setValue('showInflow', checked)}
            {...register('showInflow')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showOutflow">Show Outflow</Label>
          <Switch 
            id="showOutflow" 
            checked={settings.showOutflow} 
            onCheckedChange={(checked) => setValue('showOutflow', checked)}
            {...register('showOutflow')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showNet">Show Net Flow</Label>
          <Switch 
            id="showNet" 
            checked={settings.showNet} 
            onCheckedChange={(checked) => setValue('showNet', checked)}
            {...register('showNet')}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="chartType">Chart Type</Label>
          <Select 
            value={watch('chartType') || 'bar'} 
            onValueChange={(value) => setValue('chartType', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select chart type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="bar">Bar Chart</SelectItem>
              <SelectItem value="line">Line Chart</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="period">Time Period</Label>
          <Select 
            value={watch('period') || 'monthly'} 
            onValueChange={(value) => setValue('period', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="useFiscalYear">Use Fiscal Year</Label>
          <Switch 
            id="useFiscalYear" 
            checked={settings.useFiscalYear} 
            onCheckedChange={(checked) => setValue('useFiscalYear', checked)}
            {...register('useFiscalYear')}
          />
        </div>
      </div>
    );
  };

  // Recent Transactions Widget Settings
  const renderRecentTransactionsSettings = () => {
    const settings = widget.widget_settings as RecentTransactionsSettings;
    
    return (
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="limit">Number of Transactions</Label>
          <Input 
            id="limit" 
            type="number" 
            min={1} 
            max={20} 
            {...register('limit')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showAmount">Show Amount</Label>
          <Switch 
            id="showAmount" 
            checked={settings.showAmount} 
            onCheckedChange={(checked) => setValue('showAmount', checked)}
            {...register('showAmount')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showDate">Show Date</Label>
          <Switch 
            id="showDate" 
            checked={settings.showDate} 
            onCheckedChange={(checked) => setValue('showDate', checked)}
            {...register('showDate')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showCategory">Show Category</Label>
          <Switch 
            id="showCategory" 
            checked={settings.showCategory} 
            onCheckedChange={(checked) => setValue('showCategory', checked)}
            {...register('showCategory')}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="transactionType">Transaction Type</Label>
          <Select 
            value={watch('transactionType') || 'all'} 
            onValueChange={(value) => setValue('transactionType', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select transaction type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Transactions</SelectItem>
              <SelectItem value="income">Income Only</SelectItem>
              <SelectItem value="expense">Expenses Only</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    );
  };

  // Account Balance Widget Settings
  const renderAccountBalanceSettings = () => {
    const settings = widget.widget_settings as AccountBalanceSettings;
    
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label htmlFor="showChart">Show Chart</Label>
          <Switch 
            id="showChart" 
            checked={settings.showChart} 
            onCheckedChange={(checked) => setValue('showChart', checked)}
            {...register('showChart')}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="period">Time Period</Label>
          <Select 
            value={watch('period') || 'monthly'} 
            onValueChange={(value) => setValue('period', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="useFiscalYear">Use Fiscal Year</Label>
          <Switch 
            id="useFiscalYear" 
            checked={settings.useFiscalYear} 
            onCheckedChange={(checked) => setValue('useFiscalYear', checked)}
            {...register('useFiscalYear')}
          />
        </div>
      </div>
    );
  };

  // Expense Breakdown Widget Settings
  const renderExpenseBreakdownSettings = () => {
    const settings = widget.widget_settings as ExpenseBreakdownSettings;
    
    return (
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="chartType">Chart Type</Label>
          <Select 
            value={watch('chartType') || 'pie'} 
            onValueChange={(value) => setValue('chartType', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select chart type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pie">Pie Chart</SelectItem>
              <SelectItem value="bar">Bar Chart</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showLegend">Show Legend</Label>
          <Switch 
            id="showLegend" 
            checked={settings.showLegend} 
            onCheckedChange={(checked) => setValue('showLegend', checked)}
            {...register('showLegend')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showPercentages">Show Percentages</Label>
          <Switch 
            id="showPercentages" 
            checked={settings.showPercentages} 
            onCheckedChange={(checked) => setValue('showPercentages', checked)}
            {...register('showPercentages')}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="categoryLimit">Category Limit</Label>
          <Input 
            id="categoryLimit" 
            type="number" 
            min={3} 
            max={10} 
            {...register('categoryLimit')}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="period">Time Period</Label>
          <Select 
            value={watch('period') || 'monthly'} 
            onValueChange={(value) => setValue('period', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="useFiscalYear">Use Fiscal Year</Label>
          <Switch 
            id="useFiscalYear" 
            checked={settings.useFiscalYear} 
            onCheckedChange={(checked) => setValue('useFiscalYear', checked)}
            {...register('useFiscalYear')}
          />
        </div>
      </div>
    );
  };

  // Top Customers Widget Settings
  const renderTopCustomersSettings = () => {
    const settings = widget.widget_settings as TopCustomersSettings;
    
    return (
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="customerLimit">Number of Customers</Label>
          <Input 
            id="customerLimit" 
            type="number" 
            min={3} 
            max={10} 
            {...register('customerLimit')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showRevenue">Show Revenue</Label>
          <Switch 
            id="showRevenue" 
            checked={settings.showRevenue} 
            onCheckedChange={(checked) => setValue('showRevenue', checked)}
            {...register('showRevenue')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showInvoiceCount">Show Invoice Count</Label>
          <Switch 
            id="showInvoiceCount" 
            checked={settings.showInvoiceCount} 
            onCheckedChange={(checked) => setValue('showInvoiceCount', checked)}
            {...register('showInvoiceCount')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showLastPurchase">Show Last Purchase</Label>
          <Switch 
            id="showLastPurchase" 
            checked={settings.showLastPurchase} 
            onCheckedChange={(checked) => setValue('showLastPurchase', checked)}
            {...register('showLastPurchase')}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="period">Time Period</Label>
          <Select 
            value={watch('period') || 'monthly'} 
            onValueChange={(value) => setValue('period', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="useFiscalYear">Use Fiscal Year</Label>
          <Switch 
            id="useFiscalYear" 
            checked={settings.useFiscalYear} 
            onCheckedChange={(checked) => setValue('useFiscalYear', checked)}
            {...register('useFiscalYear')}
          />
        </div>
      </div>
    );
  };

  // Tax Calendar Widget Settings
  const renderTaxCalendarSettings = () => {
    const settings = widget.widget_settings as TaxCalendarSettings;
    
    return (
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="daysAhead">Days Ahead</Label>
          <Input 
            id="daysAhead" 
            type="number" 
            min={7} 
            max={90} 
            {...register('daysAhead')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showDeadlines">Show Deadlines</Label>
          <Switch 
            id="showDeadlines" 
            checked={settings.showDeadlines} 
            onCheckedChange={(checked) => setValue('showDeadlines', checked)}
            {...register('showDeadlines')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showAmounts">Show Amounts</Label>
          <Switch 
            id="showAmounts" 
            checked={settings.showAmounts} 
            onCheckedChange={(checked) => setValue('showAmounts', checked)}
            {...register('showAmounts')}
          />
        </div>
      </div>
    );
  };

  // Budget Progress Widget Settings
  const renderBudgetProgressSettings = () => {
    const settings = widget.widget_settings as BudgetProgressSettings;
    
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label htmlFor="showVariance">Show Variance</Label>
          <Switch 
            id="showVariance" 
            checked={settings.showVariance} 
            onCheckedChange={(checked) => setValue('showVariance', checked)}
            {...register('showVariance')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showPercentage">Show Percentage</Label>
          <Switch 
            id="showPercentage" 
            checked={settings.showPercentage} 
            onCheckedChange={(checked) => setValue('showPercentage', checked)}
            {...register('showPercentage')}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="showChart">Show Chart</Label>
          <Switch 
            id="showChart" 
            checked={settings.showChart} 
            onCheckedChange={(checked) => setValue('showChart', checked)}
            {...register('showChart')}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="period">Time Period</Label>
          <Select 
            value={watch('period') || 'monthly'} 
            onValueChange={(value) => setValue('period', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center justify-between">
          <Label htmlFor="useFiscalYear">Use Fiscal Year</Label>
          <Switch 
            id="useFiscalYear" 
            checked={settings.useFiscalYear} 
            onCheckedChange={(checked) => setValue('useFiscalYear', checked)}
            {...register('useFiscalYear')}
          />
        </div>
      </div>
    );
  };

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Widget Settings</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="title">Widget Title</Label>
            <Input 
              id="title" 
              {...register('title', { required: 'Title is required' })}
            />
            {errors.title && (
              <p className="text-sm text-destructive">{errors.title.message}</p>
            )}
          </div>
          
          {renderSettingsForm()}
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit">Save Changes</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
