import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from './use-toast';
import { useAuth } from '@/context/AuthContext';
import {
  RecurringTransaction,
  RecurringTransactionWithItems,
  RecurringTransactionFormValues,
  recurringTransactionFormToInsert,
  RecurringTransactionStatus
} from '@/types/recurring-transactions';
import { format, addDays, addWeeks, addMonths, addYears } from 'date-fns';

/**
 * Hook for managing recurring transactions
 */
export function useRecurringTransactions() {
  const [recurringTransactions, setRecurringTransactions] = useState<RecurringTransactionWithItems[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { user, currentCompanyId } = useAuth();

  // Fetch recurring transactions
  useEffect(() => {
    const fetchRecurringTransactions = async () => {
      if (!currentCompanyId) return;

      try {
        setIsLoading(true);

        // Fetch recurring transactions with their items
        const { data, error } = await supabase
          .from('recurring_transactions')
          .select(`
            *,
            items:recurring_transaction_items (
              *,
              account:accounts (
                id,
                name,
                code
              )
            )
          `)
          .eq('company_id', currentCompanyId)
          .is('deleted_at', null)
          .order('created_at', { ascending: false });

        if (error) throw error;

        setRecurringTransactions(data || []);
      } catch (err) {
        console.error('Error fetching recurring transactions:', err);
        toast({
          title: 'Error',
          description: 'Failed to load recurring transactions',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecurringTransactions();
  }, [currentCompanyId, toast]);

  /**
   * Create a new recurring transaction
   */
  const createRecurringTransaction = async (values: RecurringTransactionFormValues) => {
    if (!currentCompanyId || !user) {
      toast({
        title: 'Error',
        description: 'Company or user information missing',
        variant: 'destructive',
      });
      return null;
    }

    try {
      // Convert form values to database format
      const { transaction, items } = recurringTransactionFormToInsert(
        values,
        currentCompanyId,
        user.id
      );

      // Insert the recurring transaction
      const { data: newTransaction, error: transactionError } = await supabase
        .from('recurring_transactions')
        .insert(transaction)
        .select()
        .single();

      if (transactionError) throw transactionError;

      // Add recurring_transaction_id to each item
      const itemsWithTransactionId = items.map(item => ({
        ...item,
        recurring_transaction_id: newTransaction.id
      }));

      // Insert the recurring transaction items
      const { error: itemsError } = await supabase
        .from('recurring_transaction_items')
        .insert(itemsWithTransactionId);

      if (itemsError) throw itemsError;

      // Fetch the complete recurring transaction with items
      const { data: completeTransaction, error: fetchError } = await supabase
        .from('recurring_transactions')
        .select(`
          *,
          items:recurring_transaction_items (
            *,
            account:accounts (
              id,
              name,
              code
            )
          )
        `)
        .eq('id', newTransaction.id)
        .single();

      if (fetchError) throw fetchError;

      // Update state with the new transaction
      setRecurringTransactions(prev => [completeTransaction, ...prev]);

      toast({
        title: 'Success',
        description: 'Recurring transaction created successfully',
      });

      return completeTransaction;
    } catch (err) {
      console.error('Error creating recurring transaction:', err);
      toast({
        title: 'Error',
        description: 'Failed to create recurring transaction',
        variant: 'destructive',
      });
      return null;
    }
  };

  /**
   * Update an existing recurring transaction
   */
  const updateRecurringTransaction = async (
    id: string,
    values: RecurringTransactionFormValues
  ) => {
    if (!currentCompanyId || !user) {
      toast({
        title: 'Error',
        description: 'Company or user information missing',
        variant: 'destructive',
      });
      return false;
    }

    try {
      // Convert form values to database format
      const { transaction, items } = recurringTransactionFormToInsert(
        values,
        currentCompanyId,
        user.id
      );

      // Update the recurring transaction
      const { error: updateError } = await supabase
        .from('recurring_transactions')
        .update({
          name: transaction.name,
          description: transaction.description,
          frequency: transaction.frequency,
          start_date: transaction.start_date,
          end_date: transaction.end_date,
          next_due_date: transaction.next_due_date,
          day_of_month: transaction.day_of_month,
          day_of_week: transaction.day_of_week,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (updateError) throw updateError;

      // Delete existing items
      const { error: deleteError } = await supabase
        .from('recurring_transaction_items')
        .delete()
        .eq('recurring_transaction_id', id);

      if (deleteError) throw deleteError;

      // Add recurring_transaction_id to each item
      const itemsWithTransactionId = items.map(item => ({
        ...item,
        recurring_transaction_id: id
      }));

      // Insert new items
      const { error: insertError } = await supabase
        .from('recurring_transaction_items')
        .insert(itemsWithTransactionId);

      if (insertError) throw insertError;

      // Fetch the updated transaction
      const { data: updatedTransaction, error: fetchError } = await supabase
        .from('recurring_transactions')
        .select(`
          *,
          items:recurring_transaction_items (
            *,
            account:accounts (
              id,
              name,
              code
            )
          )
        `)
        .eq('id', id)
        .single();

      if (fetchError) throw fetchError;

      // Update state
      setRecurringTransactions(prev =>
        prev.map(tx => (tx.id === id ? updatedTransaction : tx))
      );

      toast({
        title: 'Success',
        description: 'Recurring transaction updated successfully',
      });

      return true;
    } catch (err) {
      console.error('Error updating recurring transaction:', err);
      toast({
        title: 'Error',
        description: 'Failed to update recurring transaction',
        variant: 'destructive',
      });
      return false;
    }
  };

  /**
   * Delete a recurring transaction
   */
  const deleteRecurringTransaction = async (id: string) => {
    try {
      // Soft delete by setting deleted_at
      const { error } = await supabase
        .from('recurring_transactions')
        .update({ 
          deleted_at: new Date().toISOString(),
          status: 'cancelled'
        })
        .eq('id', id);

      if (error) throw error;

      // Update state
      setRecurringTransactions(prev => prev.filter(tx => tx.id !== id));

      toast({
        title: 'Success',
        description: 'Recurring transaction deleted successfully',
      });

      return true;
    } catch (err) {
      console.error('Error deleting recurring transaction:', err);
      toast({
        title: 'Error',
        description: 'Failed to delete recurring transaction',
        variant: 'destructive',
      });
      return false;
    }
  };

  /**
   * Update the status of a recurring transaction
   */
  const updateStatus = async (id: string, status: RecurringTransactionStatus) => {
    try {
      const { error } = await supabase
        .from('recurring_transactions')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) throw error;

      // Update state
      setRecurringTransactions(prev =>
        prev.map(tx => (tx.id === id ? { ...tx, status } : tx))
      );

      toast({
        title: 'Status Updated',
        description: `Recurring transaction status changed to ${status}`,
      });

      return true;
    } catch (err) {
      console.error('Error updating recurring transaction status:', err);
      toast({
        title: 'Error',
        description: 'Failed to update status',
        variant: 'destructive',
      });
      return false;
    }
  };

  /**
   * Generate a transaction immediately from a recurring template
   */
  const generateTransactionNow = async (id: string) => {
    try {
      // Get the recurring transaction
      const recurringTx = recurringTransactions.find(tx => tx.id === id);
      if (!recurringTx) {
        throw new Error('Recurring transaction not found');
      }

      // Create a new transaction
      const { data: newTransaction, error: transactionError } = await supabase
        .from('transactions')
        .insert({
          company_id: currentCompanyId,
          transaction_date: new Date().toISOString().split('T')[0],
          description: recurringTx.description,
          created_by: user?.id,
          transaction_source: 'recurring',
          recurring_transaction_id: id
        })
        .select()
        .single();

      if (transactionError) throw transactionError;

      // Create transaction items
      const transactionItems = recurringTx.items.map(item => ({
        transaction_id: newTransaction.id,
        account_id: item.account_id,
        description: item.description,
        debit: item.debit,
        credit: item.credit
      }));

      const { error: itemsError } = await supabase
        .from('transaction_items')
        .insert(transactionItems);

      if (itemsError) throw itemsError;

      toast({
        title: 'Success',
        description: 'Transaction generated successfully',
      });

      return true;
    } catch (err) {
      console.error('Error generating transaction:', err);
      toast({
        title: 'Error',
        description: 'Failed to generate transaction',
        variant: 'destructive',
      });
      return false;
    }
  };

  return {
    recurringTransactions,
    isLoading,
    createRecurringTransaction,
    updateRecurringTransaction,
    deleteRecurringTransaction,
    updateStatus,
    generateTransactionNow
  };
}
