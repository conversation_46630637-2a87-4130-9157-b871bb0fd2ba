/**
 * Bill Workflow Service
 * 
 * This service provides functions for managing bill workflows, including:
 * - Recording bill payments
 * - Updating bill status
 * - Bill approval workflow
 * - Checking for overdue bills
 */

import { format } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import { showSuccessToast, showErrorToast } from '@/utils/toast-utils';
import { BillStatus } from '@/types/index';
import { PostgrestError } from '@supabase/supabase-js';

// Format currency as UGX
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-UG', {
    style: 'currency',
    currency: 'UGX',
    minimumFractionDigits: 0,
  }).format(amount);
};

/**
 * Update bill status
 *
 * @param billId Bill ID
 * @param status New status
 * @param userId User ID making the change
 * @param userRole User role (for authorization)
 * @param notes Optional notes about the status change
 * @returns Success status
 */
export const updateBillStatus = async (
  billId: string,
  status: BillStatus,
  userId: string,
  userRole: string,
  notes?: string
): Promise<boolean> => {
  try {
    // Fetch current bill status
    const { data: bill, error: fetchError } = await supabase
      .from('bills')
      .select('status, bill_number')
      .eq('id', billId)
      .single();

    if (fetchError) throw fetchError;
    if (!bill) throw new Error('Bill not found');

    const oldStatus = bill.status;

    // Check authorization for status changes
    if (status === 'approved' && userRole !== 'admin' && userRole !== 'accountant') {
      throw new Error('Only admins and accountants can approve bills');
    }

    // Update bill status
    const { error: updateError } = await supabase
      .from('bills')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', billId);

    if (updateError) throw updateError;

    // Record status change in history
    const { error: historyError } = await supabase
      .from('bill_approval_history')
      .insert({
        bill_id: billId,
        from_status: oldStatus,
        to_status: status,
        changed_by: userId,
        notes: notes || null,
      });

    if (historyError) throw historyError;

    showSuccessToast(
      'Status Updated',
      `Bill #${bill.bill_number} has been ${status === 'approved' ? 'approved' : status}.`
    );

    return true;
  } catch (error: unknown) {
    console.error('Error updating bill status:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to update bill status. Please try again.';
    showErrorToast(
      'Failed to Update Status',
      errorMessage
    );
    return false;
  }
};

/**
 * Record payment for a bill
 *
 * @param billId Bill ID
 * @param paymentData Payment data
 * @param userId User ID recording the payment
 * @returns Success status
 */
export const recordBillPayment = async (
  billId: string,
  paymentData: {
    amount: number;
    paymentDate: Date;
    paymentMethod: string;
    reference?: string;
    notes?: string;
  },
  userId: string
): Promise<boolean> => {
  try {
    // Fetch bill
    const { data: bill, error: fetchError } = await supabase
      .from('bills')
      .select('*')
      .eq('id', billId)
      .single();

    if (fetchError) throw fetchError;
    if (!bill) throw new Error('Bill not found');

    // Create payment record
    const { error: paymentError } = await supabase
      .from('bill_payments')
      .insert({
        bill_id: billId,
        amount: paymentData.amount,
        payment_date: format(paymentData.paymentDate, 'yyyy-MM-dd'),
        payment_method: paymentData.paymentMethod,
        reference: paymentData.reference || null,
        notes: paymentData.notes || null,
        recorded_by: userId,
      });

    if (paymentError) throw paymentError;

    // Check if payment completes the bill
    const { data: payments, error: paymentsError } = await supabase
      .from('bill_payments')
      .select('amount')
      .eq('bill_id', billId);

    if (paymentsError) throw paymentsError;

    const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);

    // If total paid equals or exceeds bill amount, mark as paid
    if (totalPaid >= bill.total_amount) {
      await updateBillStatus(billId, 'paid', userId, 'admin');
    }

    showSuccessToast(
      'Payment Recorded',
      `Payment of ${formatCurrency(paymentData.amount)} has been recorded for bill #${bill.bill_number}.`
    );

    return true;
  } catch (error: unknown) {
    console.error('Error recording payment:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to record payment. Please try again.';
    showErrorToast(
      'Failed to Record Payment',
      errorMessage
    );
    return false;
  }
};

/**
 * Approve a bill
 *
 * @param billId Bill ID
 * @param userId User ID approving the bill
 * @param notes Optional approval notes
 * @returns Success status
 */
export const approveBill = async (
  billId: string,
  userId: string,
  notes?: string
): Promise<boolean> => {
  try {
    return updateBillStatus(billId, 'approved', userId, 'admin', notes);
  } catch (error: unknown) {
    console.error('Error approving bill:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to approve bill. Please try again.';
    showErrorToast(
      'Failed to Approve Bill',
      errorMessage
    );
    return false;
  }
};

/**
 * Reject a bill
 *
 * @param billId Bill ID
 * @param userId User ID rejecting the bill
 * @param reason Reason for rejection
 * @returns Success status
 */
export const rejectBill = async (
  billId: string,
  userId: string,
  reason: string
): Promise<boolean> => {
  try {
    return updateBillStatus(billId, 'cancelled', userId, 'admin', reason);
  } catch (error: unknown) {
    console.error('Error rejecting bill:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to reject bill. Please try again.';
    showErrorToast(
      'Failed to Reject Bill',
      errorMessage
    );
    return false;
  }
};

/**
 * Check for overdue bills and update their status
 *
 * @param companyId Company ID
 * @returns Number of bills updated
 */
export const checkOverdueBills = async (
  companyId: string
): Promise<number> => {
  try {
    const today = new Date();

    // Find pending/approved bills that are past due date
    const { data: overdueBills, error } = await supabase
      .from('bills')
      .select('id, bill_number, due_date')
      .eq('company_id', companyId)
      .in('status', ['pending', 'approved'])
      .lt('due_date', format(today, 'yyyy-MM-dd'));

    if (error) throw error;
    if (!overdueBills || overdueBills.length === 0) return 0;

    // Update status to overdue
    const { error: updateError } = await supabase
      .from('bills')
      .update({ 
        status: 'overdue',
        updated_at: new Date().toISOString()
      })
      .in('id', overdueBills.map(bill => bill.id));

    if (updateError) throw updateError;

    // Record status changes in history
    const historyRecords = overdueBills.map(bill => ({
      bill_id: bill.id,
      from_status: 'pending', // Assuming most were pending
      to_status: 'overdue',
      changed_by: 'system', // System-generated change
      notes: 'Automatically marked as overdue',
    }));

    await supabase.from('bill_approval_history').insert(historyRecords);

    return overdueBills.length;
  } catch (error: unknown) {
    console.error('Error checking overdue bills:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to check overdue bills. Please try again.';
    showErrorToast(
      'Failed to Check Overdue Bills',
      errorMessage
    );
    return 0;
  }
};
