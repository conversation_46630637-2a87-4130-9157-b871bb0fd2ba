import { AuthError } from '@supabase/supabase-js';

/**
 * Comprehensive error handling utilities for authentication operations
 */

export interface ErrorContext {
  operation: string;
  userId?: string;
  timestamp: number;
  retryCount?: number;
}

export interface ErrorHandlerOptions {
  showToast?: boolean;
  logError?: boolean;
  rethrow?: boolean;
  context?: ErrorContext;
}

/**
 * Standardized error handler for authentication operations
 * 
 * @param error The error to handle
 * @param defaultMessage Default message if error message is not available
 * @param options Error handling options
 * @param toastFn Toast function for displaying errors
 * @returns Processed error information
 */
export function handleAuthError(
  error: unknown,
  defaultMessage: string,
  options: ErrorHandlerOptions = {},
  toastFn?: (params: { title: string; description: string; variant?: 'default' | 'destructive' }) => void
): {
  message: string;
  code?: string;
  isAuthError: boolean;
  shouldRetry: boolean;
} {
  const {
    showToast = true,
    logError = true,
    rethrow = false,
    context
  } = options;

  let message = defaultMessage;
  let code: string | undefined;
  let isAuthError = false;
  let shouldRetry = false;

  // Process different error types
  if (error instanceof AuthError) {
    isAuthError = true;
    message = error.message;
    code = error.status?.toString();
    
    // Determine if error is retryable
    shouldRetry = isRetryableAuthError(error);
  } else if (error instanceof Error) {
    message = error.message;
    
    // Check for network errors
    if (error.message.includes('fetch') || error.message.includes('network')) {
      shouldRetry = true;
    }
  } else if (typeof error === 'string') {
    message = error;
  }

  // Log error with context
  if (logError) {
    const logData = {
      message,
      code,
      isAuthError,
      shouldRetry,
      context,
      originalError: error
    };
    
    if (shouldRetry) {
      console.warn('Retryable auth error:', logData);
    } else {
      console.error('Auth error:', logData);
    }
  }

  // Show toast notification
  if (showToast && toastFn) {
    toastFn({
      title: getErrorTitle(context?.operation || 'Operation'),
      description: message,
      variant: 'destructive'
    });
  }

  // Rethrow if requested
  if (rethrow) {
    throw error;
  }

  return {
    message,
    code,
    isAuthError,
    shouldRetry
  };
}

/**
 * Determines if an auth error is retryable
 */
function isRetryableAuthError(error: AuthError): boolean {
  const retryableCodes = [
    'network_error',
    'timeout',
    'rate_limit_exceeded',
    'server_error',
    'service_unavailable'
  ];

  const retryableStatuses = [408, 429, 500, 502, 503, 504];

  return (
    retryableCodes.some(code => error.message.toLowerCase().includes(code)) ||
    (error.status && retryableStatuses.includes(error.status))
  );
}

/**
 * Gets appropriate error title based on operation
 */
function getErrorTitle(operation: string): string {
  const titles: Record<string, string> = {
    'sign_in': 'Sign In Error',
    'sign_up': 'Registration Error',
    'sign_out': 'Sign Out Error',
    'update_profile': 'Profile Update Error',
    'refresh_profile': 'Profile Refresh Error',
    'load_user_data': 'Data Loading Error'
  };

  return titles[operation] || 'Authentication Error';
}

/**
 * Creates a consistent error context for operations
 */
export function createErrorContext(
  operation: string,
  userId?: string,
  retryCount = 0
): ErrorContext {
  return {
    operation,
    userId,
    timestamp: Date.now(),
    retryCount
  };
}

/**
 * Wraps async functions with consistent error handling
 */
export function withErrorHandling<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  operation: string,
  toastFn?: (params: { title: string; description: string; variant?: 'default' | 'destructive' }) => void
): T {
  return (async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    try {
      return await fn(...args);
    } catch (error) {
      const context = createErrorContext(operation);
      const errorInfo = handleAuthError(
        error,
        `Failed to ${operation.replace('_', ' ')}`,
        { context, rethrow: true },
        toastFn
      );
      
      // Re-throw with additional context
      const enhancedError = error instanceof Error ? error : new Error(errorInfo.message);
      (enhancedError as any).context = context;
      (enhancedError as any).shouldRetry = errorInfo.shouldRetry;
      
      throw enhancedError;
    }
  }) as T;
}

/**
 * Retry wrapper for operations that may fail
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries = 3,
  delay = 1000,
  backoff = true
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Check if error is retryable
      const errorInfo = handleAuthError(error, 'Operation failed', { 
        showToast: false, 
        logError: false,
        context: createErrorContext('retry', undefined, attempt)
      });
      
      if (!errorInfo.shouldRetry) {
        break;
      }
      
      // Calculate delay with optional backoff
      const currentDelay = backoff ? delay * Math.pow(2, attempt) : delay;
      await new Promise(resolve => setTimeout(resolve, currentDelay));
    }
  }
  
  throw lastError!;
}
