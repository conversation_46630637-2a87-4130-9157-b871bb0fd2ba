import { useState, useRef, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Profile, ProfileUpdate } from '@/types/index';
import { useToast } from '@/hooks/use-toast';

// Error codes
const PROFILE_NOT_FOUND_ERROR_CODE = 'PGRST116';
const JWT_ERROR_CODE = 'JWT_EXPIRED';
const DUPLICATE_KEY_ERROR = '23505'; // PostgreSQL unique violation error code

// Default retry configuration
const DEFAULT_MAX_RETRIES = 3;
const DEFAULT_RETRY_DELAY = 1000; // 1 second

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const CACHE_KEY_PREFIX = 'profile_cache_';

// Types for enhanced functionality
export interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  exponentialBackoff?: boolean;
}

export interface ProfileOperationCallbacks {
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  onStart?: () => void;
  onComplete?: () => void;
}

export interface ProfileCache {
  data: Profile;
  timestamp: number;
  fields?: string[];
}

export interface LoadingStates {
  fetch: boolean;
  create: boolean;
  update: boolean;
  delete: boolean;
}

export interface ProfileError {
  operation: 'fetch' | 'create' | 'update' | 'delete';
  error: Error;
  timestamp: number;
  retryCount: number;
}

export interface OptimisticUpdate {
  id: string;
  originalData: Profile | null;
  optimisticData: Partial<Profile>;
  operation: 'update' | 'delete';
  timestamp: number;
}

export function useProfileManagement(
  retryConfig: RetryConfig = {
    maxRetries: DEFAULT_MAX_RETRIES,
    retryDelay: DEFAULT_RETRY_DELAY,
    exponentialBackoff: true
  }
) {
  const { toast } = useToast();

  // Enhanced state management
  const [isLoading, setIsLoading] = useState(false);
  const [loadingStates, setLoadingStates] = useState<LoadingStates>({
    fetch: false,
    create: false,
    update: false,
    delete: false
  });
  const [lastError, setLastError] = useState<ProfileError | null>(null);
  const [optimisticUpdates, setOptimisticUpdates] = useState<OptimisticUpdate[]>([]);

  // Refs for component lifecycle and operations
  const isMounted = useRef(true);
  const pendingOperations = useRef<Promise<any>[]>([]);
  const profileCreationAttempts = useRef<Record<string, number>>({});
  const profileCache = useRef<Map<string, ProfileCache>>(new Map());
  const retryAttempts = useRef<Record<string, number>>({});

  // Utility functions
  const setOperationLoading = useCallback((operation: keyof LoadingStates, loading: boolean) => {
    if (!isMounted.current) return;
    setLoadingStates(prev => ({ ...prev, [operation]: loading }));
    setIsLoading(Object.values({ ...loadingStates, [operation]: loading }).some(Boolean));
  }, [loadingStates]);

  const recordError = useCallback((operation: 'fetch' | 'create' | 'update' | 'delete', error: Error, retryCount: number = 0) => {
    if (!isMounted.current) return;
    const profileError: ProfileError = {
      operation,
      error,
      timestamp: Date.now(),
      retryCount
    };
    setLastError(profileError);
  }, []);

  const getCacheKey = useCallback((userId: string, fields?: string[]): string => {
    const fieldsKey = fields ? `_${fields.sort().join(',')}` : '';
    return `${CACHE_KEY_PREFIX}${userId}${fieldsKey}`;
  }, []);

  const getCachedProfile = useCallback((userId: string, fields?: string[]): Profile | null => {
    const cacheKey = getCacheKey(userId, fields);
    const cached = profileCache.current.get(cacheKey);

    if (!cached) return null;

    // Check if cache is still valid
    if (Date.now() - cached.timestamp > CACHE_DURATION) {
      profileCache.current.delete(cacheKey);
      return null;
    }

    return cached.data;
  }, [getCacheKey]);

  const setCachedProfile = useCallback((userId: string, profile: Profile, fields?: string[]) => {
    const cacheKey = getCacheKey(userId, fields);
    profileCache.current.set(cacheKey, {
      data: profile,
      timestamp: Date.now(),
      fields
    });
  }, [getCacheKey]);

  const clearCache = useCallback((userId?: string) => {
    if (userId) {
      // Clear specific user's cache entries
      const keysToDelete = Array.from(profileCache.current.keys())
        .filter(key => key.includes(userId));
      keysToDelete.forEach(key => profileCache.current.delete(key));
    } else {
      // Clear all cache
      profileCache.current.clear();
    }
  }, []);

  const calculateRetryDelay = useCallback((attempt: number): number => {
    if (!retryConfig.exponentialBackoff) {
      return retryConfig.retryDelay;
    }
    return retryConfig.retryDelay * Math.pow(2, attempt);
  }, [retryConfig]);

  // Cleanup function
  const cleanup = useCallback(() => {
    isMounted.current = false;
    pendingOperations.current = [];
    profileCreationAttempts.current = {};
    retryAttempts.current = {};
    profileCache.current.clear();
    setOptimisticUpdates([]);
  }, []);

  /**
   * Fetches user profile, creates one if it doesn't exist
   */
  const fetchUserProfile = async (
    userId: string,
    callbacks?: ProfileOperationCallbacks
  ): Promise<Profile | null> => {
    if (!isMounted.current) return null;

    // Check cache first
    const cachedProfile = getCachedProfile(userId);
    if (cachedProfile) {
      callbacks?.onSuccess?.(cachedProfile);
      return cachedProfile;
    }

    setOperationLoading('fetch', true);
    callbacks?.onStart?.();

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      // Handle case where profile doesn't exist
      if (error?.code === PROFILE_NOT_FOUND_ERROR_CODE) {
        const newProfile = await createInitialProfile(userId, callbacks);
        return newProfile;
      }

      if (error) throw error;

      const profile = data as Profile;
      setCachedProfile(userId, profile);
      callbacks?.onSuccess?.(profile);
      return profile;
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Failed to fetch profile');
      recordError('fetch', err);
      handleError(error, 'Failed to fetch profile');
      callbacks?.onError?.(err);
      return null;
    } finally {
      if (isMounted.current) {
        setOperationLoading('fetch', false);
        callbacks?.onComplete?.();
      }
    }
  };

  /**
   * Fetches specific fields of user profile for performance optimization
   */
  const fetchProfileFields = async (
    userId: string,
    fields: (keyof Profile)[],
    callbacks?: ProfileOperationCallbacks
  ): Promise<Partial<Profile> | null> => {
    if (!isMounted.current) return null;
    if (!fields.length) return null;

    // Check cache first
    const cachedProfile = getCachedProfile(userId, fields);
    if (cachedProfile) {
      const partialProfile = fields.reduce((acc, field) => {
        acc[field] = cachedProfile[field];
        return acc;
      }, {} as Partial<Profile>);
      callbacks?.onSuccess?.(partialProfile);
      return partialProfile;
    }

    setOperationLoading('fetch', true);
    callbacks?.onStart?.();

    try {
      const selectFields = fields.join(', ');
      const { data, error } = await supabase
        .from('profiles')
        .select(selectFields)
        .eq('user_id', userId)
        .single();

      if (error?.code === PROFILE_NOT_FOUND_ERROR_CODE) {
        // Create profile if it doesn't exist, then return requested fields
        const newProfile = await createInitialProfile(userId);
        if (newProfile) {
          const partialProfile = fields.reduce((acc, field) => {
            acc[field] = newProfile[field];
            return acc;
          }, {} as Partial<Profile>);
          callbacks?.onSuccess?.(partialProfile);
          return partialProfile;
        }
        return null;
      }

      if (error) throw error;

      const partialProfile = data as Partial<Profile>;
      // Cache the full profile if we have it, otherwise cache partial
      if (fields.length === Object.keys(data).length) {
        setCachedProfile(userId, data as Profile, fields);
      }

      callbacks?.onSuccess?.(partialProfile);
      return partialProfile;
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Failed to fetch profile fields');
      recordError('fetch', err);
      handleError(error, 'Failed to fetch profile fields');
      callbacks?.onError?.(err);
      return null;
    } finally {
      if (isMounted.current) {
        setOperationLoading('fetch', false);
        callbacks?.onComplete?.();
      }
    }
  };

  /**
   * Creates an initial profile for a new user with retry logic
   */
  const createInitialProfile = async (
    userId: string,
    callbacks?: ProfileOperationCallbacks
  ): Promise<Profile | null> => {
    if (!isMounted.current) return null;

    // Check if we've exceeded retry attempts
    const attempts = profileCreationAttempts.current[userId] || 0;
    if (attempts >= retryConfig.maxRetries) {
      const error = new Error('Max retry attempts exceeded');
      console.error(`Max retry attempts (${retryConfig.maxRetries}) exceeded for profile creation`);
      recordError('create', error, attempts);
      handleError(error, 'Failed to create profile after multiple attempts');
      callbacks?.onError?.(error);
      return null;
    }

    setOperationLoading('create', true);
    if (attempts === 0) callbacks?.onStart?.();

    try {
      // Get current session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError) throw sessionError;
      if (!session?.user) throw new Error('No active session found');

      const user = session.user;
      const newProfile = {
        user_id: userId,
        first_name: user.user_metadata?.first_name || '',
        last_name: user.user_metadata?.last_name || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('Creating new profile:', { userId, metadata: user.user_metadata, attempt: attempts + 1 });

      // Use a transaction to ensure atomicity
      const { data: createdProfile, error: createError } = await supabase
        .from('profiles')
        .insert(newProfile)
        .select()
        .single();

      if (createError) {
        // Handle duplicate key error
        if (createError.code === DUPLICATE_KEY_ERROR) {
          console.log('Profile already exists, fetching existing profile');
          return await fetchUserProfile(userId);
        }

        console.error('Profile creation error:', createError);
        throw createError;
      }

      console.log('Profile created successfully:', createdProfile);
      const profile = createdProfile as Profile;

      // Cache the new profile
      setCachedProfile(userId, profile);

      // Reset retry counter on success
      delete profileCreationAttempts.current[userId];
      callbacks?.onSuccess?.(profile);
      return profile;
    } catch (error) {
      console.error('Error in createInitialProfile:', error);
      const err = error instanceof Error ? error : new Error('Failed to create initial profile');

      // Increment retry counter
      profileCreationAttempts.current[userId] = attempts + 1;

      // If we haven't exceeded max retries, wait and try again
      if (attempts < retryConfig.maxRetries - 1) {
        const delay = calculateRetryDelay(attempts);
        console.log(`Retrying profile creation in ${delay}ms (attempt ${attempts + 1}/${retryConfig.maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return createInitialProfile(userId, callbacks);
      }

      recordError('create', err, attempts);
      handleError(error, 'Failed to create initial profile');
      callbacks?.onError?.(err);
      return null;
    } finally {
      if (isMounted.current) {
        setOperationLoading('create', false);
        if (attempts >= retryConfig.maxRetries - 1) {
          callbacks?.onComplete?.();
        }
      }
    }
  };

  /**
   * Updates user profile information with optimistic updates
   */
  const updateProfile = async (
    userId: string,
    updates: Partial<ProfileUpdate>,
    options: {
      optimistic?: boolean;
      callbacks?: ProfileOperationCallbacks
    } = {}
  ): Promise<boolean> => {
    if (!isMounted.current) return false;
    if (!userId) {
      const error = new Error('You must be logged in to update your profile');
      recordError('update', error);
      showToast('Error', error.message, 'destructive');
      options.callbacks?.onError?.(error);
      return false;
    }

    const { optimistic = false, callbacks } = options;
    let optimisticUpdateId: string | null = null;

    setOperationLoading('update', true);
    callbacks?.onStart?.();

    // Apply optimistic update if requested
    if (optimistic) {
      const currentProfile = getCachedProfile(userId);
      if (currentProfile) {
        optimisticUpdateId = `update_${Date.now()}_${Math.random()}`;
        const optimisticUpdate: OptimisticUpdate = {
          id: optimisticUpdateId,
          originalData: currentProfile,
          optimisticData: updates,
          operation: 'update',
          timestamp: Date.now()
        };

        setOptimisticUpdates(prev => [...prev, optimisticUpdate]);

        // Update cache with optimistic data
        const optimisticProfile = { ...currentProfile, ...updates };
        setCachedProfile(userId, optimisticProfile);
      }
    }

    try {
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (updateError) throw updateError;

      // Remove optimistic update on success
      if (optimisticUpdateId) {
        setOptimisticUpdates(prev => prev.filter(update => update.id !== optimisticUpdateId));
      }

      // Update cache with real data
      const currentProfile = getCachedProfile(userId);
      if (currentProfile) {
        const updatedProfile = {
          ...currentProfile,
          ...updates,
          updated_at: new Date().toISOString()
        };
        setCachedProfile(userId, updatedProfile);
      }

      showToast('Success', 'Profile updated successfully');
      callbacks?.onSuccess?.(updates);
      return true;
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Failed to update profile');

      // Rollback optimistic update on error
      if (optimisticUpdateId) {
        setOptimisticUpdates(prev => {
          const update = prev.find(u => u.id === optimisticUpdateId);
          if (update && update.originalData) {
            setCachedProfile(userId, update.originalData);
          }
          return prev.filter(u => u.id !== optimisticUpdateId);
        });
      }

      recordError('update', err);
      handleError(error, 'Failed to update profile');
      callbacks?.onError?.(err);
      return false;
    } finally {
      if (isMounted.current) {
        setOperationLoading('update', false);
        callbacks?.onComplete?.();
      }
    }
  };

  /**
   * Deletes user profile (soft delete using deleted_at field)
   */
  const deleteProfile = async (
    userId: string,
    options: {
      hard?: boolean;
      callbacks?: ProfileOperationCallbacks
    } = {}
  ): Promise<boolean> => {
    if (!isMounted.current) return false;
    if (!userId) {
      const error = new Error('You must be logged in to delete your profile');
      recordError('delete', error);
      showToast('Error', error.message, 'destructive');
      options.callbacks?.onError?.(error);
      return false;
    }

    const { hard = false, callbacks } = options;
    let optimisticUpdateId: string | null = null;

    setOperationLoading('delete', true);
    callbacks?.onStart?.();

    // Apply optimistic update for soft delete
    if (!hard) {
      const currentProfile = getCachedProfile(userId);
      if (currentProfile) {
        optimisticUpdateId = `delete_${Date.now()}_${Math.random()}`;
        const optimisticUpdate: OptimisticUpdate = {
          id: optimisticUpdateId,
          originalData: currentProfile,
          optimisticData: { deleted_at: new Date().toISOString() },
          operation: 'delete',
          timestamp: Date.now()
        };

        setOptimisticUpdates(prev => [...prev, optimisticUpdate]);
        clearCache(userId); // Remove from cache immediately
      }
    }

    try {
      if (hard) {
        // Hard delete - permanently remove the record
        const { error: deleteError } = await supabase
          .from('profiles')
          .delete()
          .eq('user_id', userId);

        if (deleteError) throw deleteError;
      } else {
        // Soft delete - set deleted_at timestamp
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            deleted_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId);

        if (updateError) throw updateError;
      }

      // Remove optimistic update on success
      if (optimisticUpdateId) {
        setOptimisticUpdates(prev => prev.filter(update => update.id !== optimisticUpdateId));
      }

      // Clear cache for this user
      clearCache(userId);

      showToast('Success', 'Profile deleted successfully');
      callbacks?.onSuccess?.(true);
      return true;
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Failed to delete profile');

      // Rollback optimistic update on error
      if (optimisticUpdateId) {
        setOptimisticUpdates(prev => {
          const update = prev.find(u => u.id === optimisticUpdateId);
          if (update && update.originalData) {
            setCachedProfile(userId, update.originalData);
          }
          return prev.filter(u => u.id !== optimisticUpdateId);
        });
      }

      recordError('delete', err);
      handleError(error, 'Failed to delete profile');
      callbacks?.onError?.(err);
      return false;
    } finally {
      if (isMounted.current) {
        setOperationLoading('delete', false);
        callbacks?.onComplete?.();
      }
    }
  };

  /**
   * Retries the last failed operation
   */
  const retryLastOperation = useCallback(async (): Promise<boolean> => {
    if (!lastError) return false;

    // Clear the error state
    setLastError(null);

    // Note: This is a simplified retry - in a real implementation,
    // you'd need to store the operation parameters to retry properly
    showToast('Info', 'Retry functionality would need operation context');
    return false;
  }, [lastError]);

  /**
   * Handles errors consistently
   */
  const handleError = (error: unknown, defaultMessage: string) => {
    if (!isMounted.current) return;

    const errorMessage = error instanceof Error
      ? error.message
      : defaultMessage;

    console.error(errorMessage, error);
    showToast('Error', errorMessage, 'destructive');
  };

  /**
   * Shows toast notification
   */
  const showToast = (
    title: string,
    description: string,
    variant: 'default' | 'destructive' = 'default'
  ) => {
    if (!isMounted.current) return;

    toast({
      title,
      description,
      variant
    });
  };

  return {
    // Core operations
    fetchUserProfile,
    fetchProfileFields,
    updateProfile,
    deleteProfile,

    // State
    isLoading,
    loadingStates,
    lastError,
    optimisticUpdates,

    // Cache management
    clearCache,
    getCachedProfile,

    // Utilities
    cleanup,
    retryLastOperation
  };
}