import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useRef,
  useCallback,
  useMemo
} from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import {
  Profile,
  UserRole,
  Company,
  AuthState
} from '@/types/index';
import { useToast } from '@/hooks/use-toast';
import { useProfileManagement } from '@/hooks/useProfileManagement';
import { useCompanyManagement } from '@/hooks/useCompanyManagement';
import { useRoleManagement } from '@/hooks/useRoleManagement';
import { useIsMounted } from '@/hooks/useMountedState';
import { debounceAsync } from '@/utils/debounce-utils';
import {
  handleAuthError,
  withErrorHandling,
  withRetry,
  createErrorContext,
  type ErrorContext
} from '@/utils/auth-error-handler';

/**
 * Enhanced AuthContext interface with comprehensive type safety
 */
interface AuthContextType extends AuthState {
  session: Session | null;
  needsOnboarding: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, firstName: string, lastName: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<Profile>) => Promise<void>;
  refreshProfile: () => Promise<void>;
  setCurrentCompanyId: (companyId: string | null) => void;
  clearUserData: () => void;
  retryLastOperation: () => Promise<void>;
}

/**
 * Loading state management interface
 */
interface LoadingState {
  profile: boolean;
  roles: boolean;
  companies: boolean;
  auth: boolean;
}

/**
 * Operation queue for managing concurrent async operations
 */
interface OperationQueue {
  current: Promise<void> | null;
  pending: Array<() => Promise<void>>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Enhanced AuthProvider with comprehensive error handling, race condition protection,
 * and performance optimizations
 */
export const AuthProvider = ({ children }: { children: ReactNode }): JSX.Element => {
  // Core state
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [roles, setRoles] = useState<UserRole[]>([]);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [currentCompanyId, setCurrentCompanyId] = useState<string | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [needsOnboarding, setNeedsOnboarding] = useState<boolean>(false);

  // Enhanced loading state management
  const [loadingState, setLoadingState] = useState<LoadingState>({
    profile: false,
    roles: false,
    companies: false,
    auth: true
  });

  // Hooks and utilities
  const { toast } = useToast();
  const isMounted = useIsMounted();

  // Refs for operation management
  const operationQueue = useRef<OperationQueue>({ current: null, pending: [] });
  const initialLoadComplete = useRef(false);
  const lastSessionId = useRef<string | null>(null);
  const lastOperation = useRef<(() => Promise<void>) | null>(null);

  const { fetchUserProfile, updateProfile: updateUserProfile } = useProfileManagement();
  const { fetchUserCompanies } = useCompanyManagement();
  const { fetchUserRoles } = useRoleManagement();

  // Computed loading state
  const loading = useMemo(() =>
    loadingState.profile || loadingState.roles || loadingState.companies || loadingState.auth,
    [loadingState]
  );

  /**
   * Centralized function to clear all user data
   * Reduces redundant state updates and ensures consistency
   */
  const clearUserData = useCallback((): void => {
    if (!isMounted()) return;

    setUser(null);
    setProfile(null);
    setRoles([]);
    setCompanies([]);
    setCurrentCompanyId(null);
    setSession(null);
    setNeedsOnboarding(false);
    setLoadingState({
      profile: false,
      roles: false,
      companies: false,
      auth: false
    });

    // Clear refs
    initialLoadComplete.current = false;
    lastSessionId.current = null;
    lastOperation.current = null;

    // Cancel any pending operations
    if (operationQueue.current.current) {
      operationQueue.current.pending = [];
    }
  }, [isMounted]);

  /**
   * Enhanced loading state management
   */
  const updateLoadingState = useCallback((updates: Partial<LoadingState>): void => {
    if (!isMounted()) return;
    setLoadingState(prev => ({ ...prev, ...updates }));
  }, [isMounted]);

  /**
   * Consistent error handling with toast notifications
   */
  const handleError = useCallback((
    error: unknown,
    operation: string,
    defaultMessage: string,
    shouldRethrow = false
  ): void => {
    if (!isMounted()) return;

    const errorInfo = handleAuthError(
      error,
      defaultMessage,
      {
        context: createErrorContext(operation, user?.id),
        showToast: true,
        logError: true,
        rethrow: shouldRethrow
      },
      toast
    );

    // Store last operation for retry functionality
    if (errorInfo.shouldRetry && lastOperation.current) {
      // Keep the last operation for potential retry
    }
  }, [isMounted, user?.id, toast]);

  /**
   * Queue management for preventing race conditions
   */
  const executeOperation = useCallback(async (operation: () => Promise<void>): Promise<void> => {
    // If there's a current operation, queue this one
    if (operationQueue.current.current) {
      operationQueue.current.pending.push(operation);
      return;
    }

    // Execute the operation
    operationQueue.current.current = operation();

    try {
      await operationQueue.current.current;
    } finally {
      operationQueue.current.current = null;

      // Execute next operation in queue
      const nextOperation = operationQueue.current.pending.shift();
      if (nextOperation) {
        executeOperation(nextOperation);
      }
    }
  }, []);

  /**
   * Enhanced refresh profile function with better error handling and loading states
   */
  const refreshProfile = useCallback(async (): Promise<void> => {
    if (!user || !isMounted()) return;

    const operation = async (): Promise<void> => {
      console.log("Refreshing user profile and data");

      updateLoadingState({
        profile: true,
        roles: true,
        companies: true
      });

      try {
        // Use withRetry for resilient data fetching
        const [newProfile, userRoles, userCompanies] = await withRetry(
          async () => {
            const results = await Promise.all([
              fetchUserProfile(user.id),
              fetchUserRoles(user.id),
              fetchUserCompanies(user.id)
            ]);
            return results;
          },
          3, // max retries
          1000 // delay
        );

        if (!isMounted()) return;

        console.log("All user data refreshed:", {
          hasProfile: !!newProfile,
          roles: userRoles?.length || 0,
          companies: userCompanies?.length || 0
        });

        // Update state with fetched data
        if (newProfile) {
          setProfile(newProfile);
        }
        setRoles(userRoles || []);
        setCompanies(userCompanies || []);

        // Set current company if not already set and companies exist
        if (!currentCompanyId && userCompanies && userCompanies.length > 0) {
          console.log("Setting current company ID during refresh:", userCompanies[0].id);
          setCurrentCompanyId(userCompanies[0].id);
        }
      } catch (error) {
        handleError(error, 'refresh_profile', 'Failed to refresh user data');
      } finally {
        updateLoadingState({
          profile: false,
          roles: false,
          companies: false
        });
      }
    };

    // Store operation for retry functionality
    lastOperation.current = operation;
    await executeOperation(operation);
  }, [
    user,
    isMounted,
    updateLoadingState,
    fetchUserProfile,
    fetchUserRoles,
    fetchUserCompanies,
    currentCompanyId,
    handleError,
    executeOperation
  ]);

  /**
   * Enhanced update profile function with consistent error handling
   */
  const updateProfile = useCallback(async (updates: Partial<Profile>): Promise<void> => {
    if (!user || !isMounted()) {
      handleError(
        new Error("You must be logged in to update your profile"),
        'update_profile',
        'Authentication required'
      );
      return;
    }

    const operation = async (): Promise<void> => {
      updateLoadingState({ profile: true });

      try {
        const success = await updateUserProfile(user.id, updates);
        if (success && isMounted()) {
          await refreshProfile();
        }
      } catch (error) {
        handleError(error, 'update_profile', 'Failed to update profile');
      } finally {
        updateLoadingState({ profile: false });
      }
    };

    lastOperation.current = operation;
    await executeOperation(operation);
  }, [user, isMounted, handleError, updateLoadingState, updateUserProfile, refreshProfile, executeOperation]);

  /**
   * Enhanced sign in with consistent error handling
   */
  const signIn = useCallback(async (email: string, password: string): Promise<void> => {
    updateLoadingState({ auth: true });

    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;
    } catch (error) {
      handleError(error, 'sign_in', 'Failed to sign in', true);
    } finally {
      updateLoadingState({ auth: false });
    }
  }, [updateLoadingState, handleError]);

  /**
   * Enhanced sign up with consistent error handling
   */
  const signUp = useCallback(async (email: string, password: string, firstName: string, lastName: string): Promise<void> => {
    updateLoadingState({ auth: true });

    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName
          }
        }
      });

      if (error) throw error;

      toast({
        title: "Account created",
        description: "Your account has been created successfully"
      });
    } catch (error) {
      handleError(error, 'sign_up', 'Failed to create account', true);
    } finally {
      updateLoadingState({ auth: false });
    }
  }, [updateLoadingState, handleError, toast]);

  /**
   * Enhanced sign out with proper cleanup
   */
  const signOut = useCallback(async (): Promise<void> => {
    const operation = async (): Promise<void> => {
      updateLoadingState({ auth: true });

      try {
        const { error } = await supabase.auth.signOut();
        if (error) throw error;

        // Use centralized cleanup function
        clearUserData();
      } catch (error) {
        handleError(error, 'sign_out', 'Failed to sign out');
      } finally {
        updateLoadingState({ auth: false });
      }
    };

    await executeOperation(operation);
  }, [updateLoadingState, clearUserData, handleError, executeOperation]);

  /**
   * Retry last failed operation
   */
  const retryLastOperation = useCallback(async (): Promise<void> => {
    if (!lastOperation.current) {
      console.warn('No operation to retry');
      return;
    }

    try {
      await executeOperation(lastOperation.current);
    } catch (error) {
      handleError(error, 'retry_operation', 'Retry failed');
    }
  }, [executeOperation, handleError]);

  /**
   * Loads user data after authentication
   */
  const loadUserData = useCallback(async (userId: string): Promise<void> => {
    console.log(`Loading user data for: ${userId}`);

    updateLoadingState({
      profile: true,
      roles: true,
      companies: true
    });

    try {
      const [userProfile, userRoles, userCompanies] = await withRetry(
        async () => {
          const results = await Promise.all([
            fetchUserProfile(userId),
            fetchUserRoles(userId),
            fetchUserCompanies(userId)
          ]);
          return results;
        },
        3, // max retries
        1000 // delay
      );

      if (!isMounted()) return;

      initialLoadComplete.current = true;

      setProfile(userProfile);
      setRoles(userRoles || []);
      setCompanies(userCompanies || []);

      // Set first company as current if available and no company is selected
      if (userCompanies && userCompanies.length > 0 && !currentCompanyId) {
        console.log(`Setting initial company: ${userCompanies[0].id}`);
        setCurrentCompanyId(userCompanies[0].id);
        setNeedsOnboarding(false);
      } else if (!userCompanies || userCompanies.length === 0) {
        console.log('No companies found for user - onboarding needed');
        setNeedsOnboarding(true);
        // Note: Navigation to onboarding should be handled by components that have Router context
      }
    } catch (error) {
      handleError(error, 'load_user_data', 'Failed to load user data');
      // Clear data on error
      if (isMounted()) {
        setProfile(null);
        setRoles([]);
        setCompanies([]);
      }
    } finally {
      updateLoadingState({
        profile: false,
        roles: false,
        companies: false
      });
    }
  }, [
    updateLoadingState,
    fetchUserProfile,
    fetchUserRoles,
    fetchUserCompanies,
    currentCompanyId,
    handleError,
    isMounted
  ]);

  /**
   * Handles authentication state changes
   */
  const handleAuthChange = useCallback(async (currentSession: Session | null, event?: string): Promise<void> => {
    if (!isMounted()) return;

    // Prevent duplicate loads on the same session
    const currentSessionId = currentSession?.user?.id;
    const isInitialLoad = !initialLoadComplete.current;
    const sessionChanged = currentSessionId !== lastSessionId.current;

    console.log(`Auth state handler called. Event: ${event}, Initial load: ${isInitialLoad}, Session changed: ${sessionChanged}`);

    // Skip if it's the same session and not a sign out event
    if (!isInitialLoad && !sessionChanged && event !== 'SIGNED_OUT') {
      console.log("Skipping redundant auth state change");
      return;
    }

    // Update session tracking
    lastSessionId.current = currentSessionId || null;
    setSession(currentSession);
    setUser(currentSession?.user ?? null);

    if (currentSession?.user) {
      // Load user data with operation queue
      const operation = async (): Promise<void> => {
        await loadUserData(currentSession.user.id);
      };

      await executeOperation(operation);
    } else {
      // No session - clear all user data
      clearUserData();
    }
  }, [isMounted, loadUserData, clearUserData, executeOperation]);

  /**
   * Enhanced auth state change handler with debouncing and race condition protection
   */
  useEffect(() => {
    // Create debounced auth change handler
    const debouncedAuthChange = debounceAsync(handleAuthChange, 100);

    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log(`Auth state changed: ${event} ${!!session}`);
        await debouncedAuthChange(session, event);
      }
    );

    // Initialize auth state
    const initializeAuth = async (): Promise<void> => {
      try {
        updateLoadingState({ auth: true });
        const { data: { session: initialSession } } = await supabase.auth.getSession();
        await handleAuthChange(initialSession, 'INITIAL');
      } catch (error) {
        handleError(error, 'auth_initialization', 'Failed to initialize authentication');
      } finally {
        updateLoadingState({ auth: false });
      }
    };

    initializeAuth();

    return () => {
      subscription.unsubscribe();
    };
  }, [handleAuthChange, updateLoadingState, handleError]);

  /**
   * Context value with enhanced functionality
   */
  const value = useMemo((): AuthContextType => ({
    // Core state
    user,
    profile,
    roles,
    companies,
    currentCompanyId,
    session,
    loading,
    needsOnboarding,

    // Enhanced methods
    signIn,
    signUp,
    signOut,
    updateProfile,
    refreshProfile,
    setCurrentCompanyId,
    clearUserData,
    retryLastOperation
  }), [
    user,
    profile,
    roles,
    companies,
    currentCompanyId,
    session,
    loading,
    needsOnboarding,
    signIn,
    signUp,
    signOut,
    updateProfile,
    refreshProfile,
    clearUserData,
    retryLastOperation
  ]);

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Hook to access the authentication context
 *
 * @returns AuthContextType with all authentication state and methods
 * @throws Error if used outside of AuthProvider
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
