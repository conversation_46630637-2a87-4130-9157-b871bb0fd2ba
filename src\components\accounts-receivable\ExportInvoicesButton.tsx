import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { exportToCSV, formatDateForCSV, formatCurrencyForCSV } from '@/utils/csv-export-utils';
import { format } from 'date-fns';
import { useToast } from '@/hooks/use-toast';

interface Invoice {
  invoice_number: string;
  issue_date: string;
  due_date: string;
  total_amount: number;
  status: string;
  paid_amount?: number;
  customers?: {
    name: string;
  };
}

interface ExportInvoicesButtonProps {
  invoices: Invoice[];
  isLoading?: boolean;
}

/**
 * Button component for exporting invoices to CSV
 */
export function ExportInvoicesButton({
  invoices,
  isLoading = false
}: ExportInvoicesButtonProps): JSX.Element {
  const [exporting, setExporting] = useState(false);
  const { toast } = useToast();

  // Define headers for the CSV file
  const headers = {
    invoice_number: 'Invoice Number',
    issue_date: 'Issue Date',
    due_date: 'Due Date',
    customer_name: 'Customer',
    total_amount: 'Amount (UGX)',
    status: 'Status',
    paid_amount: 'Paid Amount (UGX)',
    balance: 'Balance (UGX)'
  };

  const handleExport = async (): Promise<void> => {
    try {
      setExporting(true);

      // Format invoices for CSV export
      const formattedInvoices = invoices.map(invoice => {
        const customerName = invoice.customers?.name || 'Unknown Customer';
        const paidAmount = invoice.paid_amount || 0;
        const balance = invoice.total_amount - paidAmount;

        return {
          invoice_number: invoice.invoice_number,
          issue_date: formatDateForCSV(invoice.issue_date),
          due_date: formatDateForCSV(invoice.due_date),
          customer_name: customerName,
          total_amount: formatCurrencyForCSV(invoice.total_amount),
          status: invoice.status,
          paid_amount: formatCurrencyForCSV(paidAmount),
          balance: formatCurrencyForCSV(balance)
        };
      });

      // Generate filename with current date
      const currentDate = format(new Date(), 'yyyy-MM-dd');
      const filename = `invoices-${currentDate}.csv`;

      // Export to CSV
      exportToCSV(formattedInvoices, filename, headers);

      // Show success message
      toast({
        title: 'Export Successful',
        description: `${invoices.length} invoices exported to ${filename}`,
        variant: 'default'
      });
    } catch (error) {
      console.error('Error exporting invoices:', error);
      toast({
        title: 'Export Failed',
        description: 'There was an error exporting the invoices. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setExporting(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleExport}
      disabled={isLoading || exporting || invoices.length === 0}
      className="gap-2"
    >
      <Download className="h-4 w-4" />
      {exporting ? 'Exporting...' : 'Export CSV'}
    </Button>
  );
}
