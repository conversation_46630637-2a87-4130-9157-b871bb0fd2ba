
import React from 'react';
import { Tabs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { FileTex<PERSON>, <PERSON><PERSON>hart, RefreshCw, Layers, CheckCircle } from 'lucide-react';

interface LedgerTabsProps {
  activeTab: 'accounts' | 'transactions' | 'recurring' | 'batch' | 'approval';
  onTabChange: (tab: 'accounts' | 'transactions' | 'recurring' | 'batch' | 'approval') => void;
}

const LedgerTabs: React.FC<LedgerTabsProps> = ({
  activeTab,
  onTabChange
}): React.JSX.Element => {
  return (
    <TabsList>
      <TabsTrigger
        value="accounts"
        className="flex items-center"
        onClick={() => onTabChange('accounts')}
      >
        <BarChart className="mr-2 h-4 w-4" />
        Chart of Accounts
      </TabsTrigger>
      <TabsTrigger
        value="transactions"
        className="flex items-center"
        onClick={() => onTabChange('transactions')}
      >
        <FileText className="mr-2 h-4 w-4" />
        Transaction Journal
      </TabsTrigger>
      <TabsTrigger
        value="recurring"
        className="flex items-center"
        onClick={() => onTabChange('recurring')}
      >
        <RefreshCw className="mr-2 h-4 w-4" />
        Recurring
      </TabsTrigger>
      <TabsTrigger
        value="batch"
        className="flex items-center"
        onClick={() => onTabChange('batch')}
      >
        <Layers className="mr-2 h-4 w-4" />
        Batch
      </TabsTrigger>
      <TabsTrigger
        value="approval"
        className="flex items-center"
        onClick={() => onTabChange('approval')}
      >
        <CheckCircle className="mr-2 h-4 w-4" />
        Approvals
      </TabsTrigger>
    </TabsList>
  );
};

export default LedgerTabs;
