import { useState, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import {
  approveBill,
  rejectBill,
  recordBillPayment,
  checkOverdueBills
} from '@/services/bill-workflow-service';
import { showSuccessToast, showErrorToast } from '@/utils/toast-utils';
import { useRetryMutation } from './use-retry-mutation';
import { Tables, Enums } from '@/types';

// Type for bill with related data
export interface BillWithRelations extends Tables<'bills'> {
  vendor: Pick<Tables<'vendors'>, 'id' | 'name' | 'email' | 'phone' | 'address' | 'city' | 'country'>;
  bill_items: Array<Tables<'bill_items'>>;
  bill_payments?: Array<Tables<'bill_payments'>>;
  bill_approval_history?: Array<
    Tables<'bill_approval_history'> & {
      user?: {
        email: string;
      };
    }
  >;
};

// Type for bill filter options
export interface BillFilterOptions {
  status?: "pending" | "approved" | "paid" | "overdue" | "cancelled" | 'all';
  dateRange?: 'all' | '30days' | '90days' | 'thisYear' | 'custom';
  startDate?: Date;
  endDate?: Date;
  vendorId?: string;
  minAmount?: number;
  maxAmount?: number;
  searchTerm?: string;
}

/**
 * Hook for managing bills
 * @returns Object with bill data and management functions
 */
export function useBills(): {
  bills: BillWithRelations[] | undefined;
  isLoadingBills: boolean;
  billsError: Error | null;
  refetchBills: () => Promise<any>;
  selectedBill: BillWithRelations | null | undefined;
  isLoadingBill: boolean;
  selectedBillError: Error | null;
  refetchSelectedBill: () => Promise<any>;
  selectBill: (billId: string | null) => void;
  filterOptions: BillFilterOptions;
  setFilterOptions: React.Dispatch<React.SetStateAction<BillFilterOptions>>;
  handleApproveBill: (billId: string, notes: string) => Promise<void>;
  handleRejectBill: (billId: string, notes: string) => Promise<void>;
  handleRecordPayment: (billId: string, paymentData: {
    amount: number;
    paymentDate: Date;
    paymentMethod: string;
    reference?: string;
    notes?: string;
  }) => void;
  checkOverdueBills: () => void;
  isApprovingBill: boolean;
  isRejectingBill: boolean;
  isRecordingPayment: boolean;
  isCheckingOverdueBills: boolean;
} {
  const { user, currentCompanyId } = useAuth();
  const queryClient = useQueryClient();
  const [selectedBillId, setSelectedBillId] = useState<string | null>(null);
  const [filterOptions, setFilterOptions] = useState<BillFilterOptions>({
    status: 'all',
    dateRange: 'all',
  });

  /**
   * Query for fetching bills with filters
   */
  const {
    data: bills,
    isLoading: isLoadingBills,
    error: billsError,
    refetch: refetchBills,
  } = useQuery({
    queryKey: ['bills', currentCompanyId, filterOptions],
    queryFn: async () => {
      if (!currentCompanyId) return [];

      let query = supabase
        .from('bills')
        .select(`
          *,
          vendor:vendors(id, name, email, phone)
        `)
        .eq('company_id', currentCompanyId);

      // Apply status filter
      if (filterOptions.status && filterOptions.status !== 'all') {
        query = query.eq('status', filterOptions.status);
      }

      // Apply date range filter
      if (filterOptions.dateRange && filterOptions.dateRange !== 'all') {
        const today = new Date();
        let startDate: Date;

        if (filterOptions.dateRange === '30days') {
          startDate = new Date(today);
          startDate.setDate(today.getDate() - 30);
        } else if (filterOptions.dateRange === '90days') {
          startDate = new Date(today);
          startDate.setDate(today.getDate() - 90);
        } else if (filterOptions.dateRange === 'thisYear') {
          startDate = new Date(today.getFullYear(), 0, 1);
        } else if (filterOptions.dateRange === 'custom' && filterOptions.startDate && filterOptions.endDate) {
          startDate = filterOptions.startDate;
          query = query.lte('issue_date', filterOptions.endDate.toISOString().split('T')[0]);
        } else {
          startDate = new Date(today);
          startDate.setDate(today.getDate() - 30);
        }

        query = query.gte('issue_date', startDate.toISOString().split('T')[0]);
      }

      // Apply vendor filter
      if (filterOptions.vendorId) {
        query = query.eq('vendor_id', filterOptions.vendorId);
      }

      // Apply amount filters
      if (filterOptions.minAmount) {
        query = query.gte('total_amount', filterOptions.minAmount);
      }
      if (filterOptions.maxAmount) {
        query = query.lte('total_amount', filterOptions.maxAmount);
      }

      // Apply search term filter
      if (filterOptions.searchTerm) {
        query = query.or(`bill_number.ilike.%${filterOptions.searchTerm}%,vendor.name.ilike.%${filterOptions.searchTerm}%`);
      }

      // Order by issue date (newest first)
      query = query.order('issue_date', { ascending: false });

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    },
    enabled: !!currentCompanyId,
  });

  /**
   * Query for fetching a single bill with all related data
   */
  const {
    data: selectedBill,
    isLoading: isLoadingBill,
    error: selectedBillError,
    refetch: refetchSelectedBill,
  } = useQuery({
    queryKey: ['bill', selectedBillId],
    queryFn: async () => {
      if (!selectedBillId) return null;

      const { data, error } = await supabase
        .from('bills')
        .select(`
          *,
          vendor:vendors (
            id, name, email, phone, address, city, country
          ),
          bill_items (
            id, description, quantity, unit_price, tax_rate, amount
          ),
          bill_payments (
            id, amount, payment_date, payment_method, reference, notes
          ),
          bill_approval_history (
            id, from_status, to_status, changed_by, created_at, notes,
            user:profiles!bill_approval_history_changed_by_fkey (
              email
            )
          )
        `)
        .eq('id', selectedBillId)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!selectedBillId,
  });

  /**
   * Mutation for approving a bill
   */
  const approveBillMutation = useRetryMutation(
    async ({ billId, notes }: { billId: string; notes: string }) => {
      if (!user) throw new Error("Authentication required");

      const success = await approveBill(billId, user.id, notes);
      if (!success) throw new Error("Failed to approve bill");
      return success;
    },
    {
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['bills'] });
        if (selectedBillId === variables.billId) {
          queryClient.invalidateQueries({ queryKey: ['bill', variables.billId] });
        }
      },
    }
  );

  /**
   * Mutation for rejecting a bill
   */
  const rejectBillMutation = useRetryMutation(
    async ({ billId, notes }: { billId: string; notes: string }) => {
      if (!user) throw new Error("Authentication required");

      const success = await rejectBill(billId, user.id, notes);
      if (!success) throw new Error("Failed to reject bill");
      return success;
    },
    {
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['bills'] });
        if (selectedBillId === variables.billId) {
          queryClient.invalidateQueries({ queryKey: ['bill', variables.billId] });
        }
      },
    }
  );

  /**
   * Mutation for recording bill payment
   */
  const recordPaymentMutation = useRetryMutation(
    async ({
      billId,
      paymentData
    }: {
      billId: string;
      paymentData: {
        amount: number;
        paymentDate: Date;
        paymentMethod: string;
        reference?: string;
        notes?: string;
      };
    }) => {
      if (!user) throw new Error("Authentication required");

      const success = await recordBillPayment(billId, paymentData, user.id);
      if (!success) throw new Error("Failed to record payment");
      return success;
    },
    {
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['bills'] });
        if (selectedBillId === variables.billId) {
          queryClient.invalidateQueries({ queryKey: ['bill', variables.billId] });
        }
      },
    }
  );

  /**
   * Check for overdue bills
   */
  const checkOverdueBillsMutation = useRetryMutation(
    async () => {
      if (!currentCompanyId) throw new Error("Company ID required");

      const updatedCount = await checkOverdueBills(currentCompanyId);
      return updatedCount;
    },
    {
      onSuccess: (count) => {
        if (count > 0) {
          queryClient.invalidateQueries({ queryKey: ['bills'] });
          showSuccessToast(
            'Overdue Bills Updated',
            `${count} bills have been marked as overdue.`
          );
        }
      },
    }
  );

  /**
   * Handle bill selection
   */
  const selectBill = useCallback((billId: string | null): void => {
    setSelectedBillId(billId);
  }, []);

  /**
   * Handle bill approval
   */
  const handleApproveBill = useCallback(async (billId: string, notes: string): Promise<void> => {
    try {
      await approveBillMutation.mutateAsync({ billId, notes });
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : 'Failed to approve bill. Please try again.';

      showErrorToast('Approval Failed', errorMessage);
    }
  }, [approveBillMutation]);

  /**
   * Handle bill rejection
   */
  const handleRejectBill = useCallback(async (billId: string, notes: string): Promise<void> => {
    try {
      await rejectBillMutation.mutateAsync({ billId, notes });
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : 'Failed to reject bill. Please try again.';

      showErrorToast('Rejection Failed', errorMessage);
    }
  }, [rejectBillMutation]);

  /**
   * Handle recording payment
   */
  const handleRecordPayment = useCallback((
    billId: string,
    paymentData: {
      amount: number;
      paymentDate: Date;
      paymentMethod: string;
      reference?: string;
      notes?: string;
    }
  ): void => {
    recordPaymentMutation.mutate({ billId, paymentData });
  }, [recordPaymentMutation]);

  return {
    bills,
    isLoadingBills,
    billsError,
    refetchBills,
    selectedBill,
    isLoadingBill,
    selectedBillError,
    refetchSelectedBill,
    selectBill,
    filterOptions,
    setFilterOptions,
    handleApproveBill,
    handleRejectBill,
    handleRecordPayment,
    checkOverdueBills: checkOverdueBillsMutation.mutate,
    isApprovingBill: approveBillMutation.isPending,
    isRejectingBill: rejectBillMutation.isPending,
    isRecordingPayment: recordPaymentMutation.isPending,
    isCheckingOverdueBills: checkOverdueBillsMutation.isPending,
  };
}
