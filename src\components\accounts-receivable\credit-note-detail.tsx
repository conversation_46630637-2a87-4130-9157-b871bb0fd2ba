/**
 * Credit Note Detail Component
 * 
 * This component displays the details of a credit note and provides
 * actions for approving, rejecting, or printing the credit note.
 */

import { useState } from 'react';
import { format } from 'date-fns';
import { 
  Printer, 
  CheckCircle, 
  XCircle, 
  ArrowLeft,
  FileText
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/context/AuthContext';
import { CreditNoteWithRelations, updateCreditNoteStatus } from '@/services/credit-note-service';

interface CreditNoteDetailProps {
  creditNote: CreditNoteWithRelations;
  onClose: () => void;
  onStatusChange: () => void;
}

export function CreditNoteDetail({
  creditNote,
  onClose,
  onStatusChange
}: CreditNoteDetailProps): JSX.Element {
  const { user, roles } = useAuth();
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [rejectionDialogOpen, setRejectionDialogOpen] = useState(false);
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const userRole = roles?.[0] || 'viewer';
  const canApprove = userRole === 'admin' || userRole === 'accountant';
  const isPending = creditNote.status === 'issued';
  const isApproved = creditNote.status === 'approved';
  const isRejected = creditNote.status === 'rejected';
  const isDraft = creditNote.status === 'draft';

  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: creditNote.currency || 'UGX',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string): string => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'issued':
        return 'bg-blue-100 text-blue-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle approval
  const handleApprove = async () => {
    if (!user) return;
    
    setIsSubmitting(true);
    
    const success = await updateCreditNoteStatus(
      creditNote.id,
      'approved',
      user.id,
      notes
    );
    
    setIsSubmitting(false);
    setApprovalDialogOpen(false);
    
    if (success) {
      onStatusChange();
    }
  };

  // Handle rejection
  const handleReject = async () => {
    if (!user) return;
    
    setIsSubmitting(true);
    
    const success = await updateCreditNoteStatus(
      creditNote.id,
      'rejected',
      user.id,
      notes
    );
    
    setIsSubmitting(false);
    setRejectionDialogOpen(false);
    
    if (success) {
      onStatusChange();
    }
  };

  // Handle print
  const handlePrint = () => {
    window.print();
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Button variant="ghost" onClick={onClose}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div className="flex space-x-2">
          {isPending && canApprove && (
            <>
              <Button 
                variant="outline" 
                onClick={() => setRejectionDialogOpen(true)}
              >
                <XCircle className="mr-2 h-4 w-4" />
                Reject
              </Button>
              <Button 
                variant="default" 
                onClick={() => setApprovalDialogOpen(true)}
              >
                <CheckCircle className="mr-2 h-4 w-4" />
                Approve
              </Button>
            </>
          )}
          <Button variant="outline" onClick={handlePrint}>
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
        </div>
      </div>

      <div className="print:block">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Credit Note #{creditNote.credit_note_number}</CardTitle>
              <div className="text-sm text-muted-foreground mt-1">
                For Invoice #{creditNote.invoices.invoice_number}
              </div>
            </div>
            <Badge className={getStatusBadgeColor(creditNote.status)}>
              {creditNote.status.charAt(0).toUpperCase() + creditNote.status.slice(1)}
            </Badge>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium mb-2">Customer Information</h3>
                <div className="text-sm">
                  <div className="font-medium">{creditNote.customers.name}</div>
                  <div>{creditNote.customers.email}</div>
                </div>
              </div>
              <div>
                <h3 className="font-medium mb-2">Credit Note Details</h3>
                <div className="text-sm grid grid-cols-2 gap-x-4 gap-y-1">
                  <div>Issue Date:</div>
                  <div>{format(new Date(creditNote.issue_date), 'MMM d, yyyy')}</div>
                  <div>Reason:</div>
                  <div>{creditNote.reason}</div>
                  {creditNote.approved_at && (
                    <>
                      <div>Approved Date:</div>
                      <div>{format(new Date(creditNote.approved_at), 'MMM d, yyyy')}</div>
                    </>
                  )}
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="font-medium mb-2">Credit Note Items</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Qty</TableHead>
                    <TableHead className="text-right">Unit Price</TableHead>
                    <TableHead className="text-right">Tax Rate</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {creditNote.credit_note_items.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.description}</TableCell>
                      <TableCell className="text-right">{item.quantity}</TableCell>
                      <TableCell className="text-right">{formatCurrency(item.unit_price)}</TableCell>
                      <TableCell className="text-right">{item.tax_rate}%</TableCell>
                      <TableCell className="text-right">{formatCurrency(item.line_total)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <div className="mt-4 text-right space-y-1">
                <div className="text-sm">
                  Subtotal: <span className="font-medium">{formatCurrency(creditNote.amount)}</span>
                </div>
                <div className="text-sm">
                  Tax: <span className="font-medium">{formatCurrency(creditNote.tax_amount)}</span>
                </div>
                <div className="text-base font-bold">
                  Total: <span>{formatCurrency(creditNote.total_amount)}</span>
                </div>
              </div>
            </div>

            {creditNote.notes && (
              <div>
                <h3 className="font-medium mb-2">Notes</h3>
                <div className="text-sm bg-gray-50 p-3 rounded-md">
                  {creditNote.notes}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Approval Dialog */}
      <Dialog open={approvalDialogOpen} onOpenChange={setApprovalDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Credit Note</DialogTitle>
            <DialogDescription>
              Are you sure you want to approve this credit note? This will mark the original invoice as credited.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Add approval notes (optional)"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setApprovalDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleApprove} disabled={isSubmitting}>
              {isSubmitting ? 'Approving...' : 'Approve Credit Note'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rejection Dialog */}
      <Dialog open={rejectionDialogOpen} onOpenChange={setRejectionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Credit Note</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this credit note.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Reason for rejection"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              required
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setRejectionDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleReject} 
              disabled={isSubmitting || !notes.trim()}
            >
              {isSubmitting ? 'Rejecting...' : 'Reject Credit Note'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
