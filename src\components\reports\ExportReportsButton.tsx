import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { exportToCSV, formatDateForCSV, formatCurrencyForCSV } from '@/utils/csv-export-utils';
import { format } from 'date-fns';
import { useToast } from '@/hooks/use-toast';
import {
  BalanceSheetData,
  ProfitAndLossData,
  CashFlowData,
  TaxReportData
} from '@/services/report-service';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface ExportReportsButtonProps {
  balanceSheet?: BalanceSheetData | null;
  profitAndLoss?: ProfitAndLossData | null;
  cashFlow?: CashFlowData | null;
  taxReport?: TaxReportData | null;
  startDate: Date;
  endDate: Date;
  isLoading?: boolean;
}

/**
 * Button component for exporting financial reports to CSV
 */
export function ExportReportsButton({
  balanceSheet,
  profitAndLoss,
  cashFlow,
  taxReport,
  startDate,
  endDate,
  isLoading = false
}: ExportReportsButtonProps): React.JSX.Element {
  const [exporting, setExporting] = useState(false);
  const { toast } = useToast();

  const handleExportBalanceSheet = async (): Promise<void> => {
    try {
      setExporting(true);

      if (!balanceSheet) {
        toast({
          title: 'Export Failed',
          description: 'No balance sheet data available to export.',
          variant: 'destructive'
        });
        return;
      }

      // Format balance sheet data for CSV export
      const assets = balanceSheet.assets.items.map(item => ({
        category: 'Assets',
        name: item.name,
        amount: formatCurrencyForCSV(item.amount)
      }));

      const liabilities = balanceSheet.liabilities.items.map(item => ({
        category: 'Liabilities',
        name: item.name,
        amount: formatCurrencyForCSV(item.amount)
      }));

      const equity = balanceSheet.equity.items.map(item => ({
        category: 'Equity',
        name: item.name,
        amount: formatCurrencyForCSV(item.amount)
      }));

      const balanceSheetData = [...assets, ...liabilities, ...equity];

      // Generate filename with date
      const formattedDate = format(endDate, 'yyyy-MM-dd');
      const filename = `balance-sheet-${formattedDate}.csv`;

      // Define headers for the CSV file
      const headers = {
        category: 'Category',
        name: 'Account',
        amount: 'Amount (UGX)'
      };

      // Export to CSV
      exportToCSV(balanceSheetData, filename, headers);

      // Show success message
      toast({
        title: 'Export Successful',
        description: `Balance Sheet exported to ${filename}`,
        variant: 'default'
      });
    } catch (error) {
      console.error('Error exporting balance sheet:', error);
      toast({
        title: 'Export Failed',
        description: 'There was an error exporting the balance sheet. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setExporting(false);
    }
  };

  const handleExportProfitAndLoss = async (): Promise<void> => {
    try {
      setExporting(true);

      if (!profitAndLoss) {
        toast({
          title: 'Export Failed',
          description: 'No profit and loss data available to export.',
          variant: 'destructive'
        });
        return;
      }

      // Format profit and loss data for CSV export
      const revenue = profitAndLoss.revenue.items.map(item => ({
        category: 'Revenue',
        name: item.name,
        amount: formatCurrencyForCSV(item.amount)
      }));

      const expenses = profitAndLoss.expenses.items.map(item => ({
        category: 'Expenses',
        name: item.name,
        amount: formatCurrencyForCSV(item.amount)
      }));

      const profitAndLossData = [
        ...revenue,
        ...expenses,
        {
          category: 'Summary',
          name: 'Total Revenue',
          amount: formatCurrencyForCSV(profitAndLoss.revenue.total)
        },
        {
          category: 'Summary',
          name: 'Total Expenses',
          amount: formatCurrencyForCSV(profitAndLoss.expenses.total)
        },
        {
          category: 'Summary',
          name: 'Net Profit/Loss',
          amount: formatCurrencyForCSV(profitAndLoss.netProfit)
        }
      ];

      // Generate filename with date range
      const formattedStartDate = format(startDate, 'yyyy-MM-dd');
      const formattedEndDate = format(endDate, 'yyyy-MM-dd');
      const filename = `profit-and-loss-${formattedStartDate}-to-${formattedEndDate}.csv`;

      // Define headers for the CSV file
      const headers = {
        category: 'Category',
        name: 'Account',
        amount: 'Amount (UGX)'
      };

      // Export to CSV
      exportToCSV(profitAndLossData, filename, headers);

      // Show success message
      toast({
        title: 'Export Successful',
        description: `Profit and Loss report exported to ${filename}`,
        variant: 'default'
      });
    } catch (error) {
      console.error('Error exporting profit and loss:', error);
      toast({
        title: 'Export Failed',
        description: 'There was an error exporting the profit and loss report. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setExporting(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button disabled={isLoading || exporting}>
          <Download className="mr-2 h-4 w-4" />
          {exporting ? 'Exporting...' : 'Export Reports'}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={handleExportBalanceSheet} disabled={!balanceSheet}>
          Export Balance Sheet
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleExportProfitAndLoss} disabled={!profitAndLoss}>
          Export Profit & Loss
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
