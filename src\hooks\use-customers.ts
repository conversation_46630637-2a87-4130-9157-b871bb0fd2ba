
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { toast } from "@/hooks/use-toast";
import {
  Customer,
  CustomerInsert,
  CustomerUpdate
} from "@/types/index";

/**
 * Form values for creating or updating a customer
 */
export interface CustomerFormValues {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  tax_id?: string;
  contact_name?: string;
  notes?: string;
}

export const useCustomers = () => {
  const { currentCompanyId } = useAuth();
  const queryClient = useQueryClient();

  /**
   * Query to fetch all active customers for the current company
   */
  const {
    data: customers,
    isLoading,
    error,
  } = useQuery<Customer[]>({
    queryKey: ['customers', currentCompanyId],
    queryFn: async () => {
      if (!currentCompanyId) return [];

      const { data, error } = await supabase
        .from('customers')
        .select('*')
        .eq('company_id', currentCompanyId)
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      return data as Customer[];
    },
    enabled: !!currentCompanyId,
  });

  /**
   * Mutation to create a new customer
   */
  const createCustomerMutation = useMutation<Customer, Error, CustomerFormValues>({
    mutationFn: async (customerData: CustomerFormValues) => {
      if (!currentCompanyId) throw new Error("Company ID is required");

      // Prepare customer data for insertion
      const customerInsert: CustomerInsert = {
        company_id: currentCompanyId,
        name: customerData.name,
        email: customerData.email || null,
        phone: customerData.phone || null,
        address: customerData.address || null,
        city: customerData.city || null,
        country: customerData.country || 'Uganda',
        tax_id: customerData.tax_id || null,
        contact_name: customerData.contact_name || null,
        notes: customerData.notes || null,
        is_active: true,
      };

      const { data, error } = await supabase
        .from('customers')
        .insert(customerInsert)
        .select()
        .single();

      if (error) throw error;
      return data as Customer;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      toast({
        title: "Customer created",
        description: "The customer has been created successfully.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error creating customer",
        description: error.message || "Failed to create customer. Please try again.",
        variant: "destructive",
      });
    }
  });

  /**
   * Mutation to update an existing customer
   */
  const updateCustomerMutation = useMutation<
    Customer,
    Error,
    { id: string; customerData: CustomerFormValues }
  >({
    mutationFn: async ({
      id,
      customerData
    }) => {
      // Prepare customer data for update
      const customerUpdate: CustomerUpdate = {
        name: customerData.name,
        email: customerData.email || null,
        phone: customerData.phone || null,
        address: customerData.address || null,
        city: customerData.city || null,
        country: customerData.country || 'Uganda',
        tax_id: customerData.tax_id || null,
        contact_name: customerData.contact_name || null,
        notes: customerData.notes || null,
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('customers')
        .update(customerUpdate)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data as Customer;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      toast({
        title: "Customer updated",
        description: "The customer has been updated successfully.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error updating customer",
        description: error.message || "Failed to update customer. Please try again.",
        variant: "destructive",
      });
    }
  });

  /**
   * Mutation to soft-delete a customer by setting is_active to false
   */
  const deleteCustomerMutation = useMutation<boolean, Error, string>({
    mutationFn: async (customerId: string) => {
      // Soft delete by setting is_active to false
      const customerUpdate: CustomerUpdate = {
        is_active: false,
        updated_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from('customers')
        .update(customerUpdate)
        .eq('id', customerId);

      if (error) throw error;
      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      toast({
        title: "Customer deleted",
        description: "The customer has been deleted successfully.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error deleting customer",
        description: error.message || "Failed to delete customer. Please try again.",
        variant: "destructive",
      });
    }
  });

  /**
   * Return the hook's API
   */
  return {
    // Query results
    customers,
    isLoading,
    error,

    // Mutations
    createCustomer: createCustomerMutation.mutate,
    isCreatingCustomer: createCustomerMutation.isPending,
    updateCustomer: updateCustomerMutation.mutate,
    isUpdatingCustomer: updateCustomerMutation.isPending,
    deleteCustomer: deleteCustomerMutation.mutate,
    isDeletingCustomer: deleteCustomerMutation.isPending,
  };
};
