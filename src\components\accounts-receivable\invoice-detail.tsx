import { useState } from "react";
import { format } from "date-fns";
import { useInvoices } from "@/hooks/use-invoices";
import { Receipt, ArrowRight, FilePen, Mail, Trash2, FileText, CreditCard } from "lucide-react";
import { CreditNoteDialog } from "./credit-note-dialog";

import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface Customer {
  id: string;
  name: string;
  email?: string;
}

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  tax_rate: number;
  amount: number;
}

export type InvoiceWithRelations = {
  id: string;
  invoice_number: string;
  issue_date: string;
  due_date: string;
  status: string;
  amount: number;
  tax_amount: number;
  total_amount: number;
  notes?: string;
  customers: Customer;
  invoice_items: InvoiceItem[];
};

interface InvoiceDetailProps {
  invoice: InvoiceWithRelations;
  onClose: () => void;
}

export const InvoiceDetail = ({ invoice, onClose }: InvoiceDetailProps): JSX.Element => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [printPreviewOpen, setPrintPreviewOpen] = useState(false);

  const { updateInvoiceStatus, deleteInvoice, isDeletingInvoice } = useInvoices();

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'paid':
        return "bg-green-500 hover:bg-green-600";
      case 'overdue':
        return "bg-red-500 hover:bg-red-600";
      case 'sent':
        return "bg-blue-500 hover:bg-blue-600";
      case 'draft':
        return "bg-gray-500 hover:bg-gray-600";
      case 'cancelled':
        return "bg-red-500 hover:bg-red-600";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  const handleUpdateStatus = (status: "draft" | "sent" | "paid" | "overdue" | "cancelled"): void => {
    updateInvoiceStatus({ invoiceId: invoice.id, status });
  };

  const handleDeleteInvoice = (): void => {
    deleteInvoice(invoice.id, {
      onSuccess: () => {
        setDeleteDialogOpen(false);
        onClose();
      }
    });
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <>
      <Card className="mb-6">
        <CardHeader className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <CardTitle>Invoice #{invoice.invoice_number}</CardTitle>
            <CardDescription>
              Issued on {format(new Date(invoice.issue_date), "PPP")} •
              Due on {format(new Date(invoice.due_date), "PPP")}
            </CardDescription>
          </div>
          <Badge className={`text-white ${getStatusColor(invoice.status)}`}>
            {invoice.status.toUpperCase()}
          </Badge>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium mb-1">Customer</h3>
              <p>{invoice.customers.name}</p>
              {invoice.customers.email && (
                <p className="text-sm text-muted-foreground">{invoice.customers.email}</p>
              )}
            </div>
            <div className="text-right">
              <h3 className="text-sm font-medium mb-1">Amount Due</h3>
              <p className="text-2xl font-bold">{formatCurrency(invoice.total_amount)}</p>
            </div>
          </div>

          <div>
            <h3 className="font-medium mb-2">Invoice Items</h3>
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Qty</TableHead>
                    <TableHead className="text-right">Unit Price</TableHead>
                    <TableHead className="text-right">Tax Rate</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invoice.invoice_items.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.description}</TableCell>
                      <TableCell className="text-right">{item.quantity}</TableCell>
                      <TableCell className="text-right">{formatCurrency(item.unit_price)}</TableCell>
                      <TableCell className="text-right">{item.tax_rate}%</TableCell>
                      <TableCell className="text-right">{formatCurrency(item.amount)}</TableCell>
                    </TableRow>
                  ))}
                  <TableRow>
                    <TableCell colSpan={4} className="text-right font-medium">
                      Subtotal
                    </TableCell>
                    <TableCell className="text-right">
                      {formatCurrency(invoice.amount)}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell colSpan={4} className="text-right font-medium">
                      Tax
                    </TableCell>
                    <TableCell className="text-right">
                      {formatCurrency(invoice.tax_amount)}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell colSpan={4} className="text-right font-medium">
                      Total
                    </TableCell>
                    <TableCell className="text-right font-bold">
                      {formatCurrency(invoice.total_amount)}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>

          {invoice.notes && (
            <div>
              <h3 className="font-medium mb-1">Notes</h3>
              <p className="text-sm text-muted-foreground">{invoice.notes}</p>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-wrap gap-2 justify-between">
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" onClick={() => setPrintPreviewOpen(true)}>
              <FileText className="mr-2 h-4 w-4" />
              Print / Download
            </Button>
            <Button variant="outline">
              <Mail className="mr-2 h-4 w-4" />
              Send Email
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {invoice.status === 'draft' && (
              <Button onClick={() => handleUpdateStatus('sent')}>
                <ArrowRight className="mr-2 h-4 w-4" />
                Send Invoice
              </Button>
            )}
            {invoice.status === 'sent' && (
              <Button onClick={() => handleUpdateStatus('paid')} variant="default">
                <Receipt className="mr-2 h-4 w-4" />
                Mark as Paid
              </Button>
            )}
            {(invoice.status === 'sent' || invoice.status === 'paid' || invoice.status === 'overdue') && (
              <CreditNoteDialog invoice={invoice} onCreditNoteCreated={onClose} />
            )}
            <Button variant="outline">
              <FilePen className="mr-2 h-4 w-4" />
              Edit
            </Button>
            <Button
              variant="destructive"
              onClick={() => setDeleteDialogOpen(true)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete invoice #{invoice.invoice_number}. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700"
              onClick={handleDeleteInvoice}
              disabled={isDeletingInvoice}
            >
              {isDeletingInvoice ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Print Preview Dialog */}
      <Dialog open={printPreviewOpen} onOpenChange={setPrintPreviewOpen}>
        <DialogContent className="max-w-4xl max-h-screen overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Invoice Preview</DialogTitle>
            <DialogDescription>
              Print or download this invoice as PDF
            </DialogDescription>
          </DialogHeader>
          <div className="p-6 bg-white border rounded-lg">
            <div className="flex justify-between items-start mb-8">
              <div>
                <h1 className="text-2xl font-bold">INVOICE</h1>
                <p className="text-sm text-gray-600">#{invoice.invoice_number}</p>
              </div>
              <div className="text-right">
                <h2 className="font-bold">Kaya Finance</h2>
                <p className="text-sm"><EMAIL></p>
                <p className="text-sm">Kampala, Uganda</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-8 mb-8">
              <div>
                <h3 className="font-bold mb-2">Bill To:</h3>
                <p>{invoice.customers.name}</p>
                {invoice.customers.email && <p>{invoice.customers.email}</p>}
              </div>
              <div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <p className="font-bold">Invoice Date:</p>
                  <p>{format(new Date(invoice.issue_date), "PPP")}</p>
                  <p className="font-bold">Due Date:</p>
                  <p>{format(new Date(invoice.due_date), "PPP")}</p>
                  <p className="font-bold">Status:</p>
                  <p className="uppercase">{invoice.status}</p>
                </div>
              </div>
            </div>

            <table className="w-full mb-8 text-sm">
              <thead>
                <tr className="border-b border-gray-300">
                  <th className="py-2 text-left">Description</th>
                  <th className="py-2 text-right">Qty</th>
                  <th className="py-2 text-right">Unit Price</th>
                  <th className="py-2 text-right">Tax</th>
                  <th className="py-2 text-right">Amount</th>
                </tr>
              </thead>
              <tbody>
                {invoice.invoice_items.map((item) => (
                  <tr key={item.id} className="border-b border-gray-200">
                    <td className="py-2">{item.description}</td>
                    <td className="py-2 text-right">{item.quantity}</td>
                    <td className="py-2 text-right">{formatCurrency(item.unit_price)}</td>
                    <td className="py-2 text-right">{item.tax_rate}%</td>
                    <td className="py-2 text-right">{formatCurrency(item.amount)}</td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr>
                  <td colSpan={4} className="py-2 text-right font-medium">Subtotal</td>
                  <td className="py-2 text-right">{formatCurrency(invoice.amount)}</td>
                </tr>
                <tr>
                  <td colSpan={4} className="py-2 text-right font-medium">Tax</td>
                  <td className="py-2 text-right">{formatCurrency(invoice.tax_amount)}</td>
                </tr>
                <tr>
                  <td colSpan={4} className="py-2 text-right font-bold">Total</td>
                  <td className="py-2 text-right font-bold">{formatCurrency(invoice.total_amount)}</td>
                </tr>
              </tfoot>
            </table>

            {invoice.notes && (
              <div className="mb-8">
                <h3 className="font-bold mb-2">Notes:</h3>
                <p className="text-sm">{invoice.notes}</p>
              </div>
            )}

            <div className="text-center text-sm text-gray-600 mt-8 pt-8 border-t">
              <p>Thank you for your business!</p>
              <p>Kaya Finance | Tax ID: UGA123456789</p>
            </div>
          </div>
          <div className="flex justify-end mt-4 space-x-2">
            <Button onClick={() => window.print()} className="print:hidden">
              <FileText className="mr-2 h-4 w-4" />
              Print Invoice
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
