import { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { OnboardingWizard } from '@/components/onboarding/OnboardingWizard';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';

/**
 * Onboarding page component
 * 
 * This page displays the onboarding wizard for new users.
 * It checks if the user has already completed onboarding and redirects to the dashboard if so.
 */
const Onboarding = () => {
  const { user, companies, loading } = useAuth();
  
  // Check if user has already completed onboarding
  const hasCompletedOnboarding = companies && companies.length > 0;
  
  // Set onboarding flag in user metadata
  useEffect(() => {
    const updateUserMetadata = async () => {
      if (user && !loading && !hasCompletedOnboarding) {
        try {
          await supabase.auth.updateUser({
            data: {
              onboarding_started: true,
              onboarding_started_at: new Date().toISOString(),
            },
          });
        } catch (error) {
          console.error('Error updating user metadata:', error);
        }
      }
    };
    
    updateUserMetadata();
  }, [user, loading, hasCompletedOnboarding]);
  
  // Show loading state while checking auth
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }
  
  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" replace />;
  }
  
  // Redirect to dashboard if already completed onboarding
  if (hasCompletedOnboarding) {
    return <Navigate to="/dashboard" replace />;
  }
  
  // Show onboarding wizard
  return <OnboardingWizard />;
};

export default Onboarding;
