
import {
  AccountCategory,
  AccountType,
  Account as DatabaseAccount,
  Transaction as DatabaseTransaction,
  AccountWithBalance
} from '@/types/index';

// UI-specific account model that extends the database model
export type Account = AccountWithBalance;

// UI-specific transaction model for the journal entry form
export interface Transaction {
  id: string;
  date: string;
  description: string;
  debitAccount: string;
  creditAccount: string;
  amount: number;
  reference?: string;
  status?: 'draft' | 'pending' | 'approved' | 'rejected' | 'posted';
}

export const accountCategories: AccountCategory[] = [
  'Assets',
  'Liabilities',
  'Equity',
  'Revenue',
  'Expenses'
];

// Empty chart of accounts - will be populated from the database
export const ugandanChartOfAccounts: Account[] = [];

export const getAccountById = async (id: string): Promise<Account | undefined> => {
  // This should be replaced with a Supabase query to get the account by ID
  // For now, return undefined
  return undefined;
};

export const getAccountsByCategory = async (category: AccountCategory): Promise<Account[]> => {
  // This should be replaced with a Supabase query to get accounts by category
  // For now, return an empty array
  return [];
};

export const formatUGX = (amount: number): string => {
  return new Intl.NumberFormat('en-UG', {
    style: 'currency',
    currency: 'UGX',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};
