import React, { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { CalendarIcon, Plus, Trash2 } from 'lucide-react';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { AccountWithBalance } from '@/types/index';

// Define the schema for a single transaction in the batch
const batchItemSchema = z.object({
  description: z.string().min(5, {
    message: "Description must be at least 5 characters",
  }).max(200, {
    message: "Description must be less than 200 characters",
  }),
  debitAccount: z.string({
    required_error: "Debit account is required",
  }),
  creditAccount: z.string({
    required_error: "Credit account is required",
  }),
  amount: z.coerce.number().positive({
    message: "Amount must be greater than 0",
  }).lt(**********, {
    message: "Amount must be less than UGX 1B",
  }),
  reference: z.string().max(50, {
    message: "Reference must be less than 50 characters",
  }).optional(),
}).refine(
  (data) => data.debitAccount !== data.creditAccount,
  {
    message: "Debit and credit accounts cannot be the same",
    path: ["creditAccount"],
  }
);

// Define the schema for the entire batch
const batchTransactionSchema = z.object({
  date: z.date({
    required_error: "Transaction date is required",
  }),
  batchName: z.string().min(3, {
    message: "Batch name must be at least 3 characters",
  }).max(100, {
    message: "Batch name must be less than 100 characters",
  }),
  batchDescription: z.string().max(500, {
    message: "Batch description must be less than 500 characters",
  }).optional(),
  transactions: z.array(batchItemSchema).min(1, {
    message: "At least one transaction is required",
  }),
});

type BatchTransactionFormValues = z.infer<typeof batchTransactionSchema>;

interface BatchTransactionFormProps {
  accounts: AccountWithBalance[];
  onSubmit: (data: BatchTransactionFormValues) => void;
  onCancel: () => void;
}

export const BatchTransactionForm: React.FC<BatchTransactionFormProps> = ({
  accounts,
  onSubmit,
  onCancel,
}) => {
  const form = useForm<BatchTransactionFormValues>({
    resolver: zodResolver(batchTransactionSchema),
    defaultValues: {
      date: new Date(),
      batchName: '',
      batchDescription: '',
      transactions: [
        {
          description: '',
          debitAccount: '',
          creditAccount: '',
          amount: 0,
          reference: '',
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "transactions",
  });

  // Group accounts by category for easier selection
  const accountsByCategory = accounts.reduce((acc, account) => {
    if (!acc[account.category]) {
      acc[account.category] = [];
    }
    acc[account.category].push(account);
    return acc;
  }, {} as Record<string, AccountWithBalance[]>);

  // Calculate batch totals
  const transactions = form.watch('transactions');
  const batchTotal = transactions.reduce((sum, transaction) => sum + (transaction.amount || 0), 0);

  // Check if the batch is balanced
  const isBalanced = () => {
    // For a batch to be balanced, the sum of all debits should equal the sum of all credits
    // In our simplified model, each transaction has one debit and one credit of equal amount
    // So the batch is always balanced if all transactions are valid
    return true;
  };

  const addTransaction = () => {
    append({
      description: '',
      debitAccount: '',
      creditAccount: '',
      amount: 0,
      reference: '',
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Transaction Date */}
          <FormField
            control={form.control}
            name="date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Transaction Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormDescription>
                  Date for all transactions in this batch
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Batch Name */}
          <FormField
            control={form.control}
            name="batchName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Batch Name</FormLabel>
                <FormControl>
                  <Input placeholder="Monthly Payroll" {...field} />
                </FormControl>
                <FormDescription>
                  A descriptive name for this batch of transactions
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Batch Description */}
        <FormField
          control={form.control}
          name="batchDescription"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Batch Description (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter batch description"
                  className="resize-none"
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormDescription>
                Additional details about this batch of transactions
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Transactions */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Transactions</h3>
            <Button type="button" variant="outline" onClick={addTransaction}>
              <Plus className="mr-2 h-4 w-4" />
              Add Transaction
            </Button>
          </div>

          {fields.map((field, index) => (
            <Card key={field.id} className="relative">
              <CardContent className="pt-6">
                <div className="absolute top-2 right-2">
                  {fields.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => remove(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">Remove</span>
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Description */}
                  <FormField
                    control={form.control}
                    name={`transactions.${index}.description`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Input placeholder="Transaction description" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Amount */}
                  <FormField
                    control={form.control}
                    name={`transactions.${index}.amount`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Amount</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                              UGX
                            </span>
                            <Input
                              type="number"
                              placeholder="0"
                              className="pl-12"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Debit Account */}
                  <FormField
                    control={form.control}
                    name={`transactions.${index}.debitAccount`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Debit Account</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select debit account" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="max-h-[300px]">
                            {Object.entries(accountsByCategory).map(([category, accounts]) => (
                              <React.Fragment key={category}>
                                <div className="px-2 py-1.5 text-sm font-semibold bg-muted">
                                  {category}
                                </div>
                                {accounts.map((account) => (
                                  <SelectItem key={account.id} value={account.id}>
                                    {account.code} - {account.name}
                                  </SelectItem>
                                ))}
                              </React.Fragment>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Credit Account */}
                  <FormField
                    control={form.control}
                    name={`transactions.${index}.creditAccount`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Credit Account</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select credit account" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="max-h-[300px]">
                            {Object.entries(accountsByCategory).map(([category, accounts]) => (
                              <React.Fragment key={category}>
                                <div className="px-2 py-1.5 text-sm font-semibold bg-muted">
                                  {category}
                                </div>
                                {accounts.map((account) => (
                                  <SelectItem key={account.id} value={account.id}>
                                    {account.code} - {account.name}
                                  </SelectItem>
                                ))}
                              </React.Fragment>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Reference (Optional) */}
                  <FormField
                    control={form.control}
                    name={`transactions.${index}.reference`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reference (Optional)</FormLabel>
                        <FormControl>
                          <Input placeholder="Invoice #123" {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Batch Summary */}
        <div className="bg-muted p-4 rounded-md">
          <h3 className="font-medium mb-2">Batch Summary</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Number of Transactions</p>
              <p className="font-medium">{fields.length}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Total Amount</p>
              <p className="font-medium">
                {new Intl.NumberFormat('en-UG', {
                  style: 'currency',
                  currency: 'UGX',
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0,
                }).format(batchTotal)}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Batch Status</p>
              <p className={`font-medium ${isBalanced() ? 'text-green-600' : 'text-red-600'}`}>
                {isBalanced() ? 'Balanced' : 'Unbalanced'}
              </p>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={!isBalanced()}>
            Create Batch
          </Button>
        </div>
      </form>
    </Form>
  );
};
