import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from './use-toast';
import { useAuth } from '@/context/AuthContext';

export interface TransactionBatch {
  id: string;
  company_id: string;
  name: string;
  description: string | null;
  transaction_date: string;
  status: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  transaction_count?: number;
  total_amount?: number;
}

export function useBatchTransactions() {
  const [batches, setBatches] = useState<TransactionBatch[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { currentCompanyId } = useAuth();

  // Fetch batches
  useEffect(() => {
    const fetchBatches = async () => {
      if (!currentCompanyId) return;

      try {
        setIsLoading(true);

        // Fetch batches with transaction counts and total amounts
        const { data, error } = await supabase.rpc('get_transaction_batches_with_stats', {
          p_company_id: currentCompanyId
        });

        if (error) throw error;

        setBatches(data || []);
      } catch (err) {
        console.error('Error fetching batch transactions:', err);
        toast({
          title: 'Error',
          description: 'Failed to load batch transactions',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchBatches();
  }, [currentCompanyId, toast]);

  /**
   * Post all transactions in a batch
   */
  const postBatch = async (batchId: string) => {
    try {
      const { data, error } = await supabase.rpc('post_batch_transactions', {
        p_batch_id: batchId
      });

      if (error) throw error;

      // Update local state
      setBatches(prev =>
        prev.map(batch => batch.id === batchId ? { ...batch, status: 'approved' } : batch)
      );

      toast({
        title: 'Success',
        description: `Posted ${data} transactions in batch`,
      });

      return true;
    } catch (err) {
      console.error('Error posting batch:', err);
      toast({
        title: 'Error',
        description: 'Failed to post batch',
        variant: 'destructive',
      });
      return false;
    }
  };

  /**
   * Reverse all transactions in a batch
   */
  const reverseBatch = async (batchId: string) => {
    try {
      const { data, error } = await supabase.rpc('reverse_batch_transactions', {
        p_batch_id: batchId
      });

      if (error) throw error;

      toast({
        title: 'Success',
        description: `Created reversal batch with ${data} transactions`,
      });

      // Refresh batches
      const { data: refreshedData, error: refreshError } = await supabase.rpc('get_transaction_batches_with_stats', {
        p_company_id: currentCompanyId
      });

      if (refreshError) throw refreshError;

      setBatches(refreshedData || []);

      return true;
    } catch (err) {
      console.error('Error reversing batch:', err);
      toast({
        title: 'Error',
        description: 'Failed to reverse batch',
        variant: 'destructive',
      });
      return false;
    }
  };

  /**
   * Delete a batch and all its transactions
   */
  const deleteBatch = async (batchId: string) => {
    try {
      // First, delete all transactions in the batch
      const { error: transactionError } = await supabase
        .from('transactions')
        .update({ deleted_at: new Date().toISOString() })
        .eq('batch_id', batchId);

      if (transactionError) throw transactionError;

      // Then, delete the batch
      const { error: batchError } = await supabase
        .from('transaction_batches')
        .update({ deleted_at: new Date().toISOString() })
        .eq('id', batchId);

      if (batchError) throw batchError;

      // Update local state
      setBatches(prev => prev.filter(batch => batch.id !== batchId));

      toast({
        title: 'Success',
        description: 'Batch deleted successfully',
      });

      return true;
    } catch (err) {
      console.error('Error deleting batch:', err);
      toast({
        title: 'Error',
        description: 'Failed to delete batch',
        variant: 'destructive',
      });
      return false;
    }
  };

  /**
   * Get transactions for a specific batch
   */
  const getBatchTransactions = async (batchId: string) => {
    try {
      const { data, error } = await supabase
        .from('transactions')
        .select(`
          *,
          transaction_items (
            *,
            accounts (
              id,
              name,
              code
            )
          )
        `)
        .eq('batch_id', batchId)
        .is('deleted_at', null);

      if (error) throw error;

      // Format the transactions
      const formattedTransactions = data?.map(tx => ({
        ...tx,
        items: tx.transaction_items.map((item: any) => ({
          ...item,
          account: item.accounts
        }))
      })) || [];

      return formattedTransactions;
    } catch (err) {
      console.error('Error fetching batch transactions:', err);
      toast({
        title: 'Error',
        description: 'Failed to load batch transactions',
        variant: 'destructive',
      });
      return [];
    }
  };

  return {
    batches,
    isLoading,
    postBatch,
    reverseBatch,
    deleteBatch,
    getBatchTransactions
  };
}
