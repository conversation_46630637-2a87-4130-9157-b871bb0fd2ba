/**
 * Utility functions for handling API errors
 * 
 * These functions help standardize error handling across the application
 * and provide consistent error messages to users.
 */

import { toast } from '@/components/ui/sonner';
import { PostgrestError } from '@supabase/supabase-js';

/**
 * Error codes for different types of API errors
 */
export enum ApiErrorCode {
  // Authentication errors
  UNAUTHORIZED = 'auth/unauthorized',
  INVALID_CREDENTIALS = 'auth/invalid-credentials',
  SESSION_EXPIRED = 'auth/session-expired',
  
  // Permission errors
  PERMISSION_DENIED = 'permission/denied',
  INSUFFICIENT_ROLE = 'permission/insufficient-role',
  
  // Resource errors
  NOT_FOUND = 'resource/not-found',
  ALREADY_EXISTS = 'resource/already-exists',
  CONFLICT = 'resource/conflict',
  
  // Validation errors
  VALIDATION_FAILED = 'validation/failed',
  INVALID_INPUT = 'validation/invalid-input',
  
  // Database errors
  DATABASE_ERROR = 'database/error',
  QUERY_FAILED = 'database/query-failed',
  
  // Network errors
  NETWORK_ERROR = 'network/error',
  TIMEOUT = 'network/timeout',
  
  // Server errors
  SERVER_ERROR = 'server/error',
  SERVICE_UNAVAILABLE = 'server/unavailable',
  
  // Unknown errors
  UNKNOWN = 'unknown/error',
}

/**
 * Interface for standardized API error
 */
export interface ApiError {
  code: ApiErrorCode;
  message: string;
  details?: Record<string, unknown>;
  originalError?: unknown;
}

/**
 * Create a standardized API error
 * 
 * @param code Error code
 * @param message User-friendly error message
 * @param details Additional error details
 * @param originalError Original error object
 * @returns Standardized API error
 */
export const createApiError = (
  code: ApiErrorCode,
  message: string,
  details?: Record<string, unknown>,
  originalError?: unknown
): ApiError => {
  return {
    code,
    message,
    details,
    originalError,
  };
};

/**
 * Map Supabase error to standardized API error
 * 
 * @param error Supabase error
 * @returns Standardized API error
 */
export const mapSupabaseError = (error: PostgrestError | Error): ApiError => {
  // Handle PostgreSQL error codes
  if ('code' in error && error.code) {
    switch (error.code) {
      case '23505': // unique_violation
        return createApiError(
          ApiErrorCode.ALREADY_EXISTS,
          'This record already exists',
          { constraint: error.details },
          error
        );
        
      case '23503': // foreign_key_violation
        return createApiError(
          ApiErrorCode.VALIDATION_FAILED,
          'Referenced record does not exist',
          { constraint: error.details },
          error
        );
        
      case '42P01': // undefined_table
        return createApiError(
          ApiErrorCode.DATABASE_ERROR,
          'Database table not found',
          { table: error.details },
          error
        );
        
      case '42703': // undefined_column
        return createApiError(
          ApiErrorCode.DATABASE_ERROR,
          'Database column not found',
          { column: error.details },
          error
        );
    }
  }
  
  // Handle Supabase error messages
  if (error.message) {
    if (error.message.includes('JWT expired')) {
      return createApiError(
        ApiErrorCode.SESSION_EXPIRED,
        'Your session has expired. Please sign in again.',
        {},
        error
      );
    }
    
    if (error.message.includes('Invalid login credentials')) {
      return createApiError(
        ApiErrorCode.INVALID_CREDENTIALS,
        'Invalid email or password',
        {},
        error
      );
    }
    
    if (error.message.includes('No rows found')) {
      return createApiError(
        ApiErrorCode.NOT_FOUND,
        'The requested resource was not found',
        {},
        error
      );
    }
    
    if (error.message.includes('permission denied')) {
      return createApiError(
        ApiErrorCode.PERMISSION_DENIED,
        'You do not have permission to perform this action',
        {},
        error
      );
    }
    
    if (error.message.includes('network error')) {
      return createApiError(
        ApiErrorCode.NETWORK_ERROR,
        'Network error. Please check your internet connection.',
        {},
        error
      );
    }
  }
  
  // Handle Supabase status codes
  if ('status' in error && error.status) {
    switch (error.status) {
      case 401:
        return createApiError(
          ApiErrorCode.UNAUTHORIZED,
          'You are not authorized to perform this action',
          {},
          error
        );
        
      case 403:
        return createApiError(
          ApiErrorCode.PERMISSION_DENIED,
          'You do not have permission to perform this action',
          {},
          error
        );
        
      case 404:
        return createApiError(
          ApiErrorCode.NOT_FOUND,
          'The requested resource was not found',
          {},
          error
        );
        
      case 409:
        return createApiError(
          ApiErrorCode.CONFLICT,
          'This operation conflicts with the current state',
          {},
          error
        );
        
      case 422:
        return createApiError(
          ApiErrorCode.VALIDATION_FAILED,
          'Validation failed. Please check your input.',
          {},
          error
        );
        
      case 500:
        return createApiError(
          ApiErrorCode.SERVER_ERROR,
          'An unexpected server error occurred',
          {},
          error
        );
        
      case 503:
        return createApiError(
          ApiErrorCode.SERVICE_UNAVAILABLE,
          'Service temporarily unavailable',
          {},
          error
        );
    }
  }
  
  // Default to unknown error
  return createApiError(
    ApiErrorCode.UNKNOWN,
    'An unexpected error occurred',
    {},
    error
  );
};

/**
 * Handle API error with standardized approach
 * 
 * @param error Error to handle
 * @param options Options for error handling
 * @returns Standardized API error
 */
export const handleApiError = (
  error: unknown,
  options: {
    showToast?: boolean;
    logError?: boolean;
    defaultMessage?: string;
  } = {}
): ApiError => {
  const { showToast = true, logError = true, defaultMessage = 'An unexpected error occurred' } = options;

  // Convert unknown error to ApiError
  const apiError = error instanceof Error 
    ? mapSupabaseError(error)
    : createApiError(ApiErrorCode.UNKNOWN, defaultMessage, {}, error);

  // Log error if enabled
  if (logError) {
    console.error('API Error:', {
      code: apiError.code,
      message: apiError.message,
      details: apiError.details,
      originalError: apiError.originalError
    });
  }

  // Show toast if enabled
  if (showToast) {
    toast.error(String(apiError.message), {
      description: apiError.details?.message ? String(apiError.details.message) : undefined,
      id: apiError.code // Use code as ID to prevent duplicate toasts
    });
  }

  return apiError;
};

/**
 * Create an error handler for a specific operation
 * 
 * @param operation Name of the operation
 * @param options Options for error handling
 * @returns Error handler function
 */
export const createErrorHandler = (
  operation: string,
  options: {
    showToast?: boolean;
    logError?: boolean;
    defaultMessage?: string;
  } = {}
) => {
  return (error: unknown): ApiError => {
    const defaultMessage = options.defaultMessage || `Failed to ${operation}`;
    return handleApiError(error, {
      ...options,
      defaultMessage
    });
  };
};
