import React from 'react';
import { cn } from '@/lib/utils';

interface FormGridProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Number of columns on mobile (default: 1)
   */
  cols?: number;
  
  /**
   * Number of columns on tablet (default: 2)
   */
  colsMd?: number;
  
  /**
   * Number of columns on desktop (default: 3)
   */
  colsLg?: number;
  
  /**
   * Gap between grid items (default: 4)
   */
  gap?: number | string;
}

/**
 * A responsive grid layout for form fields
 */
export function FormGrid({
  children,
  className,
  cols = 1,
  colsMd = 2,
  colsLg = 3,
  gap = 4,
  ...props
}: FormGridProps) {
  // Convert gap to string with appropriate units if it's a number
  const gapValue = typeof gap === 'number' ? `${gap * 0.25}rem` : gap;
  
  return (
    <div
      className={cn(
        'grid',
        `grid-cols-${cols}`,
        `md:grid-cols-${colsMd}`,
        `lg:grid-cols-${colsLg}`,
        className
      )}
      style={{ gap: gapValue }}
      {...props}
    >
      {children}
    </div>
  );
}

interface FormGridItemProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Number of columns the item spans (default: 1)
   */
  span?: number;
  
  /**
   * Number of columns the item spans on tablet (default: span)
   */
  spanMd?: number;
  
  /**
   * Number of columns the item spans on desktop (default: spanMd)
   */
  spanLg?: number;
}

/**
 * A grid item for the FormGrid component
 */
export function FormGridItem({
  children,
  className,
  span = 1,
  spanMd,
  spanLg,
  ...props
}: FormGridItemProps) {
  // Default spanMd to span if not provided
  const mdSpan = spanMd ?? span;
  // Default spanLg to spanMd if not provided
  const lgSpan = spanLg ?? mdSpan;
  
  return (
    <div
      className={cn(
        `col-span-${span}`,
        `md:col-span-${mdSpan}`,
        `lg:col-span-${lgSpan}`,
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

/**
 * A full-width item for the FormGrid component
 */
export function FormGridFullWidth({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn('col-span-full', className)}
      {...props}
    >
      {children}
    </div>
  );
}
