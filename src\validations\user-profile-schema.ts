import * as z from 'zod';

/**
 * Validation schema for user profiles
 * 
 * This schema enforces validation rules for user profile data
 * including name, contact information, and company details.
 */
export const userProfileSchema = z.object({
  // Personal Information
  first_name: z.string()
    .min(2, { message: 'First name must be at least 2 characters' })
    .max(50, { message: 'First name must be less than 50 characters' })
    .regex(/^[a-zA-Z\s\-']+$/, { message: 'First name can only contain letters, spaces, hyphens, and apostrophes' }),
  
  last_name: z.string()
    .min(2, { message: 'Last name must be at least 2 characters' })
    .max(50, { message: 'Last name must be less than 50 characters' })
    .regex(/^[a-zA-Z\s\-']+$/, { message: 'Last name can only contain letters, spaces, hyphens, and apostrophes' }),
  
  // Contact Information
  email: z.string()
    .email({ message: 'Please enter a valid email address' })
    .max(100, { message: 'Email must be less than 100 characters' }),
  
  phone: z.string()
    .regex(/^(\+\d{1,3})?[\s.-]?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/, { 
      message: 'Please enter a valid phone number (e.g., +256 **********)' 
    })
    .optional()
    .or(z.literal('')),
  
  // Professional Information
  position: z.string()
    .max(100, { message: 'Position must be less than 100 characters' })
    .optional()
    .or(z.literal('')),
  
  company_name: z.string()
    .max(100, { message: 'Company name must be less than 100 characters' })
    .optional()
    .or(z.literal('')),
  
  // Profile Image
  avatar_url: z.string()
    .url({ message: 'Please enter a valid URL for the avatar' })
    .optional()
    .or(z.literal('')),
  
  // Additional Validation
  bio: z.string()
    .max(500, { message: 'Bio must be less than 500 characters' })
    .optional()
    .or(z.literal('')),
  
  // Social Media Links
  linkedin_url: z.string()
    .url({ message: 'Please enter a valid LinkedIn URL' })
    .regex(/linkedin\.com/, { message: 'Please enter a valid LinkedIn URL' })
    .optional()
    .or(z.literal('')),
  
  twitter_url: z.string()
    .url({ message: 'Please enter a valid Twitter URL' })
    .regex(/twitter\.com|x\.com/, { message: 'Please enter a valid Twitter URL' })
    .optional()
    .or(z.literal('')),
});

/**
 * Type for user profile form values
 */
export type UserProfileFormValues = z.infer<typeof userProfileSchema>;

/**
 * Validation schema for user profile update
 * 
 * This schema is used when updating an existing profile,
 * making all fields optional.
 */
export const userProfileUpdateSchema = userProfileSchema.partial();

/**
 * Type for user profile update form values
 */
export type UserProfileUpdateValues = z.infer<typeof userProfileUpdateSchema>;

/**
 * Validation schema for user password change
 */
export const passwordChangeSchema = z.object({
  current_password: z.string()
    .min(8, { message: 'Password must be at least 8 characters' }),
  
  new_password: z.string()
    .min(8, { message: 'Password must be at least 8 characters' })
    .max(72, { message: 'Password must be less than 72 characters' })
    .regex(/[A-Z]/, { message: 'Password must contain at least one uppercase letter' })
    .regex(/[a-z]/, { message: 'Password must contain at least one lowercase letter' })
    .regex(/[0-9]/, { message: 'Password must contain at least one number' }),
  
  confirm_password: z.string()
    .min(8, { message: 'Password must be at least 8 characters' }),
}).refine(
  (data) => data.new_password === data.confirm_password,
  {
    message: 'Passwords do not match',
    path: ['confirm_password'],
  }
);

/**
 * Type for password change form values
 */
export type PasswordChangeValues = z.infer<typeof passwordChangeSchema>;

/**
 * Validation schema for user notification preferences
 */
export const notificationPreferencesSchema = z.object({
  email_notifications: z.boolean().default(true),
  invoice_reminders: z.boolean().default(true),
  payment_notifications: z.boolean().default(true),
  system_updates: z.boolean().default(false),
  marketing_emails: z.boolean().default(false),
});

/**
 * Type for notification preferences form values
 */
export type NotificationPreferencesValues = z.infer<typeof notificationPreferencesSchema>;
