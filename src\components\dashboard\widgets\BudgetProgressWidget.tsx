import React, { useState, useEffect } from 'react';
import { TypedDashboardWidget, BudgetProgressSettings } from '@/types/dashboard';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine, Cell } from 'recharts';
import { ArrowUpRight, ArrowDownRight, Minus } from 'lucide-react';

interface BudgetProgressWidgetProps {
  widget: TypedDashboardWidget;
}

interface BudgetCategory {
  id: string;
  name: string;
  budgetAmount: number;
  actualAmount: number;
  variance: number;
  variancePercentage: number;
}

export function BudgetProgressWidget({ widget }: BudgetProgressWidgetProps): JSX.Element {
  const settings = widget.widget_settings as BudgetProgressSettings;
  const { currentCompanyId } = useAuth();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<BudgetCategory[]>([]);
  const [error, setError] = useState<string | null>(null);

  const showVariance = settings.showVariance !== false;
  const showPercentage = settings.showPercentage !== false;
  const showChart = settings.showChart !== false;
  const period = settings.period || 'monthly';
  const useFiscalYear = settings.useFiscalYear !== false;
  const budgetCategories = settings.budgetCategories || [];

  useEffect(() => {
    const fetchBudgetData = async () => {
      if (!currentCompanyId) return;

      try {
        setLoading(true);
        setError(null);

        // Get the date range based on the selected period
        const now = new Date();
        let startDate = new Date();
        let periodLabel = '';

        if (period === 'monthly') {
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          periodLabel = now.toLocaleString('default', { month: 'long' });
        } else if (period === 'quarterly') {
          const quarter = Math.floor(now.getMonth() / 3);
          startDate = new Date(now.getFullYear(), quarter * 3, 1);
          periodLabel = `Q${quarter + 1}`;
        } else if (period === 'yearly') {
          startDate = new Date(now.getFullYear(), 0, 1);
          periodLabel = now.getFullYear().toString();
        }

        // If using fiscal year, adjust the date range
        if (useFiscalYear) {
          // Fetch company fiscal year settings
          const { data: company, error: companyError } = await supabase
            .from('companies')
            .select('fiscal_year_start')
            .eq('id', currentCompanyId)
            .single();

          if (companyError) {
            console.error('Error fetching fiscal year settings:', companyError);
          } else if (company?.fiscal_year_start) {
            const fiscalYearStart = new Date(company.fiscal_year_start);
            const currentYear = now.getFullYear();

            // Create a date for this year's fiscal start
            const thisYearFiscalStart = new Date(
              currentYear,
              fiscalYearStart.getMonth(),
              fiscalYearStart.getDate()
            );

            // If current date is before this year's fiscal start, use last year's fiscal start
            if (now < thisYearFiscalStart) {
              startDate = new Date(
                currentYear - 1,
                fiscalYearStart.getMonth(),
                fiscalYearStart.getDate()
              );
            } else {
              startDate = thisYearFiscalStart;
            }

            periodLabel = `Fiscal Year ${currentYear}`;
          }
        }

        // Format dates for the query
        const startDateStr = startDate.toISOString().split('T')[0];
        const endDateStr = now.toISOString().split('T')[0];

        // First try to use the RPC if it exists
        try {
          const { data: budgetData, error: budgetError } = await supabase
            .rpc('get_budget_vs_actual', {
              p_company_id: currentCompanyId,
              p_start_date: startDateStr,
              p_end_date: endDateStr,
              p_categories: budgetCategories.length > 0 ? budgetCategories : null
            });

          if (budgetError) {
            throw budgetError;
          }

          if (budgetData && budgetData.length > 0) {
            setData(budgetData);
            return;
          }
        } catch (rpcError) {
          console.log('RPC not available, falling back to direct query');
        }

        // If RPC fails or returns no data, use direct query
        // First get budget data
        const { data: budgets, error: budgetsError } = await supabase
          .from('budgets')
          .select(`
            id,
            name,
            amount,
            period_start,
            period_end,
            category
          `)
          .eq('company_id', currentCompanyId)
          .lte('period_start', endDateStr)
          .gte('period_end', startDateStr);

        if (budgetsError) {
          throw budgetsError;
        }

        if (!budgets || budgets.length === 0) {
          setData([]);
          return;
        }

        // Get actual expense data for the same period
        const { data: transactions, error: transactionsError } = await supabase
          .from('transactions')
          .select(`
            id,
            transaction_date,
            transaction_items (
              id,
              debit,
              credit,
              account_id,
              accounts (
                id,
                name,
                account_type_id,
                account_types (
                  id,
                  name,
                  normal_balance
                )
              )
            )
          `)
          .eq('company_id', currentCompanyId)
          .gte('transaction_date', startDateStr)
          .lte('transaction_date', endDateStr);

        if (transactionsError) {
          throw transactionsError;
        }

        // Group expenses by category
        const expensesByCategory = new Map<string, number>();

        // Process transactions to extract expense data
        if (transactions && transactions.length > 0) {
          transactions.forEach(transaction => {
            if (!transaction.transaction_items) return;

            transaction.transaction_items.forEach(item => {
              if (!item.accounts || !item.accounts.account_types) return;

              const accountType = item.accounts.account_types.name.toLowerCase();

              // Only process expense accounts
              if (accountType.includes('expense')) {
                const categoryName = item.accounts.name;
                const amount = item.debit || 0; // Expenses are typically debits

                // Add to the category total
                const currentTotal = expensesByCategory.get(categoryName) || 0;
                expensesByCategory.set(categoryName, currentTotal + amount);
              }
            });
          });
        }

        // Combine budget and actual data
        const budgetProgressData: BudgetCategory[] = budgets.map(budget => {
          const actualAmount = expensesByCategory.get(budget.category || budget.name) || 0;
          const variance = budget.amount - actualAmount;
          const variancePercentage = budget.amount > 0 ? (variance / budget.amount) * 100 : 0;

          return {
            id: budget.id,
            name: budget.name,
            budgetAmount: budget.amount,
            actualAmount,
            variance,
            variancePercentage
          };
        });

        setData(budgetProgressData);
      } catch (err: any) {
        console.error('Error fetching budget data:', err);
        setError(err.message || 'Failed to load budget data');
      } finally {
        setLoading(false);
      }
    };

    fetchBudgetData();
  }, [currentCompanyId, period, useFiscalYear, budgetCategories.join(',')]);

  // Format currency for display
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Get variance indicator component
  const getVarianceIndicator = (variance: number, percentage: number) => {
    if (variance === 0) {
      return (
        <div className="flex items-center text-muted-foreground">
          <Minus className="h-3 w-3 mr-1" />
          <span>On budget</span>
        </div>
      );
    } else if (variance > 0) {
      return (
        <div className="flex items-center text-green-600">
          <ArrowDownRight className="h-3 w-3 mr-1" />
          <span>Under budget {showPercentage && `(${Math.abs(percentage).toFixed(1)}%)`}</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center text-red-600">
          <ArrowUpRight className="h-3 w-3 mr-1" />
          <span>Over budget {showPercentage && `(${Math.abs(percentage).toFixed(1)}%)`}</span>
        </div>
      );
    }
  };

  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const item = payload[0].payload;
      return (
        <div className="bg-background p-2 border rounded shadow-sm">
          <p className="font-medium">{item.name}</p>
          <p className="text-sm">Budget: {formatCurrency(item.budgetAmount)}</p>
          <p className="text-sm">Actual: {formatCurrency(item.actualAmount)}</p>
          {showVariance && (
            <p className="text-sm">
              Variance: {formatCurrency(item.variance)}
              {showPercentage && ` (${item.variancePercentage.toFixed(1)}%)`}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Skeleton className="h-[80%] w-[80%] rounded-md" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center text-center p-4">
        <p className="text-sm text-muted-foreground">
          Error loading budget data. Please try again later.
        </p>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="h-full flex items-center justify-center text-center p-4">
        <p className="text-sm text-muted-foreground">
          No budget data available for the selected period.
        </p>
      </div>
    );
  }

  // Render as chart if showChart is true
  if (showChart) {
    return (
      <div className="h-full">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip content={<CustomTooltip />} />
            <ReferenceLine y={0} stroke="#000" />
            <Bar dataKey="budgetAmount" name="Budget" fill="#8884d8" />
            <Bar dataKey="actualAmount" name="Actual" fill="#82ca9d">
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={entry.variance > 0 ? '#4ade80' : entry.variance < 0 ? '#f87171' : '#82ca9d'}
                />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>
    );
  }

  // Render as a list with progress bars
  return (
    <div className="h-full overflow-auto">
      <ul className="space-y-4">
        {data.map((category) => {
          const progressPercentage = Math.min(100, (category.actualAmount / category.budgetAmount) * 100);
          const isOverBudget = category.variance < 0;

          return (
            <li key={category.id} className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="font-medium text-sm">{category.name}</span>
                {showVariance && (
                  <span className="text-xs">
                    {getVarianceIndicator(category.variance, category.variancePercentage)}
                  </span>
                )}
              </div>

              <div className="space-y-1">
                <Progress
                  value={progressPercentage}
                  className={isOverBudget ? 'bg-red-100' : ''}
                  indicatorClassName={isOverBudget ? 'bg-red-500' : undefined}
                />

                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Actual: {formatCurrency(category.actualAmount)}</span>
                  <span>Budget: {formatCurrency(category.budgetAmount)}</span>
                </div>
              </div>
            </li>
          );
        })}
      </ul>
    </div>
  );
}
