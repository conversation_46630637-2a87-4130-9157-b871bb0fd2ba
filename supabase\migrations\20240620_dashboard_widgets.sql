-- Migration for dashboard widgets customization
-- This migration adds support for customizable dashboard widgets

-- Step 1: Add dashboard_layout field to user_preferences table
ALTER TABLE user_preferences 
ADD COLUMN IF NOT EXISTS dashboard_layout JSONB DEFAULT '[]';

-- Step 2: Create dashboard_widgets table to store widget configurations
CREATE TABLE IF NOT EXISTS dashboard_widgets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  widget_type TEXT NOT NULL,
  widget_title TEXT NOT NULL,
  widget_position JSONB NOT NULL DEFAULT '{"x": 0, "y": 0, "w": 1, "h": 1}',
  widget_settings JSONB DEFAULT '{}',
  is_visible BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, company_id, widget_type)
);

-- Step 3: Create index for faster widget lookup
CREATE INDEX idx_dashboard_widgets_user ON dashboard_widgets(user_id);
CREATE INDEX idx_dashboard_widgets_company ON dashboard_widgets(company_id);

-- Step 4: Create function to initialize default widgets for new users
CREATE OR REPLACE FUNCTION initialize_default_widgets()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert default widgets for the new user
  INSERT INTO dashboard_widgets (
    user_id, 
    company_id, 
    widget_type, 
    widget_title, 
    widget_position, 
    widget_settings
  )
  VALUES
  (
    NEW.user_id,
    NEW.last_accessed_company_id,
    'financial_overview',
    'Financial Overview',
    '{"x": 0, "y": 0, "w": 2, "h": 1}',
    '{"showRevenue": true, "showExpenses": true, "showProfit": true}'
  ),
  (
    NEW.user_id,
    NEW.last_accessed_company_id,
    'cash_flow',
    'Cash Flow',
    '{"x": 0, "y": 1, "w": 2, "h": 2}',
    '{"period": "monthly", "showInflow": true, "showOutflow": true}'
  ),
  (
    NEW.user_id,
    NEW.last_accessed_company_id,
    'recent_transactions',
    'Recent Transactions',
    '{"x": 2, "y": 0, "w": 1, "h": 2}',
    '{"limit": 5, "showAmount": true, "showDate": true}'
  ),
  (
    NEW.user_id,
    NEW.last_accessed_company_id,
    'account_balance',
    'Account Balance',
    '{"x": 2, "y": 2, "w": 1, "h": 1}',
    '{"showChart": true, "period": "monthly"}'
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 5: Create trigger to initialize default widgets for new users
DROP TRIGGER IF EXISTS initialize_widgets_trigger ON user_preferences;
CREATE TRIGGER initialize_widgets_trigger
AFTER INSERT ON user_preferences
FOR EACH ROW
WHEN (NEW.last_accessed_company_id IS NOT NULL)
EXECUTE FUNCTION initialize_default_widgets();

-- Step 6: Add RLS policies for dashboard_widgets table
ALTER TABLE dashboard_widgets ENABLE ROW LEVEL SECURITY;

-- Users can only view their own widgets
CREATE POLICY dashboard_widgets_select_policy ON dashboard_widgets
  FOR SELECT
  USING (user_id = auth.uid());

-- Users can only insert their own widgets
CREATE POLICY dashboard_widgets_insert_policy ON dashboard_widgets
  FOR INSERT
  WITH CHECK (user_id = auth.uid());

-- Users can only update their own widgets
CREATE POLICY dashboard_widgets_update_policy ON dashboard_widgets
  FOR UPDATE
  USING (user_id = auth.uid());

-- Users can only delete their own widgets
CREATE POLICY dashboard_widgets_delete_policy ON dashboard_widgets
  FOR DELETE
  USING (user_id = auth.uid());
