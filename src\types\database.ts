export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      account_types: {
        Row: {
          company_id: string
          created_at: string | null
          id: string
          name: string
          normal_balance: string
          updated_at: string | null
        }
        Insert: {
          company_id: string
          created_at?: string | null
          id?: string
          name: string
          normal_balance: string
          updated_at?: string | null
        }
        Update: {
          company_id?: string
          created_at?: string | null
          id?: string
          name?: string
          normal_balance?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "account_types_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      accounts: {
        Row: {
          account_type_id: string
          code: string
          company_id: string
          created_at: string | null
          deleted_at: string | null
          description: string | null
          id: string
          is_active: boolean
          name: string
          parent_account_id: string | null
          parent_account_path: unknown | null
          updated_at: string | null
        }
        Insert: {
          account_type_id: string
          code: string
          company_id: string
          created_at?: string | null
          deleted_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean
          name: string
          parent_account_id?: string | null
          parent_account_path?: unknown | null
          updated_at?: string | null
        }
        Update: {
          account_type_id?: string
          code?: string
          company_id?: string
          created_at?: string | null
          deleted_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean
          name?: string
          parent_account_id?: string | null
          parent_account_path?: unknown | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "accounts_account_type_id_fkey"
            columns: ["account_type_id"]
            isOneToOne: false
            referencedRelation: "account_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_parent_account_id_fkey"
            columns: ["parent_account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_logs: {
        Row: {
          affected_columns: string[] | null
          company_id: string
          created_at: string | null
          event_type: string
          id: string
          ip_address: string | null
          new_values: Json | null
          old_values: Json | null
          target_id: string | null
          target_type: string | null
          user_agent: string | null
          user_id: string
        }
        Insert: {
          affected_columns?: string[] | null
          company_id: string
          created_at?: string | null
          event_type: string
          id?: string
          ip_address?: string | null
          new_values?: Json | null
          old_values?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_agent?: string | null
          user_id: string
        }
        Update: {
          affected_columns?: string[] | null
          company_id?: string
          created_at?: string | null
          event_type?: string
          id?: string
          ip_address?: string | null
          new_values?: Json | null
          old_values?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_agent?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "audit_logs_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      bill_approval_history: {
        Row: {
          bill_id: string
          changed_by: string
          created_at: string | null
          from_status: string
          id: string
          notes: string | null
          to_status: string
        }
        Insert: {
          bill_id: string
          changed_by: string
          created_at?: string | null
          from_status: string
          id?: string
          notes?: string | null
          to_status: string
        }
        Update: {
          bill_id?: string
          changed_by?: string
          created_at?: string | null
          from_status?: string
          id?: string
          notes?: string | null
          to_status?: string
        }
        Relationships: [
          {
            foreignKeyName: "bill_approval_history_bill_id_fkey"
            columns: ["bill_id"]
            isOneToOne: false
            referencedRelation: "bills"
            referencedColumns: ["id"]
          },
        ]
      }
      bill_approval_thresholds: {
        Row: {
          company_id: string
          created_at: string | null
          id: string
          max_amount: number | null
          min_amount: number
          required_approvals: number
          updated_at: string | null
        }
        Insert: {
          company_id: string
          created_at?: string | null
          id?: string
          max_amount?: number | null
          min_amount: number
          required_approvals?: number
          updated_at?: string | null
        }
        Update: {
          company_id?: string
          created_at?: string | null
          id?: string
          max_amount?: number | null
          min_amount?: number
          required_approvals?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "bill_approval_thresholds_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      bill_items: {
        Row: {
          bill_id: string
          company_id: string
          created_at: string
          currency: string
          deleted_at: string | null
          description: string
          id: string
          line_total: number | null
          quantity: number
          tax_rate: number
          unit_price: number
          updated_at: string
        }
        Insert: {
          bill_id: string
          company_id: string
          created_at?: string
          currency?: string
          deleted_at?: string | null
          description: string
          id?: string
          line_total?: number | null
          quantity: number
          tax_rate?: number
          unit_price: number
          updated_at?: string
        }
        Update: {
          bill_id?: string
          company_id?: string
          created_at?: string
          currency?: string
          deleted_at?: string | null
          description?: string
          id?: string
          line_total?: number | null
          quantity?: number
          tax_rate?: number
          unit_price?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "bill_items_bill_id_fkey"
            columns: ["bill_id"]
            isOneToOne: false
            referencedRelation: "bills"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bill_items_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      bill_payments: {
        Row: {
          amount: number
          bill_id: string
          created_at: string | null
          id: string
          notes: string | null
          payment_date: string
          payment_method: string
          recorded_by: string
          reference: string | null
          updated_at: string | null
        }
        Insert: {
          amount: number
          bill_id: string
          created_at?: string | null
          id?: string
          notes?: string | null
          payment_date: string
          payment_method: string
          recorded_by: string
          reference?: string | null
          updated_at?: string | null
        }
        Update: {
          amount?: number
          bill_id?: string
          created_at?: string | null
          id?: string
          notes?: string | null
          payment_date?: string
          payment_method?: string
          recorded_by?: string
          reference?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "bill_payments_bill_id_fkey"
            columns: ["bill_id"]
            isOneToOne: false
            referencedRelation: "bills"
            referencedColumns: ["id"]
          },
        ]
      }
      bills: {
        Row: {
          amount: number
          bill_number: string
          company_id: string
          created_at: string
          created_by: string
          currency: string
          deleted_at: string | null
          due_date: string
          exchange_rate: number
          id: string
          issue_date: string
          notes: string | null
          status: Database["public"]["Enums"]["bill_status"]
          tax_amount: number
          total_amount: number
          updated_at: string
          vendor_id: string
        }
        Insert: {
          amount: number
          bill_number: string
          company_id: string
          created_at?: string
          created_by: string
          currency?: string
          deleted_at?: string | null
          due_date: string
          exchange_rate?: number
          id?: string
          issue_date: string
          notes?: string | null
          status?: Database["public"]["Enums"]["bill_status"]
          tax_amount?: number
          total_amount: number
          updated_at?: string
          vendor_id: string
        }
        Update: {
          amount?: number
          bill_number?: string
          company_id?: string
          created_at?: string
          created_by?: string
          currency?: string
          deleted_at?: string | null
          due_date?: string
          exchange_rate?: number
          id?: string
          issue_date?: string
          notes?: string | null
          status?: Database["public"]["Enums"]["bill_status"]
          tax_amount?: number
          total_amount?: number
          updated_at?: string
          vendor_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "bills_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bills_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      companies: {
        Row: {
          address: string | null
          base_currency: string
          city: string | null
          company_reg_number: string | null
          country: string | null
          created_at: string | null
          created_by: string
          deleted_at: string | null
          email: string | null
          fiscal_year_start: string | null
          id: string
          is_active: boolean
          legal_name: string | null
          name: string
          postal_code: string | null
          updated_at: string | null
          vat_number: string | null
        }
        Insert: {
          address?: string | null
          base_currency?: string
          city?: string | null
          company_reg_number?: string | null
          country?: string | null
          created_at?: string | null
          created_by: string
          deleted_at?: string | null
          email?: string | null
          fiscal_year_start?: string | null
          id?: string
          is_active?: boolean
          legal_name?: string | null
          name: string
          postal_code?: string | null
          updated_at?: string | null
          vat_number?: string | null
        }
        Update: {
          address?: string | null
          base_currency?: string
          city?: string | null
          company_reg_number?: string | null
          country?: string | null
          created_at?: string | null
          created_by?: string
          deleted_at?: string | null
          email?: string | null
          fiscal_year_start?: string | null
          id?: string
          is_active?: boolean
          legal_name?: string | null
          name?: string
          postal_code?: string | null
          updated_at?: string | null
          vat_number?: string | null
        }
        Relationships: []
      }
      company_settings: {
        Row: {
          company_id: string
          created_at: string | null
          currency: string
          date_format: string
          id: string
          is_onboarding_complete: boolean
          onboarding_step: number
          timezone: string
          updated_at: string | null
        }
        Insert: {
          company_id: string
          created_at?: string | null
          currency?: string
          date_format?: string
          id?: string
          is_onboarding_complete?: boolean
          onboarding_step?: number
          timezone?: string
          updated_at?: string | null
        }
        Update: {
          company_id?: string
          created_at?: string | null
          currency?: string
          date_format?: string
          id?: string
          is_onboarding_complete?: boolean
          onboarding_step?: number
          timezone?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "company_settings_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: true
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      credit_note_approval_history: {
        Row: {
          changed_by: string
          created_at: string | null
          credit_note_id: string
          from_status: string
          id: string
          notes: string | null
          to_status: string
        }
        Insert: {
          changed_by: string
          created_at?: string | null
          credit_note_id: string
          from_status: string
          id?: string
          notes?: string | null
          to_status: string
        }
        Update: {
          changed_by?: string
          created_at?: string | null
          credit_note_id?: string
          from_status?: string
          id?: string
          notes?: string | null
          to_status?: string
        }
        Relationships: [
          {
            foreignKeyName: "credit_note_approval_history_credit_note_id_fkey"
            columns: ["credit_note_id"]
            isOneToOne: false
            referencedRelation: "credit_notes"
            referencedColumns: ["id"]
          },
        ]
      }
      credit_note_items: {
        Row: {
          company_id: string
          created_at: string | null
          credit_note_id: string
          deleted_at: string | null
          description: string
          id: string
          invoice_item_id: string | null
          line_total: number | null
          quantity: number
          tax_rate: number
          unit_price: number
          updated_at: string | null
        }
        Insert: {
          company_id: string
          created_at?: string | null
          credit_note_id: string
          deleted_at?: string | null
          description: string
          id?: string
          invoice_item_id?: string | null
          line_total?: number | null
          quantity: number
          tax_rate?: number
          unit_price: number
          updated_at?: string | null
        }
        Update: {
          company_id?: string
          created_at?: string | null
          credit_note_id?: string
          deleted_at?: string | null
          description?: string
          id?: string
          invoice_item_id?: string | null
          line_total?: number | null
          quantity?: number
          tax_rate?: number
          unit_price?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "credit_note_items_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "credit_note_items_credit_note_id_fkey"
            columns: ["credit_note_id"]
            isOneToOne: false
            referencedRelation: "credit_notes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "credit_note_items_invoice_item_id_fkey"
            columns: ["invoice_item_id"]
            isOneToOne: false
            referencedRelation: "invoice_items"
            referencedColumns: ["id"]
          },
        ]
      }
      credit_notes: {
        Row: {
          amount: number
          approved_at: string | null
          approved_by: string | null
          company_id: string
          created_at: string | null
          created_by: string
          credit_note_number: string
          currency: string
          customer_id: string
          deleted_at: string | null
          exchange_rate: number | null
          id: string
          invoice_id: string | null
          issue_date: string
          notes: string | null
          reason: string | null
          status: string
          tax_amount: number
          total_amount: number
          updated_at: string | null
        }
        Insert: {
          amount: number
          approved_at?: string | null
          approved_by?: string | null
          company_id: string
          created_at?: string | null
          created_by: string
          credit_note_number: string
          currency?: string
          customer_id: string
          deleted_at?: string | null
          exchange_rate?: number | null
          id?: string
          invoice_id?: string | null
          issue_date: string
          notes?: string | null
          reason?: string | null
          status?: string
          tax_amount?: number
          total_amount: number
          updated_at?: string | null
        }
        Update: {
          amount?: number
          approved_at?: string | null
          approved_by?: string | null
          company_id?: string
          created_at?: string | null
          created_by?: string
          credit_note_number?: string
          currency?: string
          customer_id?: string
          deleted_at?: string | null
          exchange_rate?: number | null
          id?: string
          invoice_id?: string | null
          issue_date?: string
          notes?: string | null
          reason?: string | null
          status?: string
          tax_amount?: number
          total_amount?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "credit_notes_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "credit_notes_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "credit_notes_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
        ]
      }
      customers: {
        Row: {
          address: string | null
          city: string | null
          company_id: string
          contact_name: string | null
          country: string | null
          created_at: string | null
          deleted_at: string | null
          email: string | null
          id: string
          is_active: boolean
          name: string
          notes: string | null
          payment_terms: number | null
          phone: string | null
          postal_code: string | null
          tax_id: string | null
          updated_at: string | null
        }
        Insert: {
          address?: string | null
          city?: string | null
          company_id: string
          contact_name?: string | null
          country?: string | null
          created_at?: string | null
          deleted_at?: string | null
          email?: string | null
          id?: string
          is_active?: boolean
          name: string
          notes?: string | null
          payment_terms?: number | null
          phone?: string | null
          postal_code?: string | null
          tax_id?: string | null
          updated_at?: string | null
        }
        Update: {
          address?: string | null
          city?: string | null
          company_id?: string
          contact_name?: string | null
          country?: string | null
          created_at?: string | null
          deleted_at?: string | null
          email?: string | null
          id?: string
          is_active?: boolean
          name?: string
          notes?: string | null
          payment_terms?: number | null
          phone?: string | null
          postal_code?: string | null
          tax_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customers_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      dashboard_widgets: {
        Row: {
          company_id: string
          created_at: string | null
          id: string
          is_visible: boolean
          updated_at: string | null
          user_id: string
          widget_position: Json
          widget_settings: Json | null
          widget_title: string
          widget_type: string
        }
        Insert: {
          company_id: string
          created_at?: string | null
          id?: string
          is_visible?: boolean
          updated_at?: string | null
          user_id: string
          widget_position?: Json
          widget_settings?: Json | null
          widget_title: string
          widget_type: string
        }
        Update: {
          company_id?: string
          created_at?: string | null
          id?: string
          is_visible?: boolean
          updated_at?: string | null
          user_id?: string
          widget_position?: Json
          widget_settings?: Json | null
          widget_title?: string
          widget_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "dashboard_widgets_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      employees: {
        Row: {
          address: string | null
          bank_account: string | null
          bank_name: string | null
          base_salary: number
          company_id: string
          created_at: string | null
          deleted_at: string | null
          department: string | null
          email: string | null
          hire_date: string | null
          id: string
          is_active: boolean
          name: string
          nssf_number: string | null
          phone: string | null
          position: string | null
          tin: string | null
          updated_at: string | null
        }
        Insert: {
          address?: string | null
          bank_account?: string | null
          bank_name?: string | null
          base_salary: number
          company_id: string
          created_at?: string | null
          deleted_at?: string | null
          department?: string | null
          email?: string | null
          hire_date?: string | null
          id?: string
          is_active?: boolean
          name: string
          nssf_number?: string | null
          phone?: string | null
          position?: string | null
          tin?: string | null
          updated_at?: string | null
        }
        Update: {
          address?: string | null
          bank_account?: string | null
          bank_name?: string | null
          base_salary?: number
          company_id?: string
          created_at?: string | null
          deleted_at?: string | null
          department?: string | null
          email?: string | null
          hire_date?: string | null
          id?: string
          is_active?: boolean
          name?: string
          nssf_number?: string | null
          phone?: string | null
          position?: string | null
          tin?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "employees_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      invoice_items: {
        Row: {
          company_id: string
          created_at: string
          currency: string
          deleted_at: string | null
          description: string
          id: string
          invoice_id: string
          line_total: number | null
          quantity: number
          tax_rate: number
          unit_price: number
          updated_at: string
        }
        Insert: {
          company_id: string
          created_at?: string
          currency?: string
          deleted_at?: string | null
          description: string
          id?: string
          invoice_id: string
          line_total?: number | null
          quantity: number
          tax_rate?: number
          unit_price: number
          updated_at?: string
        }
        Update: {
          company_id?: string
          created_at?: string
          currency?: string
          deleted_at?: string | null
          description?: string
          id?: string
          invoice_id?: string
          line_total?: number | null
          quantity?: number
          tax_rate?: number
          unit_price?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "invoice_items_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoice_items_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          amount: number
          company_id: string
          created_at: string | null
          created_by: string
          currency: string
          customer_id: string
          deleted_at: string | null
          due_date: string
          exchange_rate: number | null
          id: string
          invoice_number: string
          issue_date: string
          notes: string | null
          status: Database["public"]["Enums"]["invoice_status"]
          tax_amount: number
          total_amount: number
          updated_at: string | null
        }
        Insert: {
          amount: number
          company_id: string
          created_at?: string | null
          created_by: string
          currency?: string
          customer_id: string
          deleted_at?: string | null
          due_date: string
          exchange_rate?: number | null
          id?: string
          invoice_number: string
          issue_date: string
          notes?: string | null
          status?: Database["public"]["Enums"]["invoice_status"]
          tax_amount?: number
          total_amount: number
          updated_at?: string | null
        }
        Update: {
          amount?: number
          company_id?: string
          created_at?: string | null
          created_by?: string
          currency?: string
          customer_id?: string
          deleted_at?: string | null
          due_date?: string
          exchange_rate?: number | null
          id?: string
          invoice_number?: string
          issue_date?: string
          notes?: string | null
          status?: Database["public"]["Enums"]["invoice_status"]
          tax_amount?: number
          total_amount?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "invoices_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      payroll_entries: {
        Row: {
          bank_account: string | null
          created_at: string | null
          employee_id: string
          gross_pay: number
          id: string
          net_pay: number
          notes: string | null
          payment_method: string | null
          payroll_run_id: string
          total_deductions: number
          total_taxes: number
          updated_at: string | null
        }
        Insert: {
          bank_account?: string | null
          created_at?: string | null
          employee_id: string
          gross_pay: number
          id?: string
          net_pay: number
          notes?: string | null
          payment_method?: string | null
          payroll_run_id: string
          total_deductions?: number
          total_taxes?: number
          updated_at?: string | null
        }
        Update: {
          bank_account?: string | null
          created_at?: string | null
          employee_id?: string
          gross_pay?: number
          id?: string
          net_pay?: number
          notes?: string | null
          payment_method?: string | null
          payroll_run_id?: string
          total_deductions?: number
          total_taxes?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payroll_entries_employee_id_fkey"
            columns: ["employee_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payroll_entries_payroll_run_id_fkey"
            columns: ["payroll_run_id"]
            isOneToOne: false
            referencedRelation: "payroll_runs"
            referencedColumns: ["id"]
          },
        ]
      }
      payroll_items: {
        Row: {
          amount: number
          created_at: string | null
          id: string
          item_type: Database["public"]["Enums"]["payroll_item_type"]
          name: string
          payroll_entry_id: string
          taxable: boolean | null
        }
        Insert: {
          amount: number
          created_at?: string | null
          id?: string
          item_type: Database["public"]["Enums"]["payroll_item_type"]
          name: string
          payroll_entry_id: string
          taxable?: boolean | null
        }
        Update: {
          amount?: number
          created_at?: string | null
          id?: string
          item_type?: Database["public"]["Enums"]["payroll_item_type"]
          name?: string
          payroll_entry_id?: string
          taxable?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "payroll_items_payroll_entry_id_fkey"
            columns: ["payroll_entry_id"]
            isOneToOne: false
            referencedRelation: "payroll_entries"
            referencedColumns: ["id"]
          },
        ]
      }
      payroll_runs: {
        Row: {
          company_id: string
          created_at: string | null
          created_by: string
          deleted_at: string | null
          id: string
          name: string
          notes: string | null
          pay_date: string
          pay_period_end: string
          pay_period_start: string
          status: string
          updated_at: string | null
        }
        Insert: {
          company_id: string
          created_at?: string | null
          created_by: string
          deleted_at?: string | null
          id?: string
          name: string
          notes?: string | null
          pay_date: string
          pay_period_end: string
          pay_period_start: string
          status?: string
          updated_at?: string | null
        }
        Update: {
          company_id?: string
          created_at?: string | null
          created_by?: string
          deleted_at?: string | null
          id?: string
          name?: string
          notes?: string | null
          pay_date?: string
          pay_period_end?: string
          pay_period_start?: string
          status?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payroll_runs_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          deleted_at: string | null
          first_name: string
          id: string
          last_name: string
          phone: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          deleted_at?: string | null
          first_name: string
          id?: string
          last_name: string
          phone?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          deleted_at?: string | null
          first_name?: string
          id?: string
          last_name?: string
          phone?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      recurring_transaction_items: {
        Row: {
          account_id: string
          created_at: string | null
          credit: number | null
          debit: number | null
          description: string | null
          id: string
          recurring_transaction_id: string
          updated_at: string | null
        }
        Insert: {
          account_id: string
          created_at?: string | null
          credit?: number | null
          debit?: number | null
          description?: string | null
          id?: string
          recurring_transaction_id: string
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string | null
          credit?: number | null
          debit?: number | null
          description?: string | null
          id?: string
          recurring_transaction_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "recurring_transaction_items_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "recurring_transaction_items_recurring_transaction_id_fkey"
            columns: ["recurring_transaction_id"]
            isOneToOne: false
            referencedRelation: "recurring_transactions"
            referencedColumns: ["id"]
          },
        ]
      }
      recurring_transactions: {
        Row: {
          company_id: string
          created_at: string | null
          created_by: string
          day_of_month: number | null
          day_of_week: number | null
          deleted_at: string | null
          description: string | null
          end_date: string | null
          frequency: Database["public"]["Enums"]["recurrence_frequency"]
          id: string
          last_generated_date: string | null
          name: string
          next_due_date: string
          start_date: string
          status: string
          updated_at: string | null
        }
        Insert: {
          company_id: string
          created_at?: string | null
          created_by: string
          day_of_month?: number | null
          day_of_week?: number | null
          deleted_at?: string | null
          description?: string | null
          end_date?: string | null
          frequency: Database["public"]["Enums"]["recurrence_frequency"]
          id?: string
          last_generated_date?: string | null
          name: string
          next_due_date: string
          start_date: string
          status?: string
          updated_at?: string | null
        }
        Update: {
          company_id?: string
          created_at?: string | null
          created_by?: string
          day_of_month?: number | null
          day_of_week?: number | null
          deleted_at?: string | null
          description?: string | null
          end_date?: string | null
          frequency?: Database["public"]["Enums"]["recurrence_frequency"]
          id?: string
          last_generated_date?: string | null
          name?: string
          next_due_date?: string
          start_date?: string
          status?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "recurring_transactions_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      transaction_approval_notifications: {
        Row: {
          created_at: string | null
          id: string
          is_read: boolean
          transaction_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_read?: boolean
          transaction_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          is_read?: boolean
          transaction_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "transaction_approval_notifications_transaction_id_fkey"
            columns: ["transaction_id"]
            isOneToOne: false
            referencedRelation: "transactions"
            referencedColumns: ["id"]
          },
        ]
      }
      transaction_approval_thresholds: {
        Row: {
          company_id: string
          created_at: string | null
          id: string
          max_amount: number | null
          min_amount: number
          required_approvals: number
          updated_at: string | null
        }
        Insert: {
          company_id: string
          created_at?: string | null
          id?: string
          max_amount?: number | null
          min_amount: number
          required_approvals?: number
          updated_at?: string | null
        }
        Update: {
          company_id?: string
          created_at?: string | null
          id?: string
          max_amount?: number | null
          min_amount?: number
          required_approvals?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "transaction_approval_thresholds_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      transaction_approvals: {
        Row: {
          approval_level: number
          approved_at: string | null
          approved_by: string
          created_at: string | null
          id: string
          notes: string | null
          transaction_id: string
        }
        Insert: {
          approval_level?: number
          approved_at?: string | null
          approved_by: string
          created_at?: string | null
          id?: string
          notes?: string | null
          transaction_id: string
        }
        Update: {
          approval_level?: number
          approved_at?: string | null
          approved_by?: string
          created_at?: string | null
          id?: string
          notes?: string | null
          transaction_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "transaction_approvals_transaction_id_fkey"
            columns: ["transaction_id"]
            isOneToOne: false
            referencedRelation: "transactions"
            referencedColumns: ["id"]
          },
        ]
      }
      transaction_batches: {
        Row: {
          company_id: string
          created_at: string | null
          created_by: string
          deleted_at: string | null
          description: string | null
          id: string
          name: string
          status: string
          transaction_date: string
          updated_at: string | null
        }
        Insert: {
          company_id: string
          created_at?: string | null
          created_by: string
          deleted_at?: string | null
          description?: string | null
          id?: string
          name: string
          status?: string
          transaction_date: string
          updated_at?: string | null
        }
        Update: {
          company_id?: string
          created_at?: string | null
          created_by?: string
          deleted_at?: string | null
          description?: string | null
          id?: string
          name?: string
          status?: string
          transaction_date?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "transaction_batches_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      transaction_items: {
        Row: {
          account_id: string
          created_at: string | null
          credit: number | null
          debit: number | null
          description: string | null
          id: string
          transaction_id: string
          updated_at: string | null
        }
        Insert: {
          account_id: string
          created_at?: string | null
          credit?: number | null
          debit?: number | null
          description?: string | null
          id?: string
          transaction_id: string
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string | null
          credit?: number | null
          debit?: number | null
          description?: string | null
          id?: string
          transaction_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "transaction_items_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transaction_items_transaction_id_fkey"
            columns: ["transaction_id"]
            isOneToOne: false
            referencedRelation: "transactions"
            referencedColumns: ["id"]
          },
        ]
      }
      transactions: {
        Row: {
          approval_level: number | null
          approval_threshold: number | null
          batch_id: string | null
          company_id: string
          created_at: string | null
          created_by: string
          currency: string
          deleted_at: string | null
          description: string | null
          exchange_rate: number | null
          id: string
          recurring_transaction_id: string | null
          reference: string | null
          status: Database["public"]["Enums"]["approval_status"] | null
          transaction_date: string
          transaction_source: string | null
          updated_at: string | null
        }
        Insert: {
          approval_level?: number | null
          approval_threshold?: number | null
          batch_id?: string | null
          company_id: string
          created_at?: string | null
          created_by: string
          currency?: string
          deleted_at?: string | null
          description?: string | null
          exchange_rate?: number | null
          id?: string
          recurring_transaction_id?: string | null
          reference?: string | null
          status?: Database["public"]["Enums"]["approval_status"] | null
          transaction_date: string
          transaction_source?: string | null
          updated_at?: string | null
        }
        Update: {
          approval_level?: number | null
          approval_threshold?: number | null
          batch_id?: string | null
          company_id?: string
          created_at?: string | null
          created_by?: string
          currency?: string
          deleted_at?: string | null
          description?: string | null
          exchange_rate?: number | null
          id?: string
          recurring_transaction_id?: string | null
          reference?: string | null
          status?: Database["public"]["Enums"]["approval_status"] | null
          transaction_date?: string
          transaction_source?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "transactions_batch_id_fkey"
            columns: ["batch_id"]
            isOneToOne: false
            referencedRelation: "transaction_batches"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transactions_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transactions_recurring_transaction_id_fkey"
            columns: ["recurring_transaction_id"]
            isOneToOne: false
            referencedRelation: "recurring_transactions"
            referencedColumns: ["id"]
          },
        ]
      }
      user_companies: {
        Row: {
          accepted_at: string | null
          company_id: string
          created_at: string | null
          id: string
          invited_at: string | null
          invited_by: string | null
          is_default: boolean
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string | null
          user_id: string
        }
        Insert: {
          accepted_at?: string | null
          company_id: string
          created_at?: string | null
          id?: string
          invited_at?: string | null
          invited_by?: string | null
          is_default?: boolean
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
          user_id: string
        }
        Update: {
          accepted_at?: string | null
          company_id?: string
          created_at?: string | null
          id?: string
          invited_at?: string | null
          invited_by?: string | null
          is_default?: boolean
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_companies_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      user_preferences: {
        Row: {
          created_at: string | null
          dashboard_layout: Json | null
          id: string
          last_accessed_company_id: string | null
          notification_settings: Json | null
          onboarding_complete: boolean
          theme: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          dashboard_layout?: Json | null
          id?: string
          last_accessed_company_id?: string | null
          notification_settings?: Json | null
          onboarding_complete?: boolean
          theme?: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          dashboard_layout?: Json | null
          id?: string
          last_accessed_company_id?: string | null
          notification_settings?: Json | null
          onboarding_complete?: boolean
          theme?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_preferences_last_accessed_company_id_fkey"
            columns: ["last_accessed_company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      vendors: {
        Row: {
          address: string | null
          city: string | null
          company_id: string
          contact_name: string | null
          country: string | null
          created_at: string | null
          deleted_at: string | null
          email: string | null
          id: string
          is_active: boolean
          name: string
          notes: string | null
          payment_terms: number | null
          phone: string | null
          postal_code: string | null
          tax_id: string | null
          updated_at: string | null
        }
        Insert: {
          address?: string | null
          city?: string | null
          company_id: string
          contact_name?: string | null
          country?: string | null
          created_at?: string | null
          deleted_at?: string | null
          email?: string | null
          id?: string
          is_active?: boolean
          name: string
          notes?: string | null
          payment_terms?: number | null
          phone?: string | null
          postal_code?: string | null
          tax_id?: string | null
          updated_at?: string | null
        }
        Update: {
          address?: string | null
          city?: string | null
          company_id?: string
          contact_name?: string | null
          country?: string | null
          created_at?: string | null
          deleted_at?: string | null
          email?: string | null
          id?: string
          is_active?: boolean
          name?: string
          notes?: string | null
          payment_terms?: number | null
          phone?: string | null
          postal_code?: string | null
          tax_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "vendors_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      _ltree_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      _ltree_gist_options: {
        Args: { "": unknown }
        Returns: undefined
      }
      generate_recurring_transactions: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      get_bill_paid_amount: {
        Args: { p_bill_id: string }
        Returns: number
      }
      get_bill_required_approvals: {
        Args: { p_company_id: string; p_amount: number }
        Returns: number
      }
      get_invoice_aging: {
        Args: { p_company_id: string; p_as_of_date?: string }
        Returns: {
          customer_id: string
          customer_name: string
          invoice_id: string
          invoice_number: string
          issue_date: string
          due_date: string
          total_amount: number
          paid_amount: number
          balance: number
          currency: string
          days_overdue: number
          aging_bucket: string
        }[]
      }
      get_required_approvals: {
        Args: { p_company_id: string; p_amount: number }
        Returns: number
      }
      get_transaction_batches_with_stats: {
        Args: { p_company_id: string }
        Returns: {
          id: string
          company_id: string
          name: string
          description: string
          transaction_date: string
          status: string
          created_by: string
          created_at: string
          updated_at: string
          deleted_at: string
          transaction_count: number
          total_amount: number
        }[]
      }
      has_company_access: {
        Args: { company_id: string }
        Returns: boolean
      }
      has_role: {
        Args: {
          company_id: string
          required_role: Database["public"]["Enums"]["user_role"]
        }
        Returns: boolean
      }
      is_company_admin: {
        Args: { company_id: string }
        Returns: boolean
      }
      lca: {
        Args: { "": unknown[] }
        Returns: unknown
      }
      lquery_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      lquery_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      lquery_recv: {
        Args: { "": unknown }
        Returns: unknown
      }
      lquery_send: {
        Args: { "": unknown }
        Returns: string
      }
      ltree_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      ltree_decompress: {
        Args: { "": unknown }
        Returns: unknown
      }
      ltree_gist_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      ltree_gist_options: {
        Args: { "": unknown }
        Returns: undefined
      }
      ltree_gist_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      ltree_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      ltree_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      ltree_recv: {
        Args: { "": unknown }
        Returns: unknown
      }
      ltree_send: {
        Args: { "": unknown }
        Returns: string
      }
      ltree2text: {
        Args: { "": unknown }
        Returns: string
      }
      ltxtq_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      ltxtq_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      ltxtq_recv: {
        Args: { "": unknown }
        Returns: unknown
      }
      ltxtq_send: {
        Args: { "": unknown }
        Returns: string
      }
      nlevel: {
        Args: { "": unknown }
        Returns: number
      }
      post_batch_transactions: {
        Args: { p_batch_id: string }
        Returns: number
      }
      reverse_batch_transactions: {
        Args: { p_batch_id: string }
        Returns: number
      }
      text2ltree: {
        Args: { "": string }
        Returns: unknown
      }
    }
    Enums: {
      approval_status: "pending" | "approved" | "rejected"
      bank_txn_type: "deposit" | "withdrawal" | "transfer"
      bill_status: "pending" | "approved" | "paid" | "overdue" | "cancelled"
      budget_status:
        | "draft"
        | "submitted"
        | "under_review"
        | "approved"
        | "rejected"
      invoice_status:
        | "draft"
        | "sent"
        | "paid"
        | "overdue"
        | "cancelled"
        | "credited"
      payroll_item_type: "earning" | "deduction" | "tax" | "benefit"
      recurrence_frequency:
        | "daily"
        | "weekly"
        | "monthly"
        | "quarterly"
        | "yearly"
      user_role: "admin" | "accountant" | "manager" | "viewer"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      approval_status: ["pending", "approved", "rejected"],
      bank_txn_type: ["deposit", "withdrawal", "transfer"],
      bill_status: ["pending", "approved", "paid", "overdue", "cancelled"],
      budget_status: [
        "draft",
        "submitted",
        "under_review",
        "approved",
        "rejected",
      ],
      invoice_status: [
        "draft",
        "sent",
        "paid",
        "overdue",
        "cancelled",
        "credited",
      ],
      payroll_item_type: ["earning", "deduction", "tax", "benefit"],
      recurrence_frequency: [
        "daily",
        "weekly",
        "monthly",
        "quarterly",
        "yearly",
      ],
      user_role: ["admin", "accountant", "manager", "viewer"],
    },
  },
} as const
