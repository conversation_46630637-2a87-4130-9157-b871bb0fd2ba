
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 33% 98%; /* Soft gray background */
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 210 100% 22%; /* Deep navy */
    --primary-foreground: 210 40% 98%;

    --secondary: 186 100% 29%; /* Teal highlights */
    --secondary-foreground: 210 40% 98%;

    --muted: 220 14% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 186 96% 90%; /* Light teal accent */
    --accent-foreground: 222 47% 11%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 222 84% 4.9%;

    --radius: 0.5rem;

    /* Sidebar specific colors */
    --sidebar-background: 210 100% 18%; /* Darker navy for sidebar */
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 186 100% 29%; /* Teal for active items */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 100% 22%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 210 100% 22%;
    --sidebar-ring: 186 100% 29%;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222 47% 11%;

    --secondary: 186 100% 29%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 33% 17%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 213 27% 84%;

    /* Dark mode sidebar */
    --sidebar-background: 210 100% 14%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 186 100% 29%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 100% 10%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 210 100% 14%;
    --sidebar-ring: 186 100% 29%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .nav-link {
    @apply flex items-center gap-3 rounded-md px-3 py-2 text-sm transition-all hover:text-sidebar-primary-foreground hover:bg-sidebar-accent;
  }

  .nav-link.active {
    @apply bg-sidebar-primary text-sidebar-primary-foreground;
  }

  .page-container {
    @apply w-full p-4 md:p-6 lg:p-8;
  }

  .page-title {
    @apply text-2xl md:text-3xl font-bold mb-6 text-primary;
  }

  .page-subtitle {
    @apply text-lg font-medium mb-4 text-muted-foreground;
  }

  /* Responsive form utilities */
  .form-grid {
    @apply grid gap-4 w-full;
  }

  .form-grid-1 {
    @apply grid grid-cols-1 gap-4 w-full;
  }

  .form-grid-2 {
    @apply grid grid-cols-1 md:grid-cols-2 gap-4 w-full;
  }

  .form-grid-3 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 w-full;
  }

  .form-grid-4 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 w-full;
  }

  .form-col-span-full {
    @apply col-span-full;
  }

  /* Mobile-friendly button groups */
  .button-group {
    @apply flex flex-wrap gap-2;
  }

  /* Responsive card layouts */
  .card-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
  }

  .card-grid-2 {
    @apply grid grid-cols-1 md:grid-cols-2 gap-4;
  }
}
