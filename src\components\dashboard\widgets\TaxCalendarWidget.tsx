import React, { useState, useEffect } from 'react';
import { TypedDashboardWidget, TaxCalendarSettings } from '@/types/dashboard';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { Calendar, Clock, AlertCircle } from 'lucide-react';
import { format, differenceInDays, isBefore } from 'date-fns';
import { Badge } from '@/components/ui/badge';

interface TaxCalendarWidgetProps {
  widget: TypedDashboardWidget;
}

interface TaxDeadline {
  id: string;
  taxType: 'vat' | 'paye' | 'withholding' | 'corporate' | 'local_service';
  description: string;
  dueDate: string;
  estimatedAmount: number | null;
  isPaid: boolean;
}

// Tax type display names and colors
const TAX_TYPES: Record<string, { label: string; color: string }> = {
  vat: { label: 'VAT', color: 'bg-blue-100 text-blue-800' },
  paye: { label: 'PAYE', color: 'bg-green-100 text-green-800' },
  withholding: { label: 'Withholding', color: 'bg-purple-100 text-purple-800' },
  corporate: { label: 'Corporate', color: 'bg-amber-100 text-amber-800' },
  local_service: { label: 'Local Service', color: 'bg-rose-100 text-rose-800' }
};

export function TaxCalendarWidget({ widget }: TaxCalendarWidgetProps): JSX.Element {
  const settings = widget.widget_settings as TaxCalendarSettings;
  const { currentCompanyId } = useAuth();
  const [loading, setLoading] = useState(true);
  const [deadlines, setDeadlines] = useState<TaxDeadline[]>([]);
  const [error, setError] = useState<string | null>(null);

  const taxTypes = settings.taxTypes || ['vat', 'paye', 'withholding'];
  const showDeadlines = settings.showDeadlines !== false;
  const showAmounts = settings.showAmounts !== false;
  const daysAhead = settings.daysAhead || 30;

  useEffect(() => {
    const fetchTaxDeadlines = async () => {
      if (!currentCompanyId) return;

      try {
        setLoading(true);
        setError(null);

        // Calculate date range
        const today = new Date();
        const endDate = new Date();
        endDate.setDate(today.getDate() + daysAhead);

        // Format dates for the query
        const todayStr = today.toISOString().split('T')[0];
        const endDateStr = endDate.toISOString().split('T')[0];

        // Fetch tax deadlines
        const { data: deadlinesData, error: deadlinesError } = await supabase
          .rpc('get_upcoming_tax_deadlines', {
            p_company_id: currentCompanyId,
            p_start_date: todayStr,
            p_end_date: endDateStr,
            p_tax_types: taxTypes
          });

        if (deadlinesError) {
          throw deadlinesError;
        }

        // First try to use the RPC if it exists
        try {
          if (deadlinesData && deadlinesData.length > 0) {
            setDeadlines(deadlinesData);
            return;
          }
        } catch (rpcError) {
          console.log('RPC not available, falling back to direct query');
        }

        // If RPC fails or returns no data, generate tax deadlines based on Uganda tax calendar
        // Get tax settings from the database
        const { data: taxSettings, error: taxSettingsError } = await supabase
          .from('tax_settings')
          .select('*')
          .eq('company_id', currentCompanyId);

        if (taxSettingsError) {
          console.error('Error fetching tax settings:', taxSettingsError);
        }

        // Generate upcoming tax deadlines based on Uganda tax calendar
        const generatedDeadlines: TaxDeadline[] = [];
        const currentDate = new Date();
        const currentMonth = currentDate.getMonth();
        const currentYear = currentDate.getFullYear();

        // Helper function to add months to a date
        const addMonths = (date: Date, months: number) => {
          const result = new Date(date);
          result.setMonth(result.getMonth() + months);
          return result;
        };

        // Get the most recent transactions to estimate tax amounts
        const { data: transactions, error: transactionsError } = await supabase
          .from('transactions')
          .select(`
            id,
            transaction_date,
            transaction_items (
              id,
              debit,
              credit,
              account_id,
              accounts (
                id,
                name,
                account_type_id,
                account_types (
                  id,
                  name
                )
              )
            )
          `)
          .eq('company_id', currentCompanyId)
          .order('transaction_date', { ascending: false })
          .limit(100);

        if (transactionsError) {
          console.error('Error fetching transactions:', transactionsError);
        }

        // Calculate estimated tax amounts based on recent transactions
        let estimatedVAT = 0;
        let estimatedPAYE = 0;
        let estimatedWithholding = 0;
        let estimatedCorporate = 0;
        let estimatedLocalService = 0;

        if (transactions && transactions.length > 0) {
          transactions.forEach(tx => {
            if (!tx.transaction_items) return;

            tx.transaction_items.forEach(item => {
              if (!item.accounts || !item.accounts.account_types) return;

              const accountName = item.accounts.name.toLowerCase();
              const accountType = item.accounts.account_types.name.toLowerCase();

              // Estimate tax amounts based on account types
              if (accountType.includes('tax') || accountName.includes('tax')) {
                if (accountName.includes('vat')) {
                  estimatedVAT += (item.debit || 0) - (item.credit || 0);
                } else if (accountName.includes('paye')) {
                  estimatedPAYE += (item.debit || 0) - (item.credit || 0);
                } else if (accountName.includes('withholding')) {
                  estimatedWithholding += (item.debit || 0) - (item.credit || 0);
                } else if (accountName.includes('corporate')) {
                  estimatedCorporate += (item.debit || 0) - (item.credit || 0);
                } else if (accountName.includes('local service')) {
                  estimatedLocalService += (item.debit || 0) - (item.credit || 0);
                }
              }
            });
          });
        }

        // Take absolute values and average for monthly estimates
        estimatedVAT = Math.abs(estimatedVAT) / 3;
        estimatedPAYE = Math.abs(estimatedPAYE) / 3;
        estimatedWithholding = Math.abs(estimatedWithholding) / 3;
        estimatedCorporate = Math.abs(estimatedCorporate) / 3;
        estimatedLocalService = Math.abs(estimatedLocalService) / 3;

        // Generate deadlines for each tax type
        if (taxTypes.includes('vat')) {
          // VAT is due on the 15th of each month for the previous month
          for (let i = 0; i < 3; i++) {
            const dueDate = new Date(currentYear, currentMonth + i, 15);
            if (getDaysRemaining(dueDate.toISOString()) <= daysAhead) {
              generatedDeadlines.push({
                id: `vat-${currentYear}-${currentMonth + i + 1}`,
                taxType: 'vat',
                description: `Monthly VAT Return - ${format(dueDate, 'MMMM yyyy')}`,
                dueDate: dueDate.toISOString().split('T')[0],
                estimatedAmount: Math.round(estimatedVAT),
                isPaid: false
              });
            }
          }
        }

        if (taxTypes.includes('paye')) {
          // PAYE is due on the 15th of each month
          for (let i = 0; i < 3; i++) {
            const dueDate = new Date(currentYear, currentMonth + i, 15);
            if (getDaysRemaining(dueDate.toISOString()) <= daysAhead) {
              generatedDeadlines.push({
                id: `paye-${currentYear}-${currentMonth + i + 1}`,
                taxType: 'paye',
                description: `Monthly PAYE Return - ${format(dueDate, 'MMMM yyyy')}`,
                dueDate: dueDate.toISOString().split('T')[0],
                estimatedAmount: Math.round(estimatedPAYE),
                isPaid: false
              });
            }
          }
        }

        if (taxTypes.includes('withholding')) {
          // Withholding tax is due on the 20th of each month
          for (let i = 0; i < 3; i++) {
            const dueDate = new Date(currentYear, currentMonth + i, 20);
            if (getDaysRemaining(dueDate.toISOString()) <= daysAhead) {
              generatedDeadlines.push({
                id: `withholding-${currentYear}-${currentMonth + i + 1}`,
                taxType: 'withholding',
                description: `Withholding Tax Return - ${format(dueDate, 'MMMM yyyy')}`,
                dueDate: dueDate.toISOString().split('T')[0],
                estimatedAmount: Math.round(estimatedWithholding),
                isPaid: false
              });
            }
          }
        }

        if (taxTypes.includes('corporate')) {
          // Corporate tax installments are due quarterly
          const currentQuarter = Math.floor(currentMonth / 3);
          for (let i = 0; i < 2; i++) {
            const quarterMonth = (currentQuarter + i) % 4 * 3;
            const dueDate = new Date(currentYear + Math.floor((currentQuarter + i) / 4), quarterMonth + 2, 15);
            if (getDaysRemaining(dueDate.toISOString()) <= daysAhead) {
              generatedDeadlines.push({
                id: `corporate-${currentYear}-Q${(currentQuarter + i) % 4 + 1}`,
                taxType: 'corporate',
                description: `Quarterly Corporate Tax Installment - Q${(currentQuarter + i) % 4 + 1} ${dueDate.getFullYear()}`,
                dueDate: dueDate.toISOString().split('T')[0],
                estimatedAmount: Math.round(estimatedCorporate),
                isPaid: false
              });
            }
          }
        }

        if (taxTypes.includes('local_service')) {
          // Local service tax is typically due at the end of each quarter
          const currentQuarter = Math.floor(currentMonth / 3);
          for (let i = 0; i < 2; i++) {
            const quarterMonth = (currentQuarter + i) % 4 * 3;
            const dueDate = new Date(currentYear + Math.floor((currentQuarter + i) / 4), quarterMonth + 2, 30);
            if (getDaysRemaining(dueDate.toISOString()) <= daysAhead) {
              generatedDeadlines.push({
                id: `local_service-${currentYear}-Q${(currentQuarter + i) % 4 + 1}`,
                taxType: 'local_service',
                description: `Local Service Tax - Q${(currentQuarter + i) % 4 + 1} ${dueDate.getFullYear()}`,
                dueDate: dueDate.toISOString().split('T')[0],
                estimatedAmount: Math.round(estimatedLocalService),
                isPaid: false
              });
            }
          }
        }

        // Sort deadlines by due date
        generatedDeadlines.sort((a, b) => {
          return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
        });

        setDeadlines(generatedDeadlines);
      } catch (err: any) {
        console.error('Error fetching tax deadlines:', err);
        setError(err.message || 'Failed to load tax deadlines');
      } finally {
        setLoading(false);
      }
    };

    fetchTaxDeadlines();
  }, [currentCompanyId, daysAhead, taxTypes.join(',')]);

  // Format currency for display
  const formatCurrency = (amount: number | null): string => {
    if (amount === null) return 'N/A';

    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format date for display
  const formatDate = (dateString: string): string => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (e) {
      return dateString;
    }
  };

  // Get days remaining until deadline
  const getDaysRemaining = (dateString: string): number => {
    try {
      const dueDate = new Date(dateString);
      const today = new Date();
      return differenceInDays(dueDate, today);
    } catch (e) {
      return 0;
    }
  };

  // Get urgency class based on days remaining
  const getUrgencyClass = (dateString: string): string => {
    const daysRemaining = getDaysRemaining(dateString);

    if (daysRemaining < 0) {
      return 'text-red-600';
    } else if (daysRemaining <= 3) {
      return 'text-red-600';
    } else if (daysRemaining <= 7) {
      return 'text-amber-600';
    } else {
      return 'text-green-600';
    }
  };

  // Get urgency message based on days remaining
  const getUrgencyMessage = (dateString: string): string => {
    const daysRemaining = getDaysRemaining(dateString);

    if (daysRemaining < 0) {
      return 'Overdue';
    } else if (daysRemaining === 0) {
      return 'Due today';
    } else if (daysRemaining === 1) {
      return 'Due tomorrow';
    } else {
      return `${daysRemaining} days left`;
    }
  };

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Skeleton className="h-[80%] w-[80%] rounded-md" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center text-center p-4">
        <p className="text-sm text-muted-foreground">
          Error loading tax deadlines. Please try again later.
        </p>
      </div>
    );
  }

  if (deadlines.length === 0) {
    return (
      <div className="h-full flex items-center justify-center text-center p-4">
        <p className="text-sm text-muted-foreground">
          No upcoming tax deadlines in the next {daysAhead} days.
        </p>
      </div>
    );
  }

  return (
    <div className="h-full overflow-auto">
      <ul className="space-y-3">
        {deadlines.map((deadline) => {
          const taxType = TAX_TYPES[deadline.taxType] || { label: deadline.taxType, color: 'bg-gray-100 text-gray-800' };
          const urgencyClass = getUrgencyClass(deadline.dueDate);
          const urgencyMessage = getUrgencyMessage(deadline.dueDate);
          const isPastDue = isBefore(new Date(deadline.dueDate), new Date());

          return (
            <li
              key={deadline.id}
              className={`p-3 rounded-md border ${isPastDue ? 'bg-red-50 border-red-200' : 'bg-card'}`}
            >
              <div className="flex justify-between items-start">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <Badge className={taxType.color}>
                      {taxType.label}
                    </Badge>
                    {isPastDue && (
                      <Badge variant="destructive" className="text-xs">
                        Overdue
                      </Badge>
                    )}
                  </div>
                  <h4 className="font-medium text-sm">{deadline.description}</h4>
                </div>
                {deadline.isPaid && (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    Paid
                  </Badge>
                )}
              </div>

              <div className="mt-2 flex flex-col sm:flex-row sm:justify-between text-xs text-muted-foreground">
                <div className="flex items-center gap-1 mb-1 sm:mb-0">
                  <Calendar className="h-3 w-3" />
                  <span>Due: {formatDate(deadline.dueDate)}</span>
                </div>

                {showDeadlines && (
                  <div className={`flex items-center gap-1 ${urgencyClass}`}>
                    {isPastDue ? (
                      <AlertCircle className="h-3 w-3" />
                    ) : (
                      <Clock className="h-3 w-3" />
                    )}
                    <span>{urgencyMessage}</span>
                  </div>
                )}
              </div>

              {showAmounts && deadline.estimatedAmount !== null && (
                <div className="mt-2 text-sm">
                  <span className="text-muted-foreground">Estimated amount: </span>
                  <span className="font-medium">{formatCurrency(deadline.estimatedAmount)}</span>
                </div>
              )}
            </li>
          );
        })}
      </ul>
    </div>
  );
}
