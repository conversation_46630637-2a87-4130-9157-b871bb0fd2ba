import React, { useState, useEffect } from 'react';
import { TypedDashboardWidget, RecentTransactionsSettings } from '@/types/dashboard';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';
import { ArrowUpRight, ArrowDownRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Transaction, TransactionItem, Account } from '@/types/index';

interface RecentTransactionsWidgetProps {
  widget: TypedDashboardWidget;
}

interface TransactionDisplay {
  id: string;
  date: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  category: string;
}

export function RecentTransactionsWidget({ widget }: RecentTransactionsWidgetProps): JSX.Element {
  const settings = widget.widget_settings as RecentTransactionsSettings;
  const { currentCompanyId } = useAuth();
  const [loading, setLoading] = useState(true);
  const [transactions, setTransactions] = useState<TransactionDisplay[]>([]);
  const [error, setError] = useState<string | null>(null);

  const limit = settings.limit || 5;
  const showAmount = settings.showAmount !== false;
  const showDate = settings.showDate !== false;
  const showCategory = settings.showCategory !== false;
  const transactionType = settings.transactionType || 'all';

  useEffect(() => {
    const fetchTransactions = async () => {
      if (!currentCompanyId) return;

      try {
        setLoading(true);
        setError(null);

        // Fetch recent transactions with their items and account information
        const { data, error: transactionsError } = await supabase
          .from('transactions')
          .select(`
            id,
            description,
            transaction_date,
            reference,
            created_at,
            transaction_items (
              id,
              debit,
              credit,
              account_id,
              description,
              accounts (
                id,
                name,
                code,
                account_type_id,
                account_types (
                  id,
                  name,
                  normal_balance
                )
              )
            )
          `)
          .eq('company_id', currentCompanyId)
          .order('transaction_date', { ascending: false })
          .limit(limit);

        if (transactionsError) {
          throw transactionsError;
        }

        if (!data || data.length === 0) {
          // Return empty array instead of mock data
          setTransactions([]);
          return;
        }

        // Transform the data into the format needed for display
        const transformedData: TransactionDisplay[] = data.map(transaction => {
          // Calculate the total amount of the transaction
          let totalAmount = 0;
          let transactionType: 'income' | 'expense' = 'expense';
          let category = '';

          // Find the main account category from the transaction items
          if (transaction.transaction_items && transaction.transaction_items.length > 0) {
            // Get the first item with an account that has account_types
            const itemWithAccount = transaction.transaction_items.find(
              item => item.accounts?.account_types
            );

            if (itemWithAccount) {
              // Determine if this is income or expense based on account type
              const accountType = itemWithAccount.accounts.account_types.name.toLowerCase();

              if (accountType.includes('revenue') || accountType.includes('income')) {
                transactionType = 'income';
                // For income, use the credit amount
                totalAmount = transaction.transaction_items.reduce(
                  (sum, item) => sum + (item.credit || 0), 0
                );
              } else if (accountType.includes('expense')) {
                transactionType = 'expense';
                // For expense, use the debit amount
                totalAmount = transaction.transaction_items.reduce(
                  (sum, item) => sum + (item.debit || 0), 0
                );
              } else {
                // For other types, just use the first item's amount
                totalAmount = (itemWithAccount.debit || 0) - (itemWithAccount.credit || 0);
              }

              // Use the account name as the category
              category = itemWithAccount.accounts.name;
            } else {
              // Fallback if no account type is found
              totalAmount = transaction.transaction_items.reduce(
                (sum, item) => sum + ((item.debit || 0) - (item.credit || 0)), 0
              );
              category = 'Uncategorized';
            }
          }

          return {
            id: transaction.id,
            date: transaction.transaction_date,
            description: transaction.description || transaction.reference || 'Transaction',
            amount: totalAmount,
            type: transactionType,
            category: category
          };
        });

        // Filter by transaction type if specified
        let filteredData = transformedData;
        if (transactionType !== 'all') {
          filteredData = transformedData.filter(t => t.type === transactionType);
        }

        setTransactions(filteredData);
      } catch (err: any) {
        console.error('Error fetching transactions:', err);
        setError(err.message || 'Failed to load transactions');
      } finally {
        setLoading(false);
      }
    };

    fetchTransactions();
  }, [currentCompanyId, limit, transactionType]);

  // Format currency for display
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      maximumFractionDigits: 0,
    }).format(Math.abs(amount));
  };

  // Format date for display
  const formatDate = (dateString: string): string => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (e) {
      return dateString;
    }
  };

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Skeleton className="h-[80%] w-[80%] rounded-md" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center text-center p-4">
        <p className="text-sm text-muted-foreground">
          Error loading transactions. Please try again later.
        </p>
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className="h-full flex items-center justify-center text-center p-4">
        <p className="text-sm text-muted-foreground">
          No recent transactions found.
        </p>
      </div>
    );
  }

  return (
    <div className="h-full overflow-auto">
      <ul className="space-y-3">
        {transactions.map((transaction) => (
          <li
            key={transaction.id}
            className="flex items-center justify-between p-2 rounded-md hover:bg-muted/50"
          >
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-full ${transaction.type === 'income' ? 'bg-green-100' : 'bg-red-100'}`}>
                {transaction.type === 'income' ? (
                  <ArrowDownRight className="h-4 w-4 text-green-600" />
                ) : (
                  <ArrowUpRight className="h-4 w-4 text-red-600" />
                )}
              </div>

              <div>
                <p className="font-medium text-sm">{transaction.description}</p>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  {showDate && (
                    <span>{formatDate(transaction.date)}</span>
                  )}

                  {showCategory && (
                    <>
                      {showDate && <span>•</span>}
                      <span>{transaction.category}</span>
                    </>
                  )}
                </div>
              </div>
            </div>

            {showAmount && (
              <div className={`font-medium ${transaction.type === 'income' ? 'text-green-600' : 'text-red-600'}`}>
                {transaction.type === 'income' ? '+' : '-'} {formatCurrency(transaction.amount)}
              </div>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
}
