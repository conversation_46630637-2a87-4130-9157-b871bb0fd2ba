import { format } from "date-fns";
import { supabase } from "@/integrations/supabase/client";
import { Tables } from "@/types/database";
import {
  CashFlowActivitySummary,
  CashFlowData,
  PeriodComparisonData
} from "./types";

type TransactionWithItems = Tables<'transactions'> & {
  items: Array<Tables<'transaction_items'> & {
    accounts: Tables<'accounts'> & {
      account_types: Tables<'account_types'>
    }
  }>
};

type TransactionItem = Tables<'transaction_items'> & {
  accounts: Tables<'accounts'> & {
    account_types: Tables<'account_types'>
  }
};

/**
 * Categorize a transaction for cash flow purposes
 *
 * @param transaction Transaction data with account information
 * @returns Category of the transaction (operating, investing, financing)
 */
const categorizeTransactionForCashFlow = (
  transaction: TransactionWithItems
): 'operating' | 'investing' | 'financing' => {
  // Default to operating activities
  let category: 'operating' | 'investing' | 'financing' = 'operating';

  // Check transaction items to determine the category
  for (const item of transaction.items) {
    const accountName = item.accounts.name.toLowerCase();
    const accountType = item.accounts.account_types.name.toLowerCase();

    // Investing activities typically involve long-term assets
    if (
      accountType.includes('fixed asset') ||
      accountType.includes('long-term investment') ||
      accountName.includes('equipment') ||
      accountName.includes('property') ||
      accountName.includes('land') ||
      accountName.includes('building') ||
      accountName.includes('investment')
    ) {
      category = 'investing';
      break;
    }

    // Financing activities involve debt and equity
    if (
      accountType.includes('long-term liability') ||
      accountType.includes('equity') ||
      accountName.includes('loan') ||
      accountName.includes('capital') ||
      accountName.includes('dividend') ||
      accountName.includes('share') ||
      accountName.includes('stock')
    ) {
      category = 'financing';
      break;
    }
  }

  return category;
};

/**
 * Calculate period-over-period comparison data
 *
 * @param currentValue Current period value
 * @param previousValue Previous period value
 * @returns Period comparison data
 */
const calculatePeriodComparison = (
  currentValue: number,
  previousValue: number
): PeriodComparisonData => {
  const percentageChange = previousValue !== 0
    ? ((currentValue - previousValue) / Math.abs(previousValue)) * 100
    : currentValue !== 0 ? 100 : 0;

  return {
    currentPeriodTotal: currentValue,
    previousPeriodTotal: previousValue,
    percentageChange
  };
};

/**
 * Fetch cash flow data for a company within a date range
 *
 * @param companyId ID of the company
 * @param startDate Start date for the report
 * @param endDate End date for the report
 * @returns Cash flow data or null if there was an error
 */
export const fetchCashFlow = async (
  companyId: string,
  startDate: Date,
  endDate: Date
): Promise<CashFlowData | null> => {
  try {
    // Format dates for query
    const formattedStartDate = format(startDate, 'yyyy-MM-dd');
    const formattedEndDate = format(endDate, 'yyyy-MM-dd');

    // Calculate previous period dates (same duration, immediately before current period)
    const periodDurationMs = endDate.getTime() - startDate.getTime();
    const previousPeriodEndDate = new Date(startDate.getTime() - 1); // Day before start date
    const previousPeriodStartDate = new Date(previousPeriodEndDate.getTime() - periodDurationMs);

    const formattedPrevStartDate = format(previousPeriodStartDate, 'yyyy-MM-dd');
    const formattedPrevEndDate = format(previousPeriodEndDate, 'yyyy-MM-dd');

    // Fetch transactions from Supabase for current period
    const { data: transactions, error } = await supabase
      .from('transactions')
      .select(`
        *,
        items:transaction_items(
          *,
          accounts:account_id(
            *,
            account_types:account_type_id(*)
          )
        )
      `)
      .eq('company_id', companyId)
      .gte('transaction_date', formattedStartDate)
      .lte('transaction_date', formattedEndDate)
      .is('deleted_at', null);

    if (error) {
      console.error('Error fetching transactions:', error);
      throw new Error('Failed to fetch transactions');
    }

    // Fetch transactions for previous period for comparison
    const { data: prevTransactions, error: prevError } = await supabase
      .from('transactions')
      .select(`
        *,
        items:transaction_items(
          *,
          accounts:account_id(
            *,
            account_types:account_type_id(*)
          )
        )
      `)
      .eq('company_id', companyId)
      .gte('transaction_date', formattedPrevStartDate)
      .lte('transaction_date', formattedPrevEndDate)
      .is('deleted_at', null);

    if (prevError) {
      console.error('Error fetching previous period transactions:', prevError);
      // Continue with current period data even if previous period fails
    }

    // Initialize activity summaries
    const operatingActivities: CashFlowActivitySummary = { total: 0, items: [] };
    const investingActivities: CashFlowActivitySummary = { total: 0, items: [] };
    const financingActivities: CashFlowActivitySummary = { total: 0, items: [] };

    // Initialize previous period summaries
    let prevOperatingActivitiesTotal = 0;
    let prevInvestingActivitiesTotal = 0;
    let prevFinancingActivitiesTotal = 0;

    // Process current period transactions
    if (transactions && transactions.length > 0) {
      // Group transactions by category
      const operatingTxns: TransactionWithItems[] = [];
      const investingTxns: TransactionWithItems[] = [];
      const financingTxns: TransactionWithItems[] = [];

      // Categorize transactions
      transactions.forEach(txn => {
        const category = categorizeTransactionForCashFlow(txn as TransactionWithItems);

        switch (category) {
          case 'operating':
            operatingTxns.push(txn as TransactionWithItems);
            break;
          case 'investing':
            investingTxns.push(txn as TransactionWithItems);
            break;
          case 'financing':
            financingTxns.push(txn as TransactionWithItems);
            break;
        }
      });

      // Process operating activities
      operatingTxns.forEach(txn => {
        const txnItems = txn.items || [];
        let txnAmount = 0;

        // Calculate transaction amount (cash impact)
        txnItems.forEach((item: TransactionItem) => {
          // For cash flow, we're interested in the impact on cash accounts
          const isAsset = item.accounts.account_types.name.toLowerCase().includes('asset');
          const isCashAccount = item.accounts.name.toLowerCase().includes('cash') ||
                               item.accounts.name.toLowerCase().includes('bank');

          if (isAsset && isCashAccount) {
            txnAmount += (item.debit || 0) - (item.credit || 0);
          }
        });

        if (txnAmount !== 0) {
          operatingActivities.items.push({
            name: txn.description || 'Operating transaction',
            amount: txnAmount,
            transaction_id: txn.id
          });

          operatingActivities.total += txnAmount;
        }
      });

      // Process investing activities
      investingTxns.forEach(txn => {
        const txnItems = txn.items || [];
        let txnAmount = 0;

        // Calculate transaction amount (cash impact)
        txnItems.forEach((item: TransactionItem) => {
          const isAsset = item.accounts.account_types.name.toLowerCase().includes('asset');
          const isCashAccount = item.accounts.name.toLowerCase().includes('cash') ||
                               item.accounts.name.toLowerCase().includes('bank');

          if (isAsset && isCashAccount) {
            txnAmount += (item.debit || 0) - (item.credit || 0);
          }
        });

        if (txnAmount !== 0) {
          investingActivities.items.push({
            name: txn.description || 'Investing transaction',
            amount: txnAmount,
            transaction_id: txn.id
          });

          investingActivities.total += txnAmount;
        }
      });

      // Process financing activities
      financingTxns.forEach(txn => {
        const txnItems = txn.items || [];
        let txnAmount = 0;

        // Calculate transaction amount (cash impact)
        txnItems.forEach((item: TransactionItem) => {
          const isAsset = item.accounts.account_types.name.toLowerCase().includes('asset');
          const isCashAccount = item.accounts.name.toLowerCase().includes('cash') ||
                               item.accounts.name.toLowerCase().includes('bank');

          if (isAsset && isCashAccount) {
            txnAmount += (item.debit || 0) - (item.credit || 0);
          }
        });

        if (txnAmount !== 0) {
          financingActivities.items.push({
            name: txn.description || 'Financing transaction',
            amount: txnAmount,
            transaction_id: txn.id
          });

          financingActivities.total += txnAmount;
        }
      });
    } else {
      // If no real data, return empty data instead of mock data
      operatingActivities.total = 0;
      operatingActivities.items = [];

      investingActivities.total = 0;
      investingActivities.items = [];

      financingActivities.total = 0;
      financingActivities.items = [];
    }

    // Process previous period transactions if available
    if (prevTransactions && prevTransactions.length > 0) {
      // Group transactions by category
      const prevOperatingTxns: TransactionWithItems[] = [];
      const prevInvestingTxns: TransactionWithItems[] = [];
      const prevFinancingTxns: TransactionWithItems[] = [];

      // Categorize transactions
      prevTransactions.forEach(txn => {
        const category = categorizeTransactionForCashFlow(txn as TransactionWithItems);

        switch (category) {
          case 'operating':
            prevOperatingTxns.push(txn as TransactionWithItems);
            break;
          case 'investing':
            prevInvestingTxns.push(txn as TransactionWithItems);
            break;
          case 'financing':
            prevFinancingTxns.push(txn as TransactionWithItems);
            break;
        }
      });

      // Calculate totals for each category
      prevOperatingTxns.forEach(txn => {
        const txnItems = txn.items || [];

        txnItems.forEach((item: TransactionItem) => {
          const isAsset = item.accounts.account_types.name.toLowerCase().includes('asset');
          const isCashAccount = item.accounts.name.toLowerCase().includes('cash') ||
                               item.accounts.name.toLowerCase().includes('bank');

          if (isAsset && isCashAccount) {
            prevOperatingActivitiesTotal += (item.debit || 0) - (item.credit || 0);
          }
        });
      });

      prevInvestingTxns.forEach(txn => {
        const txnItems = txn.items || [];

        txnItems.forEach((item: TransactionItem) => {
          const isAsset = item.accounts.account_types.name.toLowerCase().includes('asset');
          const isCashAccount = item.accounts.name.toLowerCase().includes('cash') ||
                               item.accounts.name.toLowerCase().includes('bank');

          if (isAsset && isCashAccount) {
            prevInvestingActivitiesTotal += (item.debit || 0) - (item.credit || 0);
          }
        });
      });

      prevFinancingTxns.forEach(txn => {
        const txnItems = txn.items || [];

        txnItems.forEach((item: TransactionItem) => {
          const isAsset = item.accounts.account_types.name.toLowerCase().includes('asset');
          const isCashAccount = item.accounts.name.toLowerCase().includes('cash') ||
                               item.accounts.name.toLowerCase().includes('bank');

          if (isAsset && isCashAccount) {
            prevFinancingActivitiesTotal += (item.debit || 0) - (item.credit || 0);
          }
        });
      });
    } else {
      // Return zeros for previous period if no real data
      prevOperatingActivitiesTotal = 0;
      prevInvestingActivitiesTotal = 0;
      prevFinancingActivitiesTotal = 0;
    }

    // Calculate net cash flow
    const netCashFlow = operatingActivities.total + investingActivities.total + financingActivities.total;
    const prevNetCashFlow = prevOperatingActivitiesTotal + prevInvestingActivitiesTotal + prevFinancingActivitiesTotal;

    // Create period comparison data
    const periodComparison = {
      operatingActivities: calculatePeriodComparison(operatingActivities.total, prevOperatingActivitiesTotal),
      investingActivities: calculatePeriodComparison(investingActivities.total, prevInvestingActivitiesTotal),
      financingActivities: calculatePeriodComparison(financingActivities.total, prevFinancingActivitiesTotal),
      netCashFlow: calculatePeriodComparison(netCashFlow, prevNetCashFlow),
      startDate: formattedPrevStartDate,
      endDate: formattedPrevEndDate
    };

    return {
      operatingActivities,
      investingActivities,
      financingActivities,
      netCashFlow,
      startDate: formattedStartDate,
      endDate: formattedEndDate,
      previousPeriod: periodComparison
    };
  } catch (error) {
    console.error('Error fetching cash flow data:', error);
    return null;
  }
};
