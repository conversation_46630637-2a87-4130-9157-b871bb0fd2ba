import { format } from 'date-fns';
import { 
  ClockIcon, 
  CheckCircleIcon, 
  XCircleIcon, 
  AlertTriangleIcon, 
  SendIcon,
  BanknoteIcon
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { InvoiceWithRelations } from '@/hooks/use-enhanced-invoices';

interface InvoiceStatusHistoryProps {
  invoice: InvoiceWithRelations;
}

/**
 * Component to display the status history of an invoice
 */
export function InvoiceStatusHistory({ invoice }: InvoiceStatusHistoryProps) {
  if (!invoice.invoice_status_history || invoice.invoice_status_history.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Status History</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm">No status history available</p>
        </CardContent>
      </Card>
    );
  }
  
  // Sort history by date (newest first)
  const sortedHistory = [...invoice.invoice_status_history].sort(
    (a, b) => new Date(b.changed_at).getTime() - new Date(a.changed_at).getTime()
  );
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Status History</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedHistory.map((entry) => (
            <div key={entry.id} className="flex items-start gap-3">
              <div className="mt-0.5">
                {getStatusIcon(entry.to_status)}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">
                      Changed to {' '}
                    </span>
                    <StatusBadge status={entry.to_status} />
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {format(new Date(entry.changed_at), 'MMM d, yyyy h:mm a')}
                  </span>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  From <StatusBadge status={entry.from_status} />
                </p>
                {entry.notes && (
                  <p className="text-sm mt-1 bg-muted p-2 rounded-md">
                    {entry.notes}
                  </p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  By: {entry.changed_by === 'system' ? 'System (Automated)' : entry.changed_by}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Helper function to get the appropriate icon for a status
 */
function getStatusIcon(status: string) {
  switch (status) {
    case 'draft':
      return <ClockIcon className="h-5 w-5 text-muted-foreground" />;
    case 'sent':
      return <SendIcon className="h-5 w-5 text-blue-500" />;
    case 'paid':
      return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
    case 'overdue':
      return <AlertTriangleIcon className="h-5 w-5 text-amber-500" />;
    case 'cancelled':
      return <XCircleIcon className="h-5 w-5 text-destructive" />;
    default:
      return <ClockIcon className="h-5 w-5 text-muted-foreground" />;
  }
}

/**
 * Component to display a status badge
 */
function StatusBadge({ status }: { status: string }) {
  switch (status) {
    case 'draft':
      return <Badge variant="outline">Draft</Badge>;
    case 'sent':
      return <Badge variant="secondary">Sent</Badge>;
    case 'paid':
      return <Badge variant="success">Paid</Badge>;
    case 'overdue':
      return <Badge variant="warning">Overdue</Badge>;
    case 'cancelled':
      return <Badge variant="destructive">Cancelled</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
}
