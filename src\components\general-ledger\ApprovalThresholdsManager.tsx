import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Pencil } from 'lucide-react';
import { useTransactionApprovals, TransactionApprovalThreshold } from '@/hooks/useTransactionApprovals';

export const ApprovalThresholdsManager: React.FC = () => {
  const { thresholds, updateApprovalThreshold } = useTransactionApprovals();
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedThreshold, setSelectedThreshold] = useState<TransactionApprovalThreshold | null>(null);
  const [minAmount, setMinAmount] = useState('');
  const [maxAmount, setMaxAmount] = useState('');
  const [requiredApprovals, setRequiredApprovals] = useState('');

  const handleEditClick = (threshold: TransactionApprovalThreshold) => {
    setSelectedThreshold(threshold);
    setMinAmount(threshold.min_amount.toString());
    setMaxAmount(threshold.max_amount?.toString() || '');
    setRequiredApprovals(threshold.required_approvals.toString());
    setEditDialogOpen(true);
  };

  const handleSave = async () => {
    if (!selectedThreshold) return;

    const success = await updateApprovalThreshold(selectedThreshold.id, {
      min_amount: parseFloat(minAmount),
      max_amount: maxAmount ? parseFloat(maxAmount) : null,
      required_approvals: parseInt(requiredApprovals),
    });

    if (success) {
      setEditDialogOpen(false);
    }
  };

  const formatCurrency = (amount: number | null): string => {
    if (amount === null) return 'No limit';
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Approval Thresholds</h2>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Min Amount</TableHead>
            <TableHead>Max Amount</TableHead>
            <TableHead>Required Approvals</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {thresholds.map((threshold) => (
            <TableRow key={threshold.id}>
              <TableCell>{formatCurrency(threshold.min_amount)}</TableCell>
              <TableCell>{formatCurrency(threshold.max_amount)}</TableCell>
              <TableCell>{threshold.required_approvals}</TableCell>
              <TableCell className="text-right">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditClick(threshold)}
                >
                  <Pencil className="h-4 w-4 mr-1" />
                  Edit
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Approval Threshold</DialogTitle>
            <DialogDescription>
              Update the transaction amount thresholds and required approval levels.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="min-amount">Minimum Amount (UGX)</Label>
              <Input
                id="min-amount"
                type="number"
                value={minAmount}
                onChange={(e) => setMinAmount(e.target.value)}
                placeholder="0"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="max-amount">Maximum Amount (UGX, leave empty for no limit)</Label>
              <Input
                id="max-amount"
                type="number"
                value={maxAmount}
                onChange={(e) => setMaxAmount(e.target.value)}
                placeholder="No limit"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="required-approvals">Required Approvals</Label>
              <Input
                id="required-approvals"
                type="number"
                min="1"
                max="10"
                value={requiredApprovals}
                onChange={(e) => setRequiredApprovals(e.target.value)}
                placeholder="1"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
