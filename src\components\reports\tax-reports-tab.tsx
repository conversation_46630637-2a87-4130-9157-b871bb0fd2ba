
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TaxReportData } from '@/services/report-service';
import { TaxReport } from '@/components/reports/tax-report';
import { FileText } from 'lucide-react';
import { YearQuarterSelector } from './year-quarter-selector';

interface TaxReportsTabProps {
  reportYear: number;
  reportQuarter: number;
  dateRange: string;
  taxReport: TaxReportData | null | undefined;
  isLoadingTaxReport: boolean;
  formatCurrency: (amount: number) => string;
  setReportYear: (year: number) => void;
  setReportQuarter: (quarter: number) => void;
  setDateRange: (range: string) => void;
}

export function TaxReportsTab({
  reportYear,
  reportQuarter,
  dateRange,
  taxReport,
  isLoadingTaxReport,
  formatCurrency,
  setReportYear,
  setReportQuarter,
  setDateRange
}: TaxReportsTabProps) {
  return (
    <div>
      <YearQuarterSelector
        reportYear={reportYear}
        reportQuarter={reportQuarter}
        dateRange={dateRange}
        setReportYear={setReportYear}
        setReportQuarter={setReportQuarter}
        setDateRange={setDateRange}
      />
      
      {taxReport ? (
        <TaxReport
          data={taxReport}
          formatCurrency={formatCurrency}
          year={reportYear}
          quarter={dateRange === 'quarter' ? reportQuarter : undefined}
          isLoading={isLoadingTaxReport}
        />
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Tax Reports</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-60 flex flex-col items-center justify-center gap-2">
              <FileText className="h-8 w-8 text-muted-foreground" />
              <p className="text-muted-foreground">No tax report data available</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
