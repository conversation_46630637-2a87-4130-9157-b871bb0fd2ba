import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from './use-toast';
import { useAuth } from '@/context/AuthContext';
import { TransactionWithAccounts, ApprovalStatus } from '@/types/index';

export interface TransactionApproval {
  id: string;
  transaction_id: string;
  approved_by: string;
  approval_level: number;
  approved_at: string;
  notes: string | null;
  created_at: string;
  user?: {
    id: string;
    email: string;
    user_metadata: {
      full_name?: string;
    };
  };
}

export interface TransactionApprovalNotification {
  id: string;
  transaction_id: string;
  user_id: string;
  is_read: boolean;
  created_at: string;
  updated_at: string;
  transaction?: {
    description: string;
    transaction_date: string;
  };
}

export interface TransactionApprovalThreshold {
  id: string;
  company_id: string;
  min_amount: number;
  max_amount: number | null;
  required_approvals: number;
  created_at: string;
  updated_at: string;
}

export function useTransactionApprovals() {
  const [pendingApprovals, setPendingApprovals] = useState<TransactionWithAccounts[]>([]);
  const [approvalHistory, setApprovalHistory] = useState<TransactionApproval[]>([]);
  const [notifications, setNotifications] = useState<TransactionApprovalNotification[]>([]);
  const [thresholds, setThresholds] = useState<TransactionApprovalThreshold[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { user, currentCompanyId } = useAuth();

  // Fetch pending approvals
  useEffect(() => {
    const fetchPendingApprovals = async () => {
      if (!currentCompanyId || !user) return;

      try {
        setIsLoading(true);

        // Fetch transactions that need approval
        const { data, error } = await supabase
          .from('transactions')
          .select(`
            *,
            transaction_items (
              *,
              accounts (
                id,
                name,
                code
              )
            )
          `)
          .eq('company_id', currentCompanyId)
          .eq('status', 'pending')
          .lt('approval_level', 'approval_threshold')
          .order('transaction_date', { ascending: false });

        if (error) throw error;

        // Format the transactions
        const formattedTransactions = data?.map(tx => ({
          ...tx,
          items: tx.transaction_items.map((item: any) => ({
            ...item,
            account: item.accounts
          }))
        })) || [];

        setPendingApprovals(formattedTransactions);
      } catch (err) {
        console.error('Error fetching pending approvals:', err);
        toast({
          title: 'Error',
          description: 'Failed to load pending approvals',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchPendingApprovals();
  }, [currentCompanyId, user, toast]);

  // Fetch approval history
  useEffect(() => {
    const fetchApprovalHistory = async () => {
      if (!currentCompanyId) return;

      try {
        const { data, error } = await supabase
          .from('transaction_approvals')
          .select(`
            *,
            user:approved_by (
              id,
              email,
              user_metadata
            )
          `)
          .order('created_at', { ascending: false })
          .limit(50);

        if (error) throw error;

        setApprovalHistory(data || []);
      } catch (err) {
        console.error('Error fetching approval history:', err);
      }
    };

    fetchApprovalHistory();
  }, [currentCompanyId]);

  // Fetch notifications
  useEffect(() => {
    const fetchNotifications = async () => {
      if (!user) return;

      try {
        const { data, error } = await supabase
          .from('transaction_approval_notifications')
          .select(`
            *,
            transaction:transaction_id (
              description,
              transaction_date
            )
          `)
          .eq('user_id', user.id)
          .eq('is_read', false)
          .order('created_at', { ascending: false });

        if (error) throw error;

        setNotifications(data || []);
      } catch (err) {
        console.error('Error fetching notifications:', err);
      }
    };

    fetchNotifications();
  }, [user]);

  // Fetch approval thresholds
  useEffect(() => {
    const fetchThresholds = async () => {
      if (!currentCompanyId) return;

      try {
        const { data, error } = await supabase
          .from('transaction_approval_thresholds')
          .select('*')
          .eq('company_id', currentCompanyId)
          .order('min_amount', { ascending: true });

        if (error) throw error;

        setThresholds(data || []);
      } catch (err) {
        console.error('Error fetching approval thresholds:', err);
      }
    };

    fetchThresholds();
  }, [currentCompanyId]);

  /**
   * Approve a transaction
   */
  const approveTransaction = async (transactionId: string, notes?: string) => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to approve transactions',
        variant: 'destructive',
      });
      return false;
    }

    try {
      // Create approval record
      const { error: approvalError } = await supabase
        .from('transaction_approvals')
        .insert({
          transaction_id: transactionId,
          approved_by: user.id,
          notes: notes || null
        });

      if (approvalError) throw approvalError;

      // Refresh pending approvals
      setPendingApprovals(prev => prev.filter(tx => tx.id !== transactionId));

      toast({
        title: 'Success',
        description: 'Transaction approved successfully',
      });

      return true;
    } catch (err) {
      console.error('Error approving transaction:', err);
      toast({
        title: 'Error',
        description: 'Failed to approve transaction',
        variant: 'destructive',
      });
      return false;
    }
  };

  /**
   * Reject a transaction
   */
  const rejectTransaction = async (transactionId: string, notes?: string) => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to reject transactions',
        variant: 'destructive',
      });
      return false;
    }

    try {
      // Update transaction status
      const { error: updateError } = await supabase
        .from('transactions')
        .update({
          status: 'rejected'
        })
        .eq('id', transactionId);

      if (updateError) throw updateError;

      // Create rejection record
      const { error: approvalError } = await supabase
        .from('transaction_approvals')
        .insert({
          transaction_id: transactionId,
          approved_by: user.id,
          notes: `REJECTED: ${notes || 'No reason provided'}`
        });

      if (approvalError) throw approvalError;

      // Refresh pending approvals
      setPendingApprovals(prev => prev.filter(tx => tx.id !== transactionId));

      toast({
        title: 'Transaction Rejected',
        description: 'Transaction has been rejected',
      });

      return true;
    } catch (err) {
      console.error('Error rejecting transaction:', err);
      toast({
        title: 'Error',
        description: 'Failed to reject transaction',
        variant: 'destructive',
      });
      return false;
    }
  };

  /**
   * Mark notification as read
   */
  const markNotificationAsRead = async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('transaction_approval_notifications')
        .update({ is_read: true, updated_at: new Date().toISOString() })
        .eq('id', notificationId);

      if (error) throw error;

      // Update local state
      setNotifications(prev => prev.filter(n => n.id !== notificationId));

      return true;
    } catch (err) {
      console.error('Error marking notification as read:', err);
      return false;
    }
  };

  /**
   * Update approval thresholds
   */
  const updateApprovalThreshold = async (
    thresholdId: string,
    data: { min_amount: number; max_amount: number | null; required_approvals: number }
  ) => {
    try {
      const { error } = await supabase
        .from('transaction_approval_thresholds')
        .update({
          min_amount: data.min_amount,
          max_amount: data.max_amount,
          required_approvals: data.required_approvals,
          updated_at: new Date().toISOString()
        })
        .eq('id', thresholdId);

      if (error) throw error;

      // Update local state
      setThresholds(prev =>
        prev.map(t => (t.id === thresholdId ? { ...t, ...data } : t))
      );

      toast({
        title: 'Success',
        description: 'Approval threshold updated successfully',
      });

      return true;
    } catch (err) {
      console.error('Error updating approval threshold:', err);
      toast({
        title: 'Error',
        description: 'Failed to update approval threshold',
        variant: 'destructive',
      });
      return false;
    }
  };

  return {
    pendingApprovals,
    approvalHistory,
    notifications,
    thresholds,
    isLoading,
    approveTransaction,
    rejectTransaction,
    markNotificationAsRead,
    updateApprovalThreshold
  };
}
