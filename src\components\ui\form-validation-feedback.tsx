import React, { useState, useEffect } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { ZodSchema, z } from 'zod';
import { AlertCircle, CheckCircle2, Info } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FormValidationFeedbackProps {
  /**
   * The name of the form field to validate
   */
  name: string;

  /**
   * The Zod schema to validate against
   */
  schema: ZodSchema<any>;

  /**
   * Whether to show validation feedback in real-time
   * @default true
   */
  showRealTime?: boolean;

  /**
   * Whether to show success feedback when validation passes
   * @default true
   */
  showSuccess?: boolean;

  /**
   * Custom class name for the feedback container
   */
  className?: string;

  /**
   * Dependencies for validation (other field values that affect validation)
   */
  dependencies?: string[];
}

/**
 * A component that provides real-time validation feedback for form fields
 * based on Zod schemas.
 */
export function FormValidationFeedback({
  name,
  schema,
  showRealTime = true,
  showSuccess = true,
  className,
  dependencies = [],
}: FormValidationFeedbackProps) {
  const { control, formState } = useFormContext();
  const value = useWatch({ name, control });
  const [validationResult, setValidationResult] = useState<{
    valid: boolean;
    message?: string;
  }>({ valid: true });

  // Watch all dependencies at once with a single useWatch call
  const dependencyValues = useWatch({
    control,
    name: dependencies,
    defaultValue: dependencies.map(() => undefined)
  });

  // Get field error from form state
  const fieldError = formState.errors[name];

  // Validate the field value against the schema
  useEffect(() => {
    if (!showRealTime || value === undefined || value === '') {
      setValidationResult({ valid: true });
      return;
    }

    try {
      // Create an object with the field value and dependencies
      const dataToValidate = { [name]: value };

      // Add dependencies to the validation object
      if (dependencies.length > 0) {
        dependencies.forEach((dep, index) => {
          dataToValidate[dep] = dependencyValues[index];
        });
      }

      // Validate the field
      schema.parse(dataToValidate);
      setValidationResult({ valid: true });
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldError = error.errors.find(err =>
          err.path.includes(name) || err.path.length === 0
        );

        if (fieldError) {
          setValidationResult({
            valid: false,
            message: fieldError.message,
          });
        } else {
          setValidationResult({ valid: true });
        }
      } else {
        setValidationResult({ valid: true });
      }
    }
  }, [value, schema, name, showRealTime, dependencyValues, dependencies]);

  // Don't show anything if the field hasn't been touched and there's no error
  if (!formState.touchedFields[name] && !fieldError && !showRealTime) {
    return null;
  }

  // Show form state error if available
  if (fieldError) {
    return (
      <div className={cn("flex items-center gap-2 text-destructive text-sm mt-1", className)}>
        <AlertCircle className="h-4 w-4" />
        <span>{fieldError.message as string}</span>
      </div>
    );
  }

  // Show real-time validation feedback
  if (showRealTime && value !== undefined && value !== '') {
    if (!validationResult.valid) {
      return (
        <div className={cn("flex items-center gap-2 text-amber-600 text-sm mt-1", className)}>
          <Info className="h-4 w-4" />
          <span>{validationResult.message}</span>
        </div>
      );
    } else if (showSuccess) {
      return (
        <div className={cn("flex items-center gap-2 text-green-600 text-sm mt-1", className)}>
          <CheckCircle2 className="h-4 w-4" />
          <span>Looks good!</span>
        </div>
      );
    }
  }

  return null;
}

interface FormFieldValidationProps {
  /**
   * The name of the form field to validate
   */
  name: string;

  /**
   * The Zod schema to validate against
   */
  schema: ZodSchema<any>;

  /**
   * The form field component to render
   */
  children: React.ReactNode;

  /**
   * Whether to show validation feedback in real-time
   * @default true
   */
  showRealTime?: boolean;

  /**
   * Whether to show success feedback when validation passes
   * @default true
   */
  showSuccess?: boolean;

  /**
   * Dependencies for validation (other field values that affect validation)
   */
  dependencies?: string[];
}

/**
 * A wrapper component that adds real-time validation feedback to a form field
 */
export function FormFieldValidation({
  name,
  schema,
  children,
  showRealTime = true,
  showSuccess = true,
  dependencies = [],
}: FormFieldValidationProps) {
  return (
    <div className="space-y-1">
      {children}
      <FormValidationFeedback
        name={name}
        schema={schema}
        showRealTime={showRealTime}
        showSuccess={showSuccess}
        dependencies={dependencies}
      />
    </div>
  );
}
