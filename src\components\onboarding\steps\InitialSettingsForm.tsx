import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { But<PERSON> } from '@/components/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ArrowRight } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';
import * as z from 'zod';

// Initial settings form schema
const initialSettingsSchema = z.object({
  // Financial settings
  fiscal_year_start: z.enum(['january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december']),
  currency: z.enum(['UGX', 'USD', 'EUR', 'GBP', 'KES', 'TZS', 'RWF']),
  tax_rate: z.coerce.number().min(0).max(100),

  // Notification preferences
  email_notifications: z.boolean().default(true),
  invoice_reminders: z.boolean().default(true),
  payment_notifications: z.boolean().default(true),
});

type InitialSettingsFormValues = z.infer<typeof initialSettingsSchema>;

interface InitialSettingsFormProps {
  /**
   * Callback function called when the form is successfully submitted
   */
  onComplete: (data: InitialSettingsFormValues) => void;

  /**
   * Initial form data (if available)
   */
  initialData?: InitialSettingsFormValues;
}

/**
 * Initial settings form for the onboarding wizard
 */
export function InitialSettingsForm({ onComplete, initialData }: InitialSettingsFormProps) {
  const { toast } = useToast();
  const { currentCompanyId } = useAuth();

  // Initialize form with default values or initial data
  const form = useForm<InitialSettingsFormValues>({
    resolver: zodResolver(initialSettingsSchema),
    defaultValues: initialData || {
      fiscal_year_start: 'january',
      currency: 'UGX',
      tax_rate: 18, // Uganda's standard VAT rate
      email_notifications: true,
      invoice_reminders: true,
      payment_notifications: true,
    },
  });

  // Handle form submission
  const onSubmit = async (data: InitialSettingsFormValues) => {
    if (!currentCompanyId) {
      toast({
        title: 'Error',
        description: 'Company ID is required to save settings',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Calculate fiscal year start date based on selected month
      const currentYear = new Date().getFullYear();
      const monthMap: Record<string, number> = {
        january: 0, february: 1, march: 2, april: 3, may: 4, june: 5,
        july: 6, august: 7, september: 8, october: 9, november: 10, december: 11
      };
      const fiscalYearStartDate = new Date(currentYear, monthMap[data.fiscal_year_start], 1);

      // Update company fiscal_year_start
      const { error: companyError } = await supabase
        .from('companies')
        .update({
          fiscal_year_start: fiscalYearStartDate.toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', currentCompanyId);

      if (companyError) {
        throw companyError;
      }

      // Save company settings in Supabase
      const { error } = await supabase
        .from('company_settings')
        .upsert({
          company_id: currentCompanyId,
          fiscal_year_start: data.fiscal_year_start,
          default_currency: data.currency,
          default_tax_rate: data.tax_rate,
          updated_at: new Date().toISOString(),
        });

      if (error) {
        throw error;
      }

      // Save notification preferences
      const { error: notificationError } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: (await supabase.auth.getUser()).data.user?.id,
          email_notifications: data.email_notifications,
          invoice_reminders: data.invoice_reminders,
          payment_notifications: data.payment_notifications,
          updated_at: new Date().toISOString(),
        });

      if (notificationError) {
        throw notificationError;
      }

      // Call onComplete callback with form data
      onComplete(data);

    } catch (error: any) {
      console.error('Error saving settings:', error);

      toast({
        title: 'Error',
        description: error.message || 'Failed to save settings',
        variant: 'destructive',
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Financial Settings</h3>

          <FormField
            control={form.control}
            name="fiscal_year_start"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Fiscal Year Start</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select fiscal year start" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="january">January (Calendar Year)</SelectItem>
                    <SelectItem value="february">February</SelectItem>
                    <SelectItem value="march">March</SelectItem>
                    <SelectItem value="april">April</SelectItem>
                    <SelectItem value="may">May</SelectItem>
                    <SelectItem value="june">June</SelectItem>
                    <SelectItem value="july">July (Financial Year)</SelectItem>
                    <SelectItem value="august">August</SelectItem>
                    <SelectItem value="september">September</SelectItem>
                    <SelectItem value="october">October</SelectItem>
                    <SelectItem value="november">November</SelectItem>
                    <SelectItem value="december">December</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  When your company's fiscal year begins
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="currency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Default Currency</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select currency" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="UGX">UGX - Ugandan Shilling</SelectItem>
                      <SelectItem value="USD">USD - US Dollar</SelectItem>
                      <SelectItem value="EUR">EUR - Euro</SelectItem>
                      <SelectItem value="GBP">GBP - British Pound</SelectItem>
                      <SelectItem value="KES">KES - Kenyan Shilling</SelectItem>
                      <SelectItem value="TZS">TZS - Tanzanian Shilling</SelectItem>
                      <SelectItem value="RWF">RWF - Rwandan Franc</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tax_rate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Default Tax Rate (%)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      step="0.01"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Standard VAT rate for Uganda is 18%
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Notification Preferences</h3>

          <FormField
            control={form.control}
            name="email_notifications"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                <div className="space-y-0.5">
                  <FormLabel>Email Notifications</FormLabel>
                  <FormDescription>
                    Receive general email notifications
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="invoice_reminders"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                <div className="space-y-0.5">
                  <FormLabel>Invoice Reminders</FormLabel>
                  <FormDescription>
                    Receive reminders about upcoming and overdue invoices
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="payment_notifications"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                <div className="space-y-0.5">
                  <FormLabel>Payment Notifications</FormLabel>
                  <FormDescription>
                    Receive notifications when payments are received
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end pt-4">
          <Button type="submit" className="flex items-center gap-2">
            Complete Setup
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </form>
    </Form>
  );
}
