/**
 * Utility functions for standardized toast notifications
 *
 * These functions provide consistent toast notifications across the application
 * with predefined templates for different types of messages.
 */

import { toast } from '@/components/ui/sonner';

/**
 * Options for toast notifications
 */
interface ToastOptions {
  /**
   * Duration in milliseconds
   * @default 5000
   */
  duration?: number;

  /**
   * Whether the toast is dismissible
   * @default true
   */
  dismissible?: boolean;

  /**
   * Whether the toast is persistent (stays until dismissed)
   * @default false
   */
  persistent?: boolean;

  /**
   * Action label
   */
  actionLabel?: string;

  /**
   * Action callback
   */
  onAction?: () => void;

  /**
   * Cancel label
   */
  cancelLabel?: string;

  /**
   * Cancel callback
   */
  onCancel?: () => void;

  /**
   * Close callback
   */
  onClose?: () => void;

  /**
   * Toast ID for updating or dismissing
   */
  id?: string;
}

/**
 * Show a success toast notification
 *
 * @param title Toast title
 * @param message Toast message
 * @param options Toast options
 */
export const showSuccessToast = (
  title: string,
  message?: string,
  options?: ToastOptions
): string | number => {
  const { persistent, ...restOptions } = options || {};

  return toast.success(title, {
    description: message,
    duration: persistent ? Infinity : options?.duration || 5000,
    ...restOptions,
  });
};

/**
 * Show an error toast notification
 *
 * @param title Toast title
 * @param message Toast message
 * @param options Toast options
 */
export const showErrorToast = (
  title: string,
  message?: string,
  options?: ToastOptions
): string | number => {
  const { persistent, ...restOptions } = options || {};

  return toast.error(title, {
    description: message,
    duration: persistent ? Infinity : options?.duration || 8000, // Longer duration for errors
    ...restOptions,
  });
};

/**
 * Show a warning toast notification
 *
 * @param title Toast title
 * @param message Toast message
 * @param options Toast options
 */
export const showWarningToast = (
  title: string,
  message?: string,
  options?: ToastOptions
): string | number => {
  const { persistent, ...restOptions } = options || {};

  return toast.warning(title, {
    description: message,
    duration: persistent ? Infinity : options?.duration || 7000,
    ...restOptions,
  });
};

/**
 * Show an info toast notification
 *
 * @param title Toast title
 * @param message Toast message
 * @param options Toast options
 */
export const showInfoToast = (
  title: string,
  message?: string,
  options?: ToastOptions
): string | number => {
  const { persistent, ...restOptions } = options || {};

  return toast.info(title, {
    description: message,
    duration: persistent ? Infinity : options?.duration || 5000,
    ...restOptions,
  });
};

/**
 * Show a loading toast notification
 *
 * @param title Toast title
 * @param message Toast message
 * @param options Toast options
 * @returns Toast ID for updating
 */
export const showLoadingToast = (
  title: string,
  message?: string,
  options?: ToastOptions
): string | number => {
  return toast.loading(title, {
    description: message,
    duration: Infinity, // Loading toasts are always persistent
    ...options,
  });
};

/**
 * Update a toast notification
 *
 * @param id Toast ID
 * @param type Toast type
 * @param title Toast title
 * @param message Toast message
 * @param options Toast options
 */
export const updateToast = (
  id: string,
  type: 'success' | 'error' | 'warning' | 'info',
  title: string,
  message?: string,
  options?: ToastOptions
): string | number | undefined => {
  const { persistent, ...restOptions } = options || {};

  toast.dismiss(id);

  switch (type) {
    case 'success':
      return showSuccessToast(title, message, { ...restOptions, id });
    case 'error':
      return showErrorToast(title, message, { ...restOptions, id });
    case 'warning':
      return showWarningToast(title, message, { ...restOptions, id });
    case 'info':
      return showInfoToast(title, message, { ...restOptions, id });
  }
};

/**
 * Show a promise toast notification
 *
 * @param promise Promise to track
 * @param messages Messages for different states
 * @param options Toast options
 */
export const showPromiseToast = <T>(
  promise: Promise<T>,
  messages: {
    loading: string;
    success: string;
    error: string;
  },
  options?: ToastOptions
): string | number => {
  return toast.promise(promise, {
    loading: messages.loading,
    success: messages.success,
    error: messages.error,
    ...options,
  });
};

/**
 * Show a confirmation toast notification
 *
 * @param title Toast title
 * @param message Toast message
 * @param actionLabel Action button label
 * @param onAction Action callback
 * @param options Toast options
 */
export const showConfirmationToast = (
  title: string,
  message: string,
  actionLabel: string,
  onAction: () => void,
  options?: ToastOptions
): string | number => {
  return toast(title, {
    description: message,
    action: {
      label: actionLabel,
      onClick: onAction,
    },
    duration: options?.persistent ? Infinity : options?.duration || 10000,
    ...options,
  });
};

/**
 * Predefined toast templates for common scenarios
 */
export const toastTemplates = {
  /**
   * Show a toast for successful save
   */
  saveSuccess: (entityName: string, options?: ToastOptions): string | number =>
    showSuccessToast(`${entityName} Saved`, `${entityName} has been saved successfully.`, options),

  /**
   * Show a toast for save error
   */
  saveError: (entityName: string, errorMessage?: string, options?: ToastOptions): string | number =>
    showErrorToast(`Failed to Save ${entityName}`, errorMessage || `An error occurred while saving ${entityName.toLowerCase()}.`, options),

  /**
   * Show a toast for successful delete
   */
  deleteSuccess: (entityName: string, options?: ToastOptions): string | number =>
    showSuccessToast(`${entityName} Deleted`, `${entityName} has been deleted successfully.`, options),

  /**
   * Show a toast for delete error
   */
  deleteError: (entityName: string, errorMessage?: string, options?: ToastOptions): string | number =>
    showErrorToast(`Failed to Delete ${entityName}`, errorMessage || `An error occurred while deleting ${entityName.toLowerCase()}.`, options),

  /**
   * Show a toast for successful update
   */
  updateSuccess: (entityName: string, options?: ToastOptions): string | number =>
    showSuccessToast(`${entityName} Updated`, `${entityName} has been updated successfully.`, options),

  /**
   * Show a toast for update error
   */
  updateError: (entityName: string, errorMessage?: string, options?: ToastOptions): string | number =>
    showErrorToast(`Failed to Update ${entityName}`, errorMessage || `An error occurred while updating ${entityName.toLowerCase()}.`, options),

  /**
   * Show a toast for successful creation
   */
  createSuccess: (entityName: string, options?: ToastOptions): string | number =>
    showSuccessToast(`${entityName} Created`, `${entityName} has been created successfully.`, options),

  /**
   * Show a toast for creation error
   */
  createError: (entityName: string, errorMessage?: string, options?: ToastOptions): string | number =>
    showErrorToast(`Failed to Create ${entityName}`, errorMessage || `An error occurred while creating ${entityName.toLowerCase()}.`, options),

  /**
   * Show a toast for session timeout
   */
  sessionTimeout: (options?: ToastOptions): string | number =>
    showWarningToast('Session Timeout', 'Your session has expired. Please sign in again.', { persistent: true, ...options }),

  /**
   * Show a toast for network error
   */
  networkError: (options?: ToastOptions): string | number =>
    showErrorToast('Network Error', 'Unable to connect to the server. Please check your internet connection.', options),

  /**
   * Show a toast for permission denied
   */
  permissionDenied: (options?: ToastOptions): string | number =>
    showErrorToast('Permission Denied', 'You do not have permission to perform this action.', options),
};
