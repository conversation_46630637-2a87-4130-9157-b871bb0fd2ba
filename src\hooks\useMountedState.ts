import { useRef, useEffect } from 'react';

/**
 * Custom hook to track component mounted state
 * Provides a safer alternative to tracking isMounted with useRef
 * 
 * @returns Object with isMounted ref and cleanup function
 */
export function useMountedState() {
  const isMounted = useRef(true);

  useEffect(() => {
    isMounted.current = true;
    
    return () => {
      isMounted.current = false;
    };
  }, []);

  const cleanup = (): void => {
    isMounted.current = false;
  };

  return {
    isMounted,
    cleanup
  };
}

/**
 * Hook that returns a function to check if component is still mounted
 * before performing state updates
 * 
 * @returns Function that returns true if component is mounted
 */
export function useIsMounted(): () => boolean {
  const isMounted = useRef(true);

  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  return (): boolean => isMounted.current;
}
