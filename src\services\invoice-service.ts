import { supabase, createSupabaseQuery } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import {
  InvoiceFormValues,
  invoiceFormToInsert,
  calculateInvoiceSubtotal,
  calculateInvoiceTax,
  calculateInvoiceTotal
} from "@/validations/invoice-schema";
import {
  Invoice,
  InvoiceInsert,
  InvoiceStatus,
  InvoiceWithCustomer,
  Customer
} from "@/types/index";
import { v4 as uuidv4 } from "uuid";
import { format } from "date-fns";
import { PostgrestError } from "@supabase/supabase-js";

export const generateInvoiceNumber = (): string => {
  const year = new Date().getFullYear();
  const randomPart = Math.floor(100000 + Math.random() * 900000);
  return `INV-${year}-${randomPart}`;
};

/**
 * Create a new invoice in the database
 *
 * @param invoiceData Form values from the invoice form
 * @param companyId ID of the company
 * @param customerId ID of the customer
 * @param userId ID of the user creating the invoice
 * @returns The created invoice or null if there was an error
 */
export const createInvoice = async (
  invoiceData: InvoiceFormValues,
  companyId: string,
  customerId: string,
  userId: string
): Promise<Invoice | null> => {
  try {
    // Calculate amounts
    const subtotal = calculateInvoiceSubtotal(invoiceData.items);
    const taxAmount = calculateInvoiceTax(subtotal, invoiceData.taxRate);
    const totalAmount = calculateInvoiceTotal(subtotal, taxAmount);

    // Prepare the invoice data for insertion
    const invoiceInsert: InvoiceInsert = {
      id: uuidv4(),
      invoice_number: invoiceData.invoice_number || generateInvoiceNumber(),
      company_id: companyId,
      customer_id: customerId,
      status: invoiceData.status,
      issue_date: format(invoiceData.issue_date, 'yyyy-MM-dd'),
      due_date: format(invoiceData.due_date, 'yyyy-MM-dd'),
      amount: subtotal,
      tax_amount: taxAmount,
      total_amount: totalAmount,
      notes: invoiceData.notes || null,
      created_by: userId,
      currency: invoiceData.currency,
      exchange_rate: invoiceData.exchange_rate || 1
    };

    // Insert the invoice
    const { data: invoice, error } = await supabase
      .from('invoices')
      .insert(invoiceInsert)
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Create invoice items
    if (invoiceData.items.length > 0) {
      const itemsToInsert = invoiceData.items.map(item => ({
        invoice_id: invoice.id,
        description: item.description,
        quantity: item.quantity,
        unit_price: item.price,
        tax_rate: item.taxable ? invoiceData.taxRate : 0,
        amount: item.quantity * item.price
      }));

      const { error: itemsError } = await supabase
        .from('invoice_items')
        .insert(itemsToInsert);

      if (itemsError) {
        throw itemsError;
      }
    }

    toast({
      title: "Invoice created",
      description: `Invoice ${invoice.invoice_number} has been created successfully.`,
    });

    return invoice;
  } catch (error: unknown) {
    console.error("Error creating invoice:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to create invoice. Please try again.";
    toast({
      title: "Error creating invoice",
      description: errorMessage,
      variant: "destructive",
    });
    return null;
  }
};

/**
 * Update the status of an invoice
 *
 * @param invoiceId ID of the invoice to update
 * @param newStatus New status for the invoice
 * @returns True if the update was successful, false otherwise
 */
export const updateInvoiceStatus = async (
  invoiceId: string,
  newStatus: InvoiceStatus
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('invoices')
      .update({ status: newStatus, updated_at: new Date().toISOString() })
      .eq('id', invoiceId);

    if (error) throw error;

    toast({
      title: "Invoice updated",
      description: `Invoice status has been changed to ${newStatus}.`,
    });

    return true;
  } catch (error: unknown) {
    console.error("Error updating invoice status:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to update invoice. Please try again.";
    toast({
      title: "Error updating invoice",
      description: errorMessage,
      variant: "destructive",
    });
    return false;
  }
};

/**
 * Get an invoice by ID with related customer and items
 *
 * @param sb Supabase client
 * @param id Invoice ID
 * @returns The invoice with related data
 */
export const getInvoiceById = createSupabaseQuery<Invoice & {
  customers: Pick<Customer, 'id' | 'name' | 'email' | 'phone'>,
  invoice_items: Array<{
    id: string;
    description: string;
    quantity: number;
    unit_price: number;
    tax_rate: number;
    amount: number;
  }>
}>(async (sb, id: string) => {
  return await sb
    .from('invoices')
    .select(`
      *,
      customers (
        id, name, email, phone
      ),
      invoice_items (
        id, description, quantity, unit_price, tax_rate, amount
      )
    `)
    .eq('id', id)
    .single();
});

/**
 * Get all invoices for the current company with basic customer info
 *
 * @param sb Supabase client
 * @returns List of invoices with customer information
 */
export const getInvoicesByCompany = createSupabaseQuery<Array<InvoiceWithCustomer>>(async (sb) => {
  const { data, error } = await sb
    .from('invoices')
    .select(`
      *,
      customers (
        name, email
      )
    `)
    .order('issue_date', { ascending: false });

  return { data, error };
});
