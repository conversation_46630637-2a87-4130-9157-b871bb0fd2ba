
type PriorityIndicatorProps = {
  priority: string;
};

export const PriorityIndicator = ({ priority }: PriorityIndicatorProps) => {
  const getPriorityStyles = () => {
    switch (priority) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-amber-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <span className="flex items-center gap-2">
      <span className={`h-2.5 w-2.5 rounded-full ${getPriorityStyles()}`}></span>
      {priority.charAt(0).toUpperCase() + priority.slice(1)}
    </span>
  );
};
