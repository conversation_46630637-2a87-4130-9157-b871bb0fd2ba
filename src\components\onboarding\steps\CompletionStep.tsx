import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface CompletionStepProps {
  companyData: any;
  userData: any;
  settingsData: any;
}

/**
 * Completion step for the onboarding wizard
 * 
 * Displays a summary of all the information entered during the onboarding process
 * and provides a button to proceed to the dashboard.
 */
export function CompletionStep({ companyData, userData, settingsData }: CompletionStepProps) {
  const navigate = useNavigate();

  const handleGoToDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <div className="flex justify-center mb-4">
          <CheckCircle2 className="h-16 w-16 text-green-500" />
        </div>
        <h2 className="text-2xl font-bold">Setup Complete!</h2>
        <p className="text-muted-foreground mt-2">
          Your company has been successfully set up. You can now start using Kaya Finance.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <h3 className="font-semibold text-lg mb-4">Company Information</h3>
            <dl className="space-y-2">
              <div>
                <dt className="text-sm text-muted-foreground">Company Name</dt>
                <dd className="font-medium">{companyData?.name}</dd>
              </div>
              {companyData?.tax_id && (
                <div>
                  <dt className="text-sm text-muted-foreground">Tax ID</dt>
                  <dd>{companyData.tax_id}</dd>
                </div>
              )}
              {companyData?.address && (
                <div>
                  <dt className="text-sm text-muted-foreground">Address</dt>
                  <dd>{companyData.address}</dd>
                </div>
              )}
              <div>
                <dt className="text-sm text-muted-foreground">Country</dt>
                <dd>{companyData?.country || 'Uganda'}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h3 className="font-semibold text-lg mb-4">Admin User</h3>
            <dl className="space-y-2">
              <div>
                <dt className="text-sm text-muted-foreground">Name</dt>
                <dd className="font-medium">
                  {userData?.first_name} {userData?.last_name}
                </dd>
              </div>
              <div>
                <dt className="text-sm text-muted-foreground">Email</dt>
                <dd>{userData?.email}</dd>
              </div>
              {userData?.position && (
                <div>
                  <dt className="text-sm text-muted-foreground">Position</dt>
                  <dd>{userData.position}</dd>
                </div>
              )}
              {userData?.phone && (
                <div>
                  <dt className="text-sm text-muted-foreground">Phone</dt>
                  <dd>{userData.phone}</dd>
                </div>
              )}
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h3 className="font-semibold text-lg mb-4">Company Settings</h3>
            <dl className="space-y-2">
              <div>
                <dt className="text-sm text-muted-foreground">Fiscal Year Start</dt>
                <dd className="capitalize">
                  {settingsData?.fiscal_year_start === 'january' 
                    ? 'January (Calendar Year)' 
                    : 'July (Financial Year)'}
                </dd>
              </div>
              <div>
                <dt className="text-sm text-muted-foreground">Default Currency</dt>
                <dd>{settingsData?.currency}</dd>
              </div>
              <div>
                <dt className="text-sm text-muted-foreground">Default Tax Rate</dt>
                <dd>{settingsData?.tax_rate}%</dd>
              </div>
              <div>
                <dt className="text-sm text-muted-foreground">Notifications</dt>
                <dd>
                  {settingsData?.email_notifications ? 'Email notifications enabled' : 'Email notifications disabled'}
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-center mt-8">
        <Button size="lg" onClick={handleGoToDashboard}>
          Go to Dashboard
        </Button>
      </div>
    </div>
  );
}
