
import { useState } from 'react';
import { useReports } from '@/hooks/use-reports';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { FinancialReportsTab } from '@/components/reports/financial-reports-tab';
import { TaxReportsTab } from '@/components/reports/tax-reports-tab';
import { ExportReportsButton } from '@/components/reports/ExportReportsButton';

const Reports = (): React.JSX.Element => {
  const {
    dateRange,
    startDate,
    endDate,
    reportYear,
    reportQuarter,
    setDateRange,
    setStartDate,
    setEndDate,
    setReportYear,
    setReportQuarter,

    balanceSheet,
    isLoadingBalanceSheet,

    profitAndLoss,
    isLoadingProfitAndLoss,

    cashFlow,
    isLoadingCashFlow,

    taxReport,
    isLoadingTaxReport,

    formatCurrency,
  } = useReports();

  const [activeTab, setActiveTab] = useState('financial');

  return (
    <div>
      {/* Page header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="page-title mb-1">Financial Reports</h1>
          <p className="text-muted-foreground">
            Generate and analyze financial statements
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          <ExportReportsButton
            balanceSheet={balanceSheet}
            profitAndLoss={profitAndLoss}
            cashFlow={cashFlow}
            taxReport={taxReport}
            startDate={startDate}
            endDate={endDate}
            isLoading={isLoadingBalanceSheet || isLoadingProfitAndLoss || isLoadingCashFlow || isLoadingTaxReport}
          />
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-8">
          <TabsTrigger value="financial">Financial Reports</TabsTrigger>
          <TabsTrigger value="tax">Tax Reports</TabsTrigger>
        </TabsList>

        {/* Financial Reports Tab */}
        <TabsContent value="financial" className="space-y-8">
          <FinancialReportsTab
            dateRange={dateRange}
            startDate={startDate}
            endDate={endDate}
            setDateRange={setDateRange}
            setStartDate={setStartDate}
            setEndDate={setEndDate}
            balanceSheet={balanceSheet}
            isLoadingBalanceSheet={isLoadingBalanceSheet}
            profitAndLoss={profitAndLoss}
            isLoadingProfitAndLoss={isLoadingProfitAndLoss}
            cashFlow={cashFlow}
            isLoadingCashFlow={isLoadingCashFlow}
            formatCurrency={formatCurrency}
          />
        </TabsContent>

        {/* Tax Reports Tab */}
        <TabsContent value="tax">
          <TaxReportsTab
            reportYear={reportYear}
            reportQuarter={reportQuarter}
            dateRange={dateRange}
            taxReport={taxReport}
            isLoadingTaxReport={isLoadingTaxReport}
            formatCurrency={formatCurrency}
            setReportYear={setReportYear}
            setReportQuarter={setReportQuarter}
            setDateRange={setDateRange}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Reports;
