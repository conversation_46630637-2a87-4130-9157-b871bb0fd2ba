
import React, { useState, useRef } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from '@/hooks/use-toast';
import { Upload } from 'lucide-react';

interface ImportTransactionsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImportComplete: (data: any[]) => void;
}

const ImportTransactionsDialog: React.FC<ImportTransactionsDialogProps> = ({
  open,
  onOpenChange,
  onImportComplete,
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [previewData, setPreviewData] = useState<any[] | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);
      setError(null);

      // Validate file type
      if (!selectedFile.name.endsWith('.csv') && !selectedFile.name.endsWith('.xlsx')) {
        setError('Please upload a CSV or Excel file.');
        return;
      }

      // Preview the file
      parseFile(selectedFile);
    }
  };

  const parseFile = (file: File) => {
    setIsLoading(true);
    setError(null);

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        if (event.target?.result) {
          // Parse the file content
          const content = event.target.result as string;
          const lines = content.split('\n');

          // Skip header row and process up to 3 rows for preview
          const previewData = [];

          // Simple CSV parsing (this should be replaced with a proper CSV parser)
          for (let i = 1; i < Math.min(lines.length, 4); i++) {
            const line = lines[i].trim();
            if (!line) continue;

            const columns = line.split(',');
            if (columns.length >= 6) {
              previewData.push({
                id: columns[0] || `T${1000 + i}`,
                date: columns[1] || new Date().toISOString().split('T')[0],
                description: columns[2] || 'Transaction',
                debit: columns[3] || 'Unknown Account',
                credit: columns[4] || 'Unknown Account',
                amount: parseFloat(columns[5]) || 0
              });
            }
          }

          if (previewData.length > 0) {
            setPreviewData(previewData);
          } else {
            setError('No valid data found in file. Please check the format.');
          }
        }
      } catch (err) {
        setError('Failed to parse file. Please check the file format.');
      } finally {
        setIsLoading(false);
      }
    };

    reader.onerror = () => {
      setError('Error reading file.');
      setIsLoading(false);
    };

    reader.readAsText(file);
  };

  const handleImport = () => {
    if (!previewData) return;

    setIsLoading(true);

    // Simulate import process
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "Import Successful",
        description: `${previewData.length} transactions have been imported.`,
      });
      onImportComplete(previewData);
      onOpenChange(false);
      // Reset the form state
      setFile(null);
      setPreviewData(null);
      if (fileInputRef.current) fileInputRef.current.value = '';
    }, 1000);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Import Transactions</DialogTitle>
          <DialogDescription>
            Upload a CSV or Excel file containing transaction data to import into the general ledger.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="border-2 border-dashed border-gray-300 rounded-md p-8 mb-4 text-center">
            <Input
              ref={fileInputRef}
              type="file"
              accept=".csv,.xlsx,.xls"
              onChange={handleFileChange}
              className="hidden"
              id="file-upload"
            />
            <label
              htmlFor="file-upload"
              className="flex flex-col items-center justify-center cursor-pointer"
            >
              <Upload className="h-10 w-10 text-gray-400 mb-2" />
              <span className="text-sm font-medium">
                Click to upload or drag and drop
              </span>
              <span className="text-xs text-gray-500 mt-1">
                CSV or Excel files supported
              </span>
              {file && (
                <span className="mt-2 text-sm font-medium text-blue-600">
                  {file.name}
                </span>
              )}
            </label>
          </div>

          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {previewData && (
            <div className="mt-4">
              <h4 className="text-sm font-medium mb-2">Preview (First 3 rows)</h4>
              <div className="border rounded-md overflow-x-auto">
                <table className="w-full text-xs">
                  <thead>
                    <tr className="bg-gray-50 border-b">
                      <th className="px-3 py-2 text-left">ID</th>
                      <th className="px-3 py-2 text-left">Date</th>
                      <th className="px-3 py-2 text-left">Description</th>
                      <th className="px-3 py-2 text-left">Debit</th>
                      <th className="px-3 py-2 text-left">Credit</th>
                      <th className="px-3 py-2 text-right">Amount (UGX)</th>
                    </tr>
                  </thead>
                  <tbody>
                    {previewData.map((row, index) => (
                      <tr key={index} className="border-b last:border-b-0">
                        <td className="px-3 py-2">{row.id}</td>
                        <td className="px-3 py-2">{row.date}</td>
                        <td className="px-3 py-2">{row.description}</td>
                        <td className="px-3 py-2">{row.debit}</td>
                        <td className="px-3 py-2">{row.credit}</td>
                        <td className="px-3 py-2 text-right">{row.amount.toLocaleString()}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Showing 3 of {previewData.length} transactions
              </p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button
            onClick={handleImport}
            disabled={!previewData || isLoading}
          >
            {isLoading ? 'Importing...' : 'Import Data'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ImportTransactionsDialog;
