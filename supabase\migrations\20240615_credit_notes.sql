-- Credit Notes and Aging Reports Migration
-- This migration adds support for credit notes and aging reports

-- Step 1: Add 'credited' to invoice_status enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'invoice_status' AND
                  'credited' = ANY(enum_range(NULL::invoice_status)::text[])) THEN
        ALTER TYPE invoice_status ADD VALUE 'credited' AFTER 'cancelled';
    END IF;
END$$;

-- Step 2: Create credit_notes table
CREATE TABLE IF NOT EXISTS credit_notes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES customers(id),
  invoice_id UUID REFERENCES invoices(id),
  credit_note_number TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'draft', -- draft, issued, approved, rejected
  issue_date DATE NOT NULL,
  currency TEXT NOT NULL DEFAULT 'UGX',
  exchange_rate DECIMAL(15, 6) DEFAULT 1.0,
  amount DECIMAL(15, 2) NOT NULL,
  tax_amount DECIMAL(15, 2) NOT NULL DEFAULT 0,
  total_amount DECIMAL(15, 2) NOT NULL,
  reason TEXT,
  notes TEXT,
  created_by UUID NOT NULL REFERENCES auth.users(id),
  approved_by UUID REFERENCES auth.users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(company_id, credit_note_number)
);

-- Create indexes for credit_notes
CREATE INDEX idx_credit_notes_company_customer ON credit_notes(company_id, customer_id);
CREATE INDEX idx_credit_notes_invoice ON credit_notes(invoice_id);
CREATE INDEX idx_credit_notes_status ON credit_notes(status);

-- Step 3: Create credit_note_items table
CREATE TABLE IF NOT EXISTS credit_note_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  credit_note_id UUID NOT NULL REFERENCES credit_notes(id) ON DELETE CASCADE,
  invoice_item_id UUID REFERENCES invoice_items(id),
  description TEXT NOT NULL,
  quantity DECIMAL(15, 2) NOT NULL,
  unit_price DECIMAL(15, 2) NOT NULL,
  tax_rate DECIMAL(5, 2) NOT NULL DEFAULT 0,
  line_total DECIMAL(15, 2) GENERATED ALWAYS AS (quantity * unit_price) STORED,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for credit_note_items
CREATE INDEX idx_credit_note_items_credit_note ON credit_note_items(credit_note_id);

-- Step 4: Create credit_note_approval_history table
CREATE TABLE IF NOT EXISTS credit_note_approval_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  credit_note_id UUID NOT NULL REFERENCES credit_notes(id) ON DELETE CASCADE,
  from_status TEXT NOT NULL,
  to_status TEXT NOT NULL,
  changed_by UUID NOT NULL REFERENCES auth.users(id),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 5: Create function for aging reports
CREATE OR REPLACE FUNCTION get_invoice_aging(
  p_company_id UUID,
  p_as_of_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
  customer_id UUID,
  customer_name TEXT,
  invoice_id UUID,
  invoice_number TEXT,
  issue_date DATE,
  due_date DATE,
  total_amount DECIMAL(15, 2),
  paid_amount DECIMAL(15, 2),
  balance DECIMAL(15, 2),
  currency TEXT,
  days_overdue INTEGER,
  aging_bucket TEXT
) AS $$
BEGIN
  RETURN QUERY
  WITH invoice_payments AS (
    SELECT
      ip.invoice_id,
      COALESCE(SUM(ip.amount), 0) as paid_amount
    FROM invoice_payments ip
    JOIN invoices i ON ip.invoice_id = i.id
    WHERE i.company_id = p_company_id
    GROUP BY ip.invoice_id
  )
  SELECT
    i.customer_id,
    c.name as customer_name,
    i.id as invoice_id,
    i.invoice_number,
    i.issue_date,
    i.due_date,
    i.total_amount,
    COALESCE(ip.paid_amount, 0) as paid_amount,
    (i.total_amount - COALESCE(ip.paid_amount, 0)) as balance,
    i.currency,
    CASE
      WHEN i.status = 'paid' THEN 0
      ELSE GREATEST(p_as_of_date - i.due_date, 0)
    END as days_overdue,
    CASE
      WHEN i.status = 'paid' THEN 'Paid'
      WHEN p_as_of_date <= i.due_date THEN 'Current'
      WHEN p_as_of_date - i.due_date BETWEEN 1 AND 30 THEN '1-30 days'
      WHEN p_as_of_date - i.due_date BETWEEN 31 AND 60 THEN '31-60 days'
      WHEN p_as_of_date - i.due_date BETWEEN 61 AND 90 THEN '61-90 days'
      ELSE 'Over 90 days'
    END as aging_bucket
  FROM invoices i
  JOIN customers c ON i.customer_id = c.id
  LEFT JOIN invoice_payments ip ON i.id = ip.invoice_id
  WHERE i.company_id = p_company_id
    AND i.status != 'draft'
    AND i.status != 'cancelled'
    AND (i.total_amount - COALESCE(ip.paid_amount, 0)) > 0;
END;
$$ LANGUAGE plpgsql;

-- Step 6: Create RLS policies for credit notes
ALTER TABLE credit_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE credit_note_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE credit_note_approval_history ENABLE ROW LEVEL SECURITY;

-- Company users can view their own credit notes
CREATE POLICY credit_notes_select_policy ON credit_notes
  FOR SELECT USING (
    company_id IN (
      SELECT company_id FROM user_companies WHERE user_id = auth.uid()
    )
  );

-- Only admins and accountants can create credit notes
CREATE POLICY credit_notes_insert_policy ON credit_notes
  FOR INSERT WITH CHECK (
    company_id IN (
      SELECT company_id FROM user_companies
      WHERE user_id = auth.uid()
      AND role IN ('admin', 'accountant')
    )
  );

-- Only admins and accountants can update credit notes
CREATE POLICY credit_notes_update_policy ON credit_notes
  FOR UPDATE USING (
    company_id IN (
      SELECT company_id FROM user_companies
      WHERE user_id = auth.uid()
      AND role IN ('admin', 'accountant')
    )
  );

-- Similar policies for credit note items
CREATE POLICY credit_note_items_select_policy ON credit_note_items
  FOR SELECT USING (
    company_id IN (
      SELECT company_id FROM user_companies WHERE user_id = auth.uid()
    )
  );

CREATE POLICY credit_note_items_insert_policy ON credit_note_items
  FOR INSERT WITH CHECK (
    company_id IN (
      SELECT company_id FROM user_companies
      WHERE user_id = auth.uid()
      AND role IN ('admin', 'accountant')
    )
  );
