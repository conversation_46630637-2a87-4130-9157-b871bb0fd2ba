-- Migration to add recurring transactions functionality

-- Create frequency type enum
CREATE TYPE recurrence_frequency AS ENUM ('daily', 'weekly', 'monthly', 'quarterly', 'yearly');

-- Create recurring transactions table
CREATE TABLE IF NOT EXISTS recurring_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  frequency recurrence_frequency NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE,
  next_due_date DATE NOT NULL,
  last_generated_date DATE,
  day_of_month INTEGER CHECK (day_of_month BETWEEN 1 AND 31),
  day_of_week INTEGER CHECK (day_of_week BETWEEN 0 AND 6),
  status TEXT NOT NULL DEFAULT 'active',
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create recurring transaction items table
CREATE TABLE IF NOT EXISTS recurring_transaction_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  recurring_transaction_id UUID NOT NULL REFERENCES recurring_transactions(id) ON DELETE CASCADE,
  account_id UUID NOT NULL REFERENCES accounts(id),
  description TEXT,
  debit DECIMAL(15, 2) DEFAULT 0,
  credit DECIMAL(15, 2) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for efficient querying
CREATE INDEX idx_recurring_transactions_company_next_due ON recurring_transactions(company_id, next_due_date);
CREATE INDEX idx_recurring_transactions_status ON recurring_transactions(status);

-- Add transaction_source field to transactions table to track origin
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS transaction_source TEXT,
ADD COLUMN IF NOT EXISTS recurring_transaction_id UUID REFERENCES recurring_transactions(id);

-- Create function to generate transactions from recurring templates
CREATE OR REPLACE FUNCTION generate_recurring_transactions()
RETURNS INTEGER AS $$
DECLARE
  rec RECORD;
  new_transaction_id UUID;
  item RECORD;
  transactions_created INTEGER := 0;
BEGIN
  -- Find all active recurring transactions due today or earlier
  FOR rec IN 
    SELECT * FROM recurring_transactions 
    WHERE status = 'active' 
    AND next_due_date <= CURRENT_DATE
    AND (end_date IS NULL OR end_date >= CURRENT_DATE)
  LOOP
    -- Create a new transaction
    INSERT INTO transactions (
      company_id, 
      transaction_date, 
      description, 
      created_by, 
      transaction_source,
      recurring_transaction_id
    ) VALUES (
      rec.company_id,
      rec.next_due_date,
      rec.description,
      rec.created_by,
      'recurring',
      rec.id
    ) RETURNING id INTO new_transaction_id;
    
    -- Copy all items from the recurring template
    FOR item IN 
      SELECT * FROM recurring_transaction_items 
      WHERE recurring_transaction_id = rec.id
    LOOP
      INSERT INTO transaction_items (
        transaction_id,
        account_id,
        description,
        debit,
        credit
      ) VALUES (
        new_transaction_id,
        item.account_id,
        item.description,
        item.debit,
        item.credit
      );
    END LOOP;
    
    -- Update the recurring transaction with new next_due_date
    UPDATE recurring_transactions 
    SET 
      last_generated_date = rec.next_due_date,
      next_due_date = CASE 
        WHEN rec.frequency = 'daily' THEN rec.next_due_date + INTERVAL '1 day'
        WHEN rec.frequency = 'weekly' THEN rec.next_due_date + INTERVAL '1 week'
        WHEN rec.frequency = 'monthly' THEN 
          -- Handle month end correctly
          CASE 
            WHEN rec.day_of_month IS NOT NULL THEN 
              -- Try to use the specified day of month, but adjust for month length
              (DATE_TRUNC('month', rec.next_due_date) + INTERVAL '1 month' + 
               ((LEAST(rec.day_of_month, 
                      EXTRACT(DAY FROM 
                        (DATE_TRUNC('month', rec.next_due_date) + INTERVAL '1 month + 1 month - 1 day')
                      )::INTEGER
                     ) - 1) || ' days')::DATE
            ELSE rec.next_due_date + INTERVAL '1 month'
          END
        WHEN rec.frequency = 'quarterly' THEN rec.next_due_date + INTERVAL '3 months'
        WHEN rec.frequency = 'yearly' THEN rec.next_due_date + INTERVAL '1 year'
      END
    WHERE id = rec.id;
    
    transactions_created := transactions_created + 1;
  END LOOP;
  
  RETURN transactions_created;
END;
$$ LANGUAGE plpgsql;

-- Create a cron job to run the function daily (requires pg_cron extension)
-- Note: This would typically be set up by a database administrator
-- CREATE EXTENSION IF NOT EXISTS pg_cron;
-- SELECT cron.schedule('0 1 * * *', 'SELECT generate_recurring_transactions()');

COMMENT ON TABLE recurring_transactions IS 'Stores templates for transactions that repeat on a schedule';
COMMENT ON TABLE recurring_transaction_items IS 'Stores the line items for recurring transaction templates';
COMMENT ON FUNCTION generate_recurring_transactions() IS 'Generates actual transactions from recurring templates that are due';
