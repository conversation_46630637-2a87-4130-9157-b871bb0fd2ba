import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { But<PERSON> } from '@/components/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowRight } from 'lucide-react';
import { userProfileSchema } from '@/validations/user-profile-schema';
import { FormValidationFeedback } from '@/components/ui/form-validation-feedback';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';
import * as z from 'zod';

// Admin user form schema
const adminUserSchema = z.object({
  first_name: userProfileSchema.shape.first_name,
  last_name: userProfileSchema.shape.last_name,
  email: userProfileSchema.shape.email,
  phone: userProfileSchema.shape.phone,
  position: userProfileSchema.shape.position,
});

type AdminUserFormValues = z.infer<typeof adminUserSchema>;

interface AdminUserFormProps {
  /**
   * Callback function called when the form is successfully submitted
   */
  onComplete: (data: AdminUserFormValues) => void;
  
  /**
   * Initial form data (if available)
   */
  initialData?: AdminUserFormValues;
}

/**
 * Admin user form for the onboarding wizard
 */
export function AdminUserForm({ onComplete, initialData }: AdminUserFormProps) {
  const { toast } = useToast();
  const { user, updateProfile } = useAuth();
  
  // Initialize form with default values or initial data
  const form = useForm<AdminUserFormValues>({
    resolver: zodResolver(adminUserSchema),
    defaultValues: initialData || {
      first_name: '',
      last_name: '',
      email: user?.email || '',
      phone: '',
      position: 'CEO',
    },
  });
  
  // Handle form submission
  const onSubmit = async (data: AdminUserFormValues) => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to update your profile',
        variant: 'destructive',
      });
      return;
    }
    
    try {
      // Update user profile in Supabase
      const { error } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          first_name: data.first_name,
          last_name: data.last_name,
          email: data.email,
          phone: data.phone || null,
          position: data.position || null,
          updated_at: new Date().toISOString(),
        });
      
      if (error) {
        throw error;
      }
      
      // Update auth context
      await updateProfile({
        first_name: data.first_name,
        last_name: data.last_name,
        phone: data.phone,
        position: data.position,
      });
      
      // Call onComplete callback with form data
      onComplete(data);
      
    } catch (error: any) {
      console.error('Error updating profile:', error);
      
      toast({
        title: 'Error',
        description: error.message || 'Failed to update profile',
        variant: 'destructive',
      });
    }
  };
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="first_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>First Name *</FormLabel>
                <FormControl>
                  <Input placeholder="Enter first name" {...field} />
                </FormControl>
                <FormMessage />
                <FormValidationFeedback name="first_name" schema={adminUserSchema} />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="last_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Last Name *</FormLabel>
                <FormControl>
                  <Input placeholder="Enter last name" {...field} />
                </FormControl>
                <FormMessage />
                <FormValidationFeedback name="last_name" schema={adminUserSchema} />
              </FormItem>
            )}
          />
        </div>
        
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email *</FormLabel>
              <FormControl>
                <Input 
                  placeholder="Enter email address" 
                  {...field} 
                  disabled={!!user?.email}
                />
              </FormControl>
              <FormDescription>
                {user?.email ? "Email cannot be changed" : "Your email address for communications"}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input placeholder="+256 700 123456" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="position"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Position</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select position" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="CEO">CEO</SelectItem>
                    <SelectItem value="CFO">CFO</SelectItem>
                    <SelectItem value="Finance Manager">Finance Manager</SelectItem>
                    <SelectItem value="Accountant">Accountant</SelectItem>
                    <SelectItem value="Business Owner">Business Owner</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="flex justify-end pt-4">
          <Button type="submit" className="flex items-center gap-2">
            Continue
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </form>
    </Form>
  );
}
