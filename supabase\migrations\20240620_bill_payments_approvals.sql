-- Migration to add bill payments and approval workflow
-- This migration adds support for bill payments, approval workflow, and related functionality

-- Step 1: Create bill_payments table
CREATE TABLE IF NOT EXISTS bill_payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  bill_id UUID NOT NULL REFERENCES bills(id) ON DELETE CASCADE,
  amount DECIMAL(15, 2) NOT NULL,
  payment_date DATE NOT NULL,
  payment_method TEXT NOT NULL,
  reference TEXT,
  notes TEXT,
  recorded_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for efficient querying
CREATE INDEX idx_bill_payments_bill ON bill_payments(bill_id);

-- Step 2: Create bill_approval_history table
CREATE TABLE IF NOT EXISTS bill_approval_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  bill_id UUID NOT NULL REFERENCES bills(id) ON DELETE CASCADE,
  from_status TEXT NOT NULL,
  to_status TEXT NOT NULL,
  changed_by UUID NOT NULL REFERENCES auth.users(id),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for efficient querying
CREATE INDEX idx_bill_approval_history_bill ON bill_approval_history(bill_id);

-- Step 3: Create bill_approval_thresholds table
CREATE TABLE IF NOT EXISTS bill_approval_thresholds (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  min_amount DECIMAL(15, 2) NOT NULL,
  max_amount DECIMAL(15, 2),
  required_approvals INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT check_bill_min_max CHECK (min_amount <= max_amount OR max_amount IS NULL)
);

-- Create index for efficient querying
CREATE INDEX idx_bill_approval_thresholds_company ON bill_approval_thresholds(company_id);

-- Step 4: Insert default approval thresholds for each company
INSERT INTO bill_approval_thresholds (company_id, min_amount, max_amount, required_approvals)
SELECT 
  id as company_id, 
  0 as min_amount, 
  1000000 as max_amount, 
  1 as required_approvals
FROM companies
WHERE NOT EXISTS (
  SELECT 1 FROM bill_approval_thresholds 
  WHERE company_id = companies.id AND min_amount = 0
);

INSERT INTO bill_approval_thresholds (company_id, min_amount, max_amount, required_approvals)
SELECT 
  id as company_id, 
  1000000 as min_amount, 
  10000000 as max_amount, 
  2 as required_approvals
FROM companies
WHERE NOT EXISTS (
  SELECT 1 FROM bill_approval_thresholds 
  WHERE company_id = companies.id AND min_amount = 1000000
);

INSERT INTO bill_approval_thresholds (company_id, min_amount, max_amount, required_approvals)
SELECT 
  id as company_id, 
  10000000 as min_amount, 
  NULL as max_amount, 
  3 as required_approvals
FROM companies
WHERE NOT EXISTS (
  SELECT 1 FROM bill_approval_thresholds 
  WHERE company_id = companies.id AND min_amount = 10000000
);

-- Step 5: Create function to get required approvals for a bill
CREATE OR REPLACE FUNCTION get_bill_required_approvals(p_company_id UUID, p_amount DECIMAL)
RETURNS INTEGER AS $$
DECLARE
  v_required_approvals INTEGER;
BEGIN
  SELECT required_approvals INTO v_required_approvals
  FROM bill_approval_thresholds
  WHERE company_id = p_company_id
    AND p_amount >= min_amount
    AND (p_amount < max_amount OR max_amount IS NULL)
  ORDER BY min_amount DESC
  LIMIT 1;
  
  RETURN COALESCE(v_required_approvals, 1);
END;
$$ LANGUAGE plpgsql;

-- Step 6: Create function to calculate total paid amount for a bill
CREATE OR REPLACE FUNCTION get_bill_paid_amount(p_bill_id UUID)
RETURNS DECIMAL AS $$
DECLARE
  v_paid_amount DECIMAL;
BEGIN
  SELECT COALESCE(SUM(amount), 0) INTO v_paid_amount
  FROM bill_payments
  WHERE bill_id = p_bill_id;
  
  RETURN v_paid_amount;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Enable RLS for new tables
ALTER TABLE bill_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE bill_approval_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE bill_approval_thresholds ENABLE ROW LEVEL SECURITY;

-- Company users can view their own bill payments
CREATE POLICY bill_payments_select_policy ON bill_payments
  FOR SELECT USING (
    bill_id IN (
      SELECT id FROM bills WHERE company_id IN (
        SELECT company_id FROM user_companies WHERE user_id = auth.uid()
      )
    )
  );

-- Company users can insert their own bill payments
CREATE POLICY bill_payments_insert_policy ON bill_payments
  FOR INSERT WITH CHECK (
    bill_id IN (
      SELECT id FROM bills WHERE company_id IN (
        SELECT company_id FROM user_companies WHERE user_id = auth.uid()
      )
    )
  );

-- Company users can view their own bill approval history
CREATE POLICY bill_approval_history_select_policy ON bill_approval_history
  FOR SELECT USING (
    bill_id IN (
      SELECT id FROM bills WHERE company_id IN (
        SELECT company_id FROM user_companies WHERE user_id = auth.uid()
      )
    )
  );

-- Company users can insert their own bill approval history
CREATE POLICY bill_approval_history_insert_policy ON bill_approval_history
  FOR INSERT WITH CHECK (
    bill_id IN (
      SELECT id FROM bills WHERE company_id IN (
        SELECT company_id FROM user_companies WHERE user_id = auth.uid()
      )
    )
  );

-- Company users can view their own bill approval thresholds
CREATE POLICY bill_approval_thresholds_select_policy ON bill_approval_thresholds
  FOR SELECT USING (
    company_id IN (
      SELECT company_id FROM user_companies WHERE user_id = auth.uid()
    )
  );

-- Only admins can manage bill approval thresholds
CREATE POLICY bill_approval_thresholds_all_policy ON bill_approval_thresholds
  USING (
    company_id IN (
      SELECT company_id FROM user_companies 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );
