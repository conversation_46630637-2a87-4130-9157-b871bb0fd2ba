
import { supabase } from '@/integrations/supabase/client';
import { UserRole } from '@/types/index';
import { useToast } from '@/hooks/use-toast';

export function useRoleManagement() {
  const { toast } = useToast();

  // Fetch user roles
  const fetchUserRoles = async (userId: string) => {
    try {
      console.log("Fetching roles for user:", userId);
      // Get user roles from user_companies table
      const { data, error } = await supabase
        .from('user_companies')
        .select('role')
        .eq('user_id', userId);

      if (error) {
        console.error('Error fetching user roles:', error);
        console.log("Returning default viewer role due to error");
        // Return default role instead of empty array for better UX
        return ["viewer"] as UserRole[];
      }

      if (data.length === 0) {
        console.log("No roles found for user, assigning default viewer role");
        // If no roles found, return default viewer role

        // Try to create a default role for the user
        try {
          console.log("Attempting to create default role for user");
          // Get the first company
          const { data: company, error: companyError } = await supabase
            .from('companies')
            .select('id')
            .limit(1)
            .single();

          if (!companyError && company) {
            console.log("Found company, creating user_companies entry");
            // Create a user_companies entry with viewer role
            await supabase
              .from('user_companies')
              .insert({
                user_id: userId,
                company_id: company.id,
                role: 'viewer',
                is_default: true,
                invited_at: new Date().toISOString(),
                accepted_at: new Date().toISOString()
              });
          } else {
            console.log("No company found or error:", companyError);
          }
        } catch (roleError) {
          console.error("Error creating default role:", roleError);
        }

        return ["viewer"] as UserRole[];
      }

      console.log("Found roles for user:", data.map(item => item.role));
      return data.map(item => item.role as UserRole);
    } catch (error) {
      console.error('Unexpected error in fetchUserRoles:', error);
      return ["viewer"] as UserRole[];
    }
  };

  // Fetch all users with roles
  const fetchCompanyUsers = async (companyId?: string) => {
    try {
      // If no company ID provided, get the first company
      if (!companyId) {
        const { data: company, error: companyError } = await supabase
          .from('companies')
          .select('id')
          .limit(1)
          .single();

        if (companyError) {
          console.error('Error fetching company:', companyError);
          return [];
        }

        companyId = company.id;
      }

      // Get all user_companies entries for this company
      const { data, error } = await supabase
        .from('user_companies')
        .select(`
          user_id,
          role,
          profiles:user_id (
            first_name,
            last_name,
            email,
            avatar_url
          )
        `)
        .eq('company_id', companyId);

      if (error) {
        console.error('Error fetching company users:', error);
        return [];
      }

      // Group by user_id to get roles per user
      const userRolesMap = data.reduce<Record<string, {
        roles: UserRole[],
        profile: any
      }>>((acc, item) => {
        if (!acc[item.user_id]) {
          acc[item.user_id] = {
            roles: [],
            profile: item.profiles
          };
        }
        acc[item.user_id].roles.push(item.role as UserRole);
        return acc;
      }, {});

      return Object.entries(userRolesMap).map(([userId, data]) => ({
        userId,
        roles: data.roles,
        profile: data.profile
      }));
    } catch (error) {
      console.error('Unexpected error fetching company users:', error);
      return [];
    }
  };

  // Assign a role to a user for the company
  const assignUserRole = async (userId: string, role: UserRole, companyId?: string) => {
    try {
      // If no company ID provided, get the first company
      if (!companyId) {
        const { data: company, error: companyError } = await supabase
          .from('companies')
          .select('id')
          .limit(1)
          .single();

        if (companyError) {
          console.error('Error fetching company:', companyError);
          toast({
            title: 'Error',
            description: 'Failed to find company',
            variant: 'destructive'
          });
          return false;
        }

        companyId = company.id;
      }

      // Check if user_company entry already exists
      const { data: existingEntry, error: checkError } = await supabase
        .from('user_companies')
        .select('id, role')
        .eq('user_id', userId)
        .eq('company_id', companyId)
        .maybeSingle();

      if (checkError) {
        console.error('Error checking existing user company entry:', checkError);
      }

      if (existingEntry) {
        // User already has a role in this company, update it
        if (existingEntry.role === role) {
          toast({
            title: 'Role Already Exists',
            description: `User already has the ${role} role`,
            variant: 'default'
          });
          return true; // Not an error, already has the role
        }

        // Update the existing role
        const { error: updateError } = await supabase
          .from('user_companies')
          .update({ role })
          .eq('id', existingEntry.id);

        if (updateError) {
          console.error('Error updating role:', updateError);
          toast({
            title: 'Error',
            description: 'Failed to update role',
            variant: 'destructive'
          });
          return false;
        }
      } else {
        // Create a new user_company entry
        const { error: insertError } = await supabase
          .from('user_companies')
          .insert({
            user_id: userId,
            company_id: companyId,
            role,
            is_default: false,
            invited_at: new Date().toISOString(),
            accepted_at: new Date().toISOString()
          });

        if (insertError) {
          console.error('Error assigning role:', insertError);
          toast({
            title: 'Error',
            description: 'Failed to assign role',
            variant: 'destructive'
          });
          return false;
        }
      }

      toast({
        title: 'Role Assigned',
        description: `${role} role has been assigned successfully`
      });
      return true;
    } catch (error) {
      console.error('Unexpected error assigning role:', error);
      return false;
    }
  };

  // Remove a role from a user
  const removeUserRole = async (userId: string, role: UserRole, companyId?: string) => {
    try {
      // If no company ID provided, get the first company
      if (!companyId) {
        const { data: company, error: companyError } = await supabase
          .from('companies')
          .select('id')
          .limit(1)
          .single();

        if (companyError) {
          console.error('Error fetching company:', companyError);
          toast({
            title: 'Error',
            description: 'Failed to find company',
            variant: 'destructive'
          });
          return false;
        }

        companyId = company.id;
      }

      // Find the user_company entry
      const { data: entry, error: findError } = await supabase
        .from('user_companies')
        .select('id, role')
        .eq('user_id', userId)
        .eq('company_id', companyId)
        .eq('role', role)
        .maybeSingle();

      if (findError) {
        console.error('Error finding user company entry:', findError);
        toast({
          title: 'Error',
          description: 'Failed to find user role',
          variant: 'destructive'
        });
        return false;
      }

      if (!entry) {
        toast({
          title: 'Role Not Found',
          description: `User does not have the ${role} role`,
          variant: 'default'
        });
        return false;
      }

      // Delete the user_company entry
      const { error: deleteError } = await supabase
        .from('user_companies')
        .delete()
        .eq('id', entry.id);

      if (deleteError) {
        console.error('Error removing role:', deleteError);
        toast({
          title: 'Error',
          description: 'Failed to remove role',
          variant: 'destructive'
        });
        return false;
      }

      toast({
        title: 'Role Removed',
        description: `${role} role has been removed successfully`
      });
      return true;
    } catch (error) {
      console.error('Unexpected error removing role:', error);
      return false;
    }
  };

  return {
    fetchUserRoles,
    fetchCompanyUsers,
    assignUserRole,
    removeUserRole
  };
}
