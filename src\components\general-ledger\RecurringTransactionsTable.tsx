import React from 'react';
import { format } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import {
  MoreHorizontal,
  Play,
  Pencil,
  Trash2,
  Pause,
  RefreshCw,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { RecurringTransactionWithItems } from '@/types/recurring-transactions';
import { RecurringTransactionDialog } from './RecurringTransactionDialog';
import { AccountWithBalance } from '@/types/index';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface RecurringTransactionsTableProps {
  recurringTransactions: RecurringTransactionWithItems[];
  accounts: AccountWithBalance[];
  onDelete: (id: string) => Promise<boolean>;
  onUpdateStatus: (id: string, status: 'active' | 'paused' | 'completed' | 'cancelled') => Promise<boolean>;
  onGenerateNow: (id: string) => Promise<boolean>;
  isLoading?: boolean;
}

export const RecurringTransactionsTable: React.FC<RecurringTransactionsTableProps> = ({
  recurringTransactions,
  accounts,
  onDelete,
  onUpdateStatus,
  onGenerateNow,
  isLoading = false,
}) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [transactionToDelete, setTransactionToDelete] = React.useState<string | null>(null);

  const handleDeleteClick = (id: string) => {
    setTransactionToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (transactionToDelete) {
      await onDelete(transactionToDelete);
      setDeleteDialogOpen(false);
      setTransactionToDelete(null);
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'paused':
        return 'warning';
      case 'completed':
        return 'default';
      case 'cancelled':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getFrequencyLabel = (frequency: string) => {
    switch (frequency) {
      case 'daily':
        return 'Daily';
      case 'weekly':
        return 'Weekly';
      case 'monthly':
        return 'Monthly';
      case 'quarterly':
        return 'Quarterly';
      case 'yearly':
        return 'Yearly';
      default:
        return frequency;
    }
  };

  // Get transaction amount from items
  const getTransactionAmount = (transaction: RecurringTransactionWithItems): number => {
    const debitItem = transaction.items.find(item => item.debit > 0);
    return debitItem?.debit || 0;
  };

  if (isLoading) {
    return <div className="py-4 text-center">Loading recurring transactions...</div>;
  }

  if (recurringTransactions.length === 0) {
    return (
      <div className="py-8 text-center">
        <p className="text-muted-foreground mb-4">No recurring transactions found</p>
        <RecurringTransactionDialog accounts={accounts} />
      </div>
    );
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Frequency</TableHead>
            <TableHead>Next Due</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {recurringTransactions.map((transaction) => (
            <TableRow key={transaction.id}>
              <TableCell className="font-medium">{transaction.name}</TableCell>
              <TableCell>{getFrequencyLabel(transaction.frequency)}</TableCell>
              <TableCell>{format(new Date(transaction.next_due_date), 'MMM d, yyyy')}</TableCell>
              <TableCell>{formatCurrency(getTransactionAmount(transaction))}</TableCell>
              <TableCell>
                <Badge variant={getStatusBadgeVariant(transaction.status)}>
                  {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <RecurringTransactionDialog
                      accounts={accounts}
                      existingTransaction={transaction}
                      trigger={
                        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                          <Pencil className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                      }
                    />
                    
                    {transaction.status === 'active' ? (
                      <DropdownMenuItem onClick={() => onUpdateStatus(transaction.id, 'paused')}>
                        <Pause className="mr-2 h-4 w-4" />
                        Pause
                      </DropdownMenuItem>
                    ) : transaction.status === 'paused' ? (
                      <DropdownMenuItem onClick={() => onUpdateStatus(transaction.id, 'active')}>
                        <Play className="mr-2 h-4 w-4" />
                        Resume
                      </DropdownMenuItem>
                    ) : null}
                    
                    {(transaction.status === 'active' || transaction.status === 'paused') && (
                      <DropdownMenuItem onClick={() => onGenerateNow(transaction.id)}>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Generate Now
                      </DropdownMenuItem>
                    )}
                    
                    <DropdownMenuItem 
                      className="text-destructive focus:text-destructive"
                      onClick={() => handleDeleteClick(transaction.id)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this recurring transaction. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
