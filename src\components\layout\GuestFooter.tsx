import { Link } from 'react-router-dom';

interface GuestFooterProps {
  // Add any props if needed
}

/**
 * Shared footer component for guest pages
 */
export function GuestFooter({}: GuestFooterProps) {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-cream-alt border-t border-border py-8">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <p className="text-dark-blue/70 text-sm">
              &copy; {currentYear} Kaya Finance. All rights reserved.
            </p>
            <p className="text-dark-blue/50 text-xs mt-1">
              Version 1.0.0
            </p>
          </div>

          <div className="flex flex-wrap justify-center gap-x-8 gap-y-2">
            <Link
              to="/terms"
              className="text-dark-blue/70 hover:text-dark-blue text-sm focus:outline-none focus:ring-2 focus:ring-dark-blue/20 rounded-sm"
              aria-label="Terms of Service"
            >
              Terms of Service
            </Link>
            <Link
              to="/privacy"
              className="text-dark-blue/70 hover:text-dark-blue text-sm focus:outline-none focus:ring-2 focus:ring-dark-blue/20 rounded-sm"
              aria-label="Privacy Policy"
            >
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
