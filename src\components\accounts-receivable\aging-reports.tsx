/**
 * Aging Reports Component
 *
 * This component displays detailed aging reports for accounts receivable,
 * including aging buckets, visualizations, and export functionality.
 */

import { useState } from 'react';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Download, Filter, RefreshCw } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { AgingChart } from './aging-chart';
import {
  AgingReportDetailed,
  InvoiceAgingData,
  CustomerAgingSummary
} from '@/services/aging-report-service';

interface AgingReportsProps {
  agingData: AgingReportDetailed | null;
  isLoading: boolean;
  asOfDate: Date;
  onDateChange: (date: Date) => void;
  onRefresh: () => void;
  formatCurrency: (amount: number) => string;
}

export function AgingReports({
  agingData,
  isLoading,
  asOfDate,
  onDateChange,
  onRefresh,
  formatCurrency
}: AgingReportsProps): JSX.Element {
  const [activeTab, setActiveTab] = useState<string>('summary');
  const [customerFilter, setCustomerFilter] = useState<string>('');
  const [amountFilter, setAmountFilter] = useState<string>('');
  const [agingBucketFilter, setAgingBucketFilter] = useState<string>('all');

  // Filter invoices based on current filters
  const filteredInvoices = agingData?.invoices.filter(invoice => {
    // Customer name filter
    if (customerFilter && !invoice.customer_name.toLowerCase().includes(customerFilter.toLowerCase())) {
      return false;
    }

    // Amount filter
    if (amountFilter) {
      const amount = parseFloat(amountFilter);
      if (!isNaN(amount) && invoice.balance < amount) {
        return false;
      }
    }

    // Aging bucket filter
    if (agingBucketFilter !== 'all' && invoice.aging_bucket !== agingBucketFilter) {
      return false;
    }

    return true;
  }) || [];

  // Export to CSV function
  const exportToCSV = () => {
    if (!agingData) return;

    // Create CSV content
    let csvContent = 'Customer,Invoice Number,Issue Date,Due Date,Amount,Paid,Balance,Days Overdue,Aging Bucket\n';

    agingData.invoices.forEach(invoice => {
      csvContent += `"${invoice.customer_name}",`;
      csvContent += `"${invoice.invoice_number}",`;
      csvContent += `"${format(new Date(invoice.issue_date), 'yyyy-MM-dd')}",`;
      csvContent += `"${format(new Date(invoice.due_date), 'yyyy-MM-dd')}",`;
      csvContent += `"${invoice.total_amount}",`;
      csvContent += `"${invoice.paid_amount}",`;
      csvContent += `"${invoice.balance}",`;
      csvContent += `"${invoice.days_overdue}",`;
      csvContent += `"${invoice.aging_bucket}"\n`;
    });

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `aging-report-${format(asOfDate, 'yyyy-MM-dd')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Get aging bucket color
  const getAgingBucketColor = (bucket: string): string => {
    switch (bucket) {
      case 'Current':
        return 'bg-green-100 text-green-800';
      case '1-30 days':
        return 'bg-yellow-100 text-yellow-800';
      case '31-60 days':
        return 'bg-orange-100 text-orange-800';
      case '61-90 days':
        return 'bg-red-100 text-red-800';
      case 'Over 90 days':
        return 'bg-red-200 text-red-900';
      case 'Paid':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Aging Reports</h2>
          <p className="text-muted-foreground">
            Analyze outstanding invoices by aging periods
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-2">
            <Label htmlFor="as-of-date">As of:</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="as-of-date"
                  variant="outline"
                  className={cn(
                    "w-[180px] justify-start text-left font-normal",
                    !asOfDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {asOfDate ? format(asOfDate, "PPP") : "Select date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={asOfDate}
                  onSelect={(date) => date && onDateChange(date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          <Button variant="outline" size="icon" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4" />
          </Button>
          <Button variant="outline" onClick={exportToCSV}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {isLoading ? (
        <Card>
          <CardContent className="pt-6">
            <div className="h-40 flex items-center justify-center">
              <p className="text-muted-foreground">Loading aging data...</p>
            </div>
          </CardContent>
        </Card>
      ) : !agingData ? (
        <Card>
          <CardContent className="pt-6">
            <div className="h-40 flex items-center justify-center">
              <p className="text-muted-foreground">No aging data available</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Aging Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <Card>
              <CardContent className="pt-6">
                <CardDescription>Total Receivables</CardDescription>
                <div className="text-2xl font-bold mt-1">
                  {formatCurrency(agingData.summary.total_receivables)}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <CardDescription>Current</CardDescription>
                <div className="text-2xl font-bold mt-1 text-green-600">
                  {formatCurrency(agingData.summary.current_amount)}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <CardDescription>1-30 Days</CardDescription>
                <div className="text-2xl font-bold mt-1 text-yellow-600">
                  {formatCurrency(agingData.summary.days_1_30_amount)}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <CardDescription>31-60 Days</CardDescription>
                <div className="text-2xl font-bold mt-1 text-orange-600">
                  {formatCurrency(agingData.summary.days_31_60_amount)}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <CardDescription>61-90 Days</CardDescription>
                <div className="text-2xl font-bold mt-1 text-red-600">
                  {formatCurrency(agingData.summary.days_61_90_amount)}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <CardDescription>Over 90 Days</CardDescription>
                <div className="text-2xl font-bold mt-1 text-red-800">
                  {formatCurrency(agingData.summary.over_90_days_amount)}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Visualization */}
          <Card>
            <CardHeader>
              <CardTitle>Aging Visualization</CardTitle>
            </CardHeader>
            <CardContent>
              <AgingChart agingData={agingData.summary} formatCurrency={formatCurrency} />
            </CardContent>
          </Card>

          {/* Detailed Reports */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Detailed Aging Report</CardTitle>
              <div className="flex flex-col sm:flex-row gap-4 mt-4">
                <div className="flex-1">
                  <Label htmlFor="customer-filter">Customer</Label>
                  <Input
                    id="customer-filter"
                    placeholder="Filter by customer name"
                    value={customerFilter}
                    onChange={(e) => setCustomerFilter(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="amount-filter">Min Amount</Label>
                  <Input
                    id="amount-filter"
                    placeholder="Min amount"
                    type="number"
                    value={amountFilter}
                    onChange={(e) => setAmountFilter(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="aging-bucket-filter">Aging Bucket</Label>
                  <Select
                    value={agingBucketFilter}
                    onValueChange={setAgingBucketFilter}
                  >
                    <SelectTrigger id="aging-bucket-filter">
                      <SelectValue placeholder="All Buckets" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Buckets</SelectItem>
                      <SelectItem value="Current">Current</SelectItem>
                      <SelectItem value="1-30 days">1-30 Days</SelectItem>
                      <SelectItem value="31-60 days">31-60 Days</SelectItem>
                      <SelectItem value="61-90 days">61-90 Days</SelectItem>
                      <SelectItem value="Over 90 days">Over 90 Days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList>
                  <TabsTrigger value="summary">Customer Summary</TabsTrigger>
                  <TabsTrigger value="detailed">Invoice Details</TabsTrigger>
                </TabsList>
                <TabsContent value="summary" className="mt-4">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Customer</TableHead>
                        <TableHead className="text-right">Current</TableHead>
                        <TableHead className="text-right">1-30 Days</TableHead>
                        <TableHead className="text-right">31-60 Days</TableHead>
                        <TableHead className="text-right">61-90 Days</TableHead>
                        <TableHead className="text-right">Over 90 Days</TableHead>
                        <TableHead className="text-right">Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {agingData.summary.customers.map((customer) => (
                        <TableRow key={customer.customer_id}>
                          <TableCell>{customer.customer_name}</TableCell>
                          <TableCell className="text-right">{formatCurrency(customer.current_amount)}</TableCell>
                          <TableCell className="text-right">{formatCurrency(customer.days_1_30_amount)}</TableCell>
                          <TableCell className="text-right">{formatCurrency(customer.days_31_60_amount)}</TableCell>
                          <TableCell className="text-right">{formatCurrency(customer.days_61_90_amount)}</TableCell>
                          <TableCell className="text-right">{formatCurrency(customer.over_90_days_amount)}</TableCell>
                          <TableCell className="text-right font-medium">{formatCurrency(customer.total_balance)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TabsContent>
                <TabsContent value="detailed" className="mt-4">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Customer</TableHead>
                        <TableHead>Invoice #</TableHead>
                        <TableHead>Issue Date</TableHead>
                        <TableHead>Due Date</TableHead>
                        <TableHead className="text-right">Amount</TableHead>
                        <TableHead className="text-right">Balance</TableHead>
                        <TableHead className="text-right">Days Overdue</TableHead>
                        <TableHead>Aging</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredInvoices.map((invoice) => (
                        <TableRow key={invoice.invoice_id}>
                          <TableCell>{invoice.customer_name}</TableCell>
                          <TableCell>{invoice.invoice_number}</TableCell>
                          <TableCell>{format(new Date(invoice.issue_date), 'MMM d, yyyy')}</TableCell>
                          <TableCell>{format(new Date(invoice.due_date), 'MMM d, yyyy')}</TableCell>
                          <TableCell className="text-right">{formatCurrency(invoice.total_amount)}</TableCell>
                          <TableCell className="text-right">{formatCurrency(invoice.balance)}</TableCell>
                          <TableCell className="text-right">{invoice.days_overdue}</TableCell>
                          <TableCell>
                            <Badge className={getAgingBucketColor(invoice.aging_bucket)}>
                              {invoice.aging_bucket}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
