/**
 * Invoice Workflow Service
 *
 * This service handles the business logic for invoice workflows,
 * including status transitions, approvals, and notifications.
 */

import { supabase } from '@/integrations/supabase/client';
import { showSuccessToast, showErrorToast, showWarningToast } from '@/utils/toast-utils';
import { format, addDays, isPast, isAfter } from 'date-fns';
import { Tables } from '@/types/database';
import { PostgrestError } from '@supabase/supabase-js';

type Invoice = Tables<'invoices'> & {
  customers?: {
    name: string;
    email: string;
  };
};

// Invoice status type
type InvoiceStatus = "draft" | "sent" | "paid" | "overdue" | "cancelled";

// Invoice workflow transition type
interface StatusTransition {
  from: InvoiceStatus;
  to: InvoiceStatus;
  label: string;
  requiresApproval: boolean;
  color: 'default' | 'primary' | 'success' | 'warning' | 'destructive';
}

// Available status transitions for invoices
export const statusTransitions: StatusTransition[] = [
  { from: 'draft', to: 'sent', label: 'Send Invoice', requiresApproval: false, color: 'primary' },
  { from: 'sent', to: 'paid', label: 'Mark as Paid', requiresApproval: true, color: 'success' },
  { from: 'sent', to: 'overdue', label: 'Mark as Overdue', requiresApproval: false, color: 'warning' },
  { from: 'overdue', to: 'paid', label: 'Mark as Paid', requiresApproval: true, color: 'success' },
  { from: 'draft', to: 'cancelled', label: 'Cancel Invoice', requiresApproval: false, color: 'destructive' },
  { from: 'sent', to: 'cancelled', label: 'Cancel Invoice', requiresApproval: true, color: 'destructive' },
  { from: 'overdue', to: 'cancelled', label: 'Cancel Invoice', requiresApproval: true, color: 'destructive' },
  { from: 'paid', to: 'draft', label: 'Revert to Draft', requiresApproval: true, color: 'warning' },
];

/**
 * Get available status transitions for an invoice
 *
 * @param currentStatus Current invoice status
 * @param userRole User role (admin, accountant, manager, viewer)
 * @returns Array of available status transitions
 */
export const getAvailableTransitions = (
  currentStatus: InvoiceStatus,
  userRole: string
): StatusTransition[] => {
  // Filter transitions based on current status
  const transitions = statusTransitions.filter(
    transition => transition.from === currentStatus
  );

  // If user is not admin or accountant, filter out transitions that require approval
  if (userRole !== 'admin' && userRole !== 'accountant') {
    return transitions.filter(transition => !transition.requiresApproval);
  }

  return transitions;
};

/**
 * Check if a status transition is valid
 *
 * @param fromStatus Current status
 * @param toStatus Target status
 * @param userRole User role
 * @returns Whether the transition is valid
 */
export const isValidTransition = (
  fromStatus: InvoiceStatus,
  toStatus: InvoiceStatus,
  userRole: string
): boolean => {
  const availableTransitions = getAvailableTransitions(fromStatus, userRole);
  return availableTransitions.some(transition => transition.to === toStatus);
};

/**
 * Update invoice status with proper validation
 *
 * @param invoiceId Invoice ID
 * @param newStatus New status
 * @param userId User ID making the change
 * @param userRole User role
 * @returns Success status and updated invoice
 */
export const updateInvoiceStatus = async (
  invoiceId: string,
  newStatus: InvoiceStatus,
  userId: string,
  userRole: string
): Promise<{ success: boolean; invoice?: Invoice }> => {
  try {
    // Fetch current invoice
    const { data: invoice, error: fetchError } = await supabase
      .from('invoices')
      .select('*')
      .eq('id', invoiceId)
      .single();

    if (fetchError) throw fetchError;
    if (!invoice) throw new Error('Invoice not found');

    // Validate status transition
    if (!isValidTransition(invoice.status, newStatus, userRole)) {
      showWarningToast(
        'Invalid Status Change',
        `Cannot change invoice status from ${invoice.status} to ${newStatus} with your permissions.`
      );
      return { success: false };
    }

    // Update invoice status
    const { data: updatedInvoice, error: updateError } = await supabase
      .from('invoices')
      .update({
        status: newStatus,
        updated_at: new Date().toISOString(),
        updated_by: userId,
      })
      .eq('id', invoiceId)
      .select()
      .single();

    if (updateError) throw updateError;

    // Create status history record
    const { error: historyError } = await supabase
      .from('invoice_status_history')
      .insert({
        invoice_id: invoiceId,
        from_status: invoice.status,
        to_status: newStatus,
        changed_by: userId,
        notes: null,
      });

    if (historyError) {
      console.error('Error recording status history:', historyError);
      // Continue anyway, this is not critical
    }

    // Show success message
    showSuccessToast(
      'Status Updated',
      `Invoice #${invoice.invoice_number} has been ${newStatus}.`
    );

    return { success: true, invoice: updatedInvoice };
  } catch (error: unknown) {
    console.error('Error updating invoice status:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to update invoice status. Please try again.';
    showErrorToast(
      'Status Update Failed',
      errorMessage
    );
    return { success: false };
  }
};

/**
 * Send invoice to customer via email
 *
 * @param invoiceId Invoice ID
 * @param userId User ID sending the invoice
 * @returns Success status
 */
export const sendInvoiceEmail = async (
  invoiceId: string,
  userId: string
): Promise<boolean> => {
  try {
    // Fetch invoice with customer details
    const { data: invoice, error: fetchError } = await supabase
      .from('invoices')
      .select(`
        *,
        customers (
          name, email
        )
      `)
      .eq('id', invoiceId)
      .single();

    if (fetchError) throw fetchError;
    if (!invoice) throw new Error('Invoice not found');
    if (!invoice.customers?.email) {
      showWarningToast(
        'Missing Email',
        'Customer does not have an email address. Please update customer details first.'
      );
      return false;
    }

    // In a real app, you would integrate with an email service here
    // For now, we'll just simulate sending an email

    // Update invoice status to 'sent'
    const { error: updateError } = await supabase
      .from('invoices')
      .update({
        status: 'sent',
        updated_at: new Date().toISOString(),
        updated_by: userId,
        sent_at: new Date().toISOString(),
        sent_by: userId,
      })
      .eq('id', invoiceId);

    if (updateError) throw updateError;

    // Create status history record
    await supabase
      .from('invoice_status_history')
      .insert({
        invoice_id: invoiceId,
        from_status: 'draft',
        to_status: 'sent',
        changed_by: userId,
        notes: `Email sent to ${invoice.customers.email}`,
      });

    showSuccessToast(
      'Invoice Sent',
      `Invoice #${invoice.invoice_number} has been sent to ${invoice.customers.name}.`
    );

    return true;
  } catch (error: unknown) {
    console.error('Error sending invoice:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to send invoice. Please try again.';
    showErrorToast(
      'Failed to Send Invoice',
      errorMessage
    );
    return false;
  }
};

/**
 * Record payment for an invoice
 *
 * @param invoiceId Invoice ID
 * @param paymentData Payment data
 * @param userId User ID recording the payment
 * @returns Success status
 */
export const recordInvoicePayment = async (
  invoiceId: string,
  paymentData: {
    amount: number;
    paymentDate: Date;
    paymentMethod: string;
    reference?: string;
    notes?: string;
  },
  userId: string
): Promise<boolean> => {
  try {
    // Fetch invoice
    const { data: invoice, error: fetchError } = await supabase
      .from('invoices')
      .select('*')
      .eq('id', invoiceId)
      .single();

    if (fetchError) throw fetchError;
    if (!invoice) throw new Error('Invoice not found');

    // Create payment record
    const { error: paymentError } = await supabase
      .from('invoice_payments')
      .insert({
        invoice_id: invoiceId,
        amount: paymentData.amount,
        payment_date: format(paymentData.paymentDate, 'yyyy-MM-dd'),
        payment_method: paymentData.paymentMethod,
        reference: paymentData.reference || null,
        notes: paymentData.notes || null,
        recorded_by: userId,
      });

    if (paymentError) throw paymentError;

    // Check if payment completes the invoice
    const { data: payments, error: paymentsError } = await supabase
      .from('invoice_payments')
      .select('amount')
      .eq('invoice_id', invoiceId);

    if (paymentsError) throw paymentsError;

    const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);

    // If total paid equals or exceeds invoice amount, mark as paid
    if (totalPaid >= invoice.total_amount) {
      await updateInvoiceStatus(invoiceId, 'paid', userId, 'admin');
    }

    showSuccessToast(
      'Payment Recorded',
      `Payment of ${formatCurrency(paymentData.amount)} has been recorded for invoice #${invoice.invoice_number}.`
    );

    return true;
  } catch (error: any) {
    console.error('Error recording payment:', error);
    showErrorToast(
      'Failed to Record Payment',
      error.message || 'Failed to record payment. Please try again.'
    );
    return false;
  }
};

/**
 * Check for overdue invoices and update their status
 *
 * @param companyId Company ID
 * @returns Number of invoices updated
 */
export const checkOverdueInvoices = async (
  companyId: string
): Promise<number> => {
  try {
    const today = new Date();

    // Find sent invoices that are past due date
    const { data: overdueInvoices, error } = await supabase
      .from('invoices')
      .select('id, invoice_number, due_date')
      .eq('company_id', companyId)
      .eq('status', 'sent')
      .lt('due_date', format(today, 'yyyy-MM-dd'));

    if (error) throw error;
    if (!overdueInvoices || overdueInvoices.length === 0) return 0;

    // Update status to overdue
    const { error: updateError } = await supabase
      .from('invoices')
      .update({
        status: 'overdue',
        updated_at: new Date().toISOString(),
      })
      .in('id', overdueInvoices.map(invoice => invoice.id));

    if (updateError) throw updateError;

    // Create status history records
    const historyRecords = overdueInvoices.map(invoice => ({
      invoice_id: invoice.id,
      from_status: 'sent',
      to_status: 'overdue',
      changed_by: 'system',
      notes: 'Automatically marked as overdue',
    }));

    await supabase
      .from('invoice_status_history')
      .insert(historyRecords);

    return overdueInvoices.length;
  } catch (error: any) {
    console.error('Error checking overdue invoices:', error);
    return 0;
  }
};

/**
 * Format currency as UGX
 *
 * @param amount Amount to format
 * @returns Formatted currency string
 */
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-UG', {
    style: 'currency',
    currency: 'UGX',
    minimumFractionDigits: 0,
  }).format(amount);
};
