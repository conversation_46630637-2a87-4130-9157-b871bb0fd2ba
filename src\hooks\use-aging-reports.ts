/**
 * Hook for managing aging reports with React Query
 * 
 * This hook provides functionality to fetch and manage aging report data.
 */

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/context/AuthContext';
import { fetchAgingReport, AgingReportDetailed } from '@/services/aging-report-service';
import { showErrorToast } from '@/utils/toast-utils';

/**
 * Hook for managing aging reports
 * 
 * @returns Object containing aging report data and related functions
 */
export const useAgingReports = () => {
  const { currentCompanyId } = useAuth();
  const [asOfDate, setAsOfDate] = useState<Date>(new Date());

  /**
   * Query to fetch aging report data
   */
  const {
    data: agingReport,
    isLoading,
    error,
    refetch,
  } = useQuery<AgingReportDetailed | null>({
    queryKey: ['agingReport', currentCompanyId, asOfDate.toISOString().split('T')[0]],
    queryFn: async () => {
      if (!currentCompanyId) return null;

      try {
        return await fetchAgingReport(currentCompanyId, asOfDate);
      } catch (error: any) {
        showErrorToast(
          'Failed to Load Aging Report',
          error.message || 'An error occurred while loading the aging report'
        );
        throw error;
      }
    },
    enabled: !!currentCompanyId,
  });

  /**
   * Format currency as UGX (or specified currency)
   */
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: agingReport?.summary.currency || 'UGX',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  /**
   * Handle date change
   */
  const handleDateChange = (date: Date) => {
    setAsOfDate(date);
  };

  return {
    agingReport,
    isLoading,
    error,
    asOfDate,
    setAsOfDate: handleDateChange,
    refetchAgingReport: refetch,
    formatCurrency,
  };
};
