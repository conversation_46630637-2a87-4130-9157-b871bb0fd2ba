import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Loader2, Trash2, RefreshCw } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { Profile } from '@/types/index';
import { useProfileManagement, ProfileOperationCallbacks } from '@/hooks/useProfileManagement';

const ProfilePage = () => {
  const { profile, user } = useAuth();
  const {
    updateProfile,
    deleteProfile,
    loadingStates,
    lastError,
    optimisticUpdates,
    retryLastOperation,
    cleanup
  } = useProfileManagement({
    maxRetries: 3,
    retryDelay: 1000,
    exponentialBackoff: true
  });

  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<Partial<Profile>>(profile || {});
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const isMounted = useRef(true);

  useEffect(() => {
    return () => {
      isMounted.current = false;
      cleanup();
    };
  }, [cleanup]);

  useEffect(() => {
    if (profile && isMounted.current) {
      setFormData(profile);
    }
  }, [profile]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (isMounted.current) {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const profileCallbacks: ProfileOperationCallbacks = {
    onStart: () => console.log('Profile operation started'),
    onSuccess: (data) => {
      console.log('Profile operation succeeded:', data);
      if (isMounted.current) {
        setIsEditing(false);
      }
    },
    onError: (error) => console.error('Profile operation failed:', error),
    onComplete: () => console.log('Profile operation completed')
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user?.id || loadingStates.update) return;

    try {
      await updateProfile(user.id, formData, {
        optimistic: true,
        callbacks: profileCallbacks
      });
    } catch (error) {
      console.error('Update failed:', error);
    }
  };

  const handleCancel = () => {
    if (isMounted.current) {
      setFormData(profile || {});
      setIsEditing(false);
    }
  };

  const handleDelete = async () => {
    if (!user?.id || loadingStates.delete) return;

    try {
      await deleteProfile(user.id, {
        hard: false, // Soft delete
        callbacks: {
          onSuccess: () => {
            setShowDeleteConfirm(false);
            // Redirect or show success message
          },
          onError: (error) => {
            console.error('Delete failed:', error);
            setShowDeleteConfirm(false);
          }
        }
      });
    } catch (error) {
      console.error('Delete operation failed:', error);
    }
  };

  const handleRetry = async () => {
    try {
      await retryLastOperation();
    } catch (error) {
      console.error('Retry failed:', error);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto space-y-4">
      {/* Status indicators */}
      <div className="flex flex-wrap gap-2">
        {optimisticUpdates.length > 0 && (
          <Badge variant="secondary" className="flex items-center gap-1">
            <Loader2 className="h-3 w-3 animate-spin" />
            {optimisticUpdates.length} pending update{optimisticUpdates.length > 1 ? 's' : ''}
          </Badge>
        )}
        {lastError && (
          <Badge variant="destructive" className="flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            {lastError.operation} failed
          </Badge>
        )}
        {Object.entries(loadingStates).map(([operation, loading]) =>
          loading && (
            <Badge key={operation} variant="outline" className="flex items-center gap-1">
              <Loader2 className="h-3 w-3 animate-spin" />
              {operation}...
            </Badge>
          )
        )}
      </div>

      {/* Error retry section */}
      {lastError && (
        <Card className="border-destructive">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-destructive" />
                <span className="text-sm text-destructive">
                  {lastError.operation} operation failed: {lastError.error.message}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRetry}
                className="flex items-center gap-1"
              >
                <RefreshCw className="h-3 w-3" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Profile Settings</CardTitle>
          <CardDescription>Manage your account settings and preferences</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  name="first_name"
                  value={formData.first_name || ''}
                  onChange={handleInputChange}
                  disabled={!isEditing || loadingStates.update}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  name="last_name"
                  value={formData.last_name || ''}
                  onChange={handleInputChange}
                  disabled={!isEditing || loadingStates.update}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={user?.email || ''}
                disabled
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                name="phone"
                value={formData.phone || ''}
                onChange={handleInputChange}
                disabled={!isEditing || loadingStates.update}
              />
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="flex space-x-2">
            {!isEditing && (
              <Button
                variant="destructive"
                size="sm"
                onClick={() => setShowDeleteConfirm(true)}
                disabled={loadingStates.delete}
                className="flex items-center gap-1"
              >
                <Trash2 className="h-3 w-3" />
                Delete Profile
              </Button>
            )}
          </div>

          <div className="flex space-x-2">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={loadingStates.update}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  onClick={handleSubmit}
                  disabled={loadingStates.update}
                  className="flex items-center gap-1"
                >
                  {loadingStates.update && <Loader2 className="h-3 w-3 animate-spin" />}
                  {loadingStates.update ? 'Saving...' : 'Save Changes'}
                </Button>
              </>
            ) : (
              <Button
                variant="outline"
                onClick={() => setIsEditing(true)}
                disabled={Object.values(loadingStates).some(Boolean)}
              >
                Edit Profile
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>

      {/* Delete confirmation dialog */}
      {showDeleteConfirm && (
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Delete Profile</CardTitle>
            <CardDescription>
              Are you sure you want to delete your profile? This action cannot be undone.
            </CardDescription>
          </CardHeader>
          <CardFooter className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => setShowDeleteConfirm(false)}
              disabled={loadingStates.delete}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={loadingStates.delete}
              className="flex items-center gap-1"
            >
              {loadingStates.delete && <Loader2 className="h-3 w-3 animate-spin" />}
              {loadingStates.delete ? 'Deleting...' : 'Delete Profile'}
            </Button>
          </CardFooter>
        </Card>
      )}
    </div>
  );
};

export default ProfilePage;
