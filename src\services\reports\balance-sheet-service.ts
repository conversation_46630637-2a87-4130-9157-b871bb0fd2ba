import { format } from "date-fns";
import { supabase } from "@/integrations/supabase/client";
import { AccountCategorySummary, BalanceSheetData } from "./types";

/**
 * Fetch balance sheet data for a company as of a specific date
 *
 * @param companyId ID of the company
 * @param date Date for the balance sheet (defaults to current date)
 * @returns Balance sheet data or null if there was an error
 */
export const fetchBalanceSheet = async (companyId: string, date = new Date()): Promise<BalanceSheetData | null> => {
  try {
    // Format date for query
    const asOfDate = format(date, 'yyyy-MM-dd');

    // Get asset accounts
    const { data: assetAccounts, error: assetError } = await supabase
      .from('account_balances')
      .select('account_id, account_name, balance')
      .eq('company_id', companyId)
      .eq('category', 'Assets')
      .lte('as_of_date', asOfDate);

    if (assetError) throw assetError;

    // Get liability accounts
    const { data: liabilityAccounts, error: liabilityError } = await supabase
      .from('account_balances')
      .select('account_id, account_name, balance')
      .eq('company_id', companyId)
      .eq('category', 'Liabilities')
      .lte('as_of_date', asOfDate);

    if (liabilityError) throw liabilityError;

    // Get equity accounts
    const { data: equityAccounts, error: equityError } = await supabase
      .from('account_balances')
      .select('account_id, account_name, balance')
      .eq('company_id', companyId)
      .eq('category', 'Equity')
      .lte('as_of_date', asOfDate);

    if (equityError) throw equityError;

    // Process and return data
    const assets: AccountCategorySummary = {
      category: 'Assets',
      total: assetAccounts?.reduce((sum, account) => sum + account.balance, 0) || 0,
      items: assetAccounts?.map(account => ({
        name: account.account_name,
        amount: account.balance,
        account_id: account.account_id
      })) || []
    };

    const liabilities: AccountCategorySummary = {
      category: 'Liabilities',
      total: liabilityAccounts?.reduce((sum, account) => sum + account.balance, 0) || 0,
      items: liabilityAccounts?.map(account => ({
        name: account.account_name,
        amount: account.balance,
        account_id: account.account_id
      })) || []
    };

    const equity: AccountCategorySummary = {
      category: 'Equity',
      total: equityAccounts?.reduce((sum, account) => sum + account.balance, 0) || 0,
      items: equityAccounts?.map(account => ({
        name: account.account_name,
        amount: account.balance,
        account_id: account.account_id
      })) || []
    };

    return {
      assets,
      liabilities,
      equity,
      asOfDate
    };
  } catch (error) {
    console.error('Error fetching balance sheet data:', error);
    return null;
  }
};
