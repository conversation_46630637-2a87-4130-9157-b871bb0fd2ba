
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from './use-toast';
import { useAuth } from '@/context/AuthContext';
import {
  Transaction,
  TransactionItem,
  Account,
  AccountType
} from '@/types/index';

/**
 * Interface for simplified transaction data used in dashboard and reports
 */
export interface RecentTransaction {
  id: string;
  description: string;
  amount: number;
  date: string;
  type: "income" | "expense";
  category: string;
}

// Type for transaction item with nested account and transaction
interface TransactionItemWithRelations extends TransactionItem {
  transactions: Pick<Transaction, 'transaction_date' | 'description' | 'company_id' | 'reference'>;
  accounts: Account & {
    account_types: AccountType;
  };
}

export const useRecentTransactions = (): { transactions: RecentTransaction[]; isLoading: boolean } => {
  const [transactions, setTransactions] = useState<RecentTransaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { currentCompanyId } = useAuth();

  useEffect(() => {
    const fetchTransactions = async (): Promise<void> => {
      if (!currentCompanyId) return;

      try {
        setIsLoading(true);

        // Fetch the most recent transaction items with account information
        const { data: transactionItems, error } = await supabase
          .from('transaction_items')
          .select(`
            id,
            transaction_id,
            description,
            debit,
            credit,
            transactions(transaction_date, description, company_id, reference),
            accounts(
              id,
              name,
              account_type_id,
              account_types(
                id,
                name,
                normal_balance
              )
            )
          `)
          .eq('transactions.company_id', currentCompanyId)
          .order('created_at', { ascending: false })
          .limit(5);

        if (error) {
          throw error;
        }

        if (transactionItems) {
          // Type assertion for the fetched data
          const typedItems = transactionItems as unknown as TransactionItemWithRelations[];

          // Transform the transaction items into the RecentTransaction format
          const recentTransactions = typedItems.map((item): RecentTransaction => {
            // Determine if this is income based on account type's normal balance
            // Revenue accounts typically have a credit normal balance
            const isIncome = item.accounts.account_types.normal_balance === 'credit' && item.credit;

            return {
              id: item.id,
              description: item.description || item.transactions.description || 'Transaction',
              amount: isIncome ? (item.credit || 0) : (item.debit || 0),
              date: item.transactions.transaction_date,
              type: isIncome ? "income" : "expense",
              category: item.accounts.name,
            };
          });

          setTransactions(recentTransactions);
        }
      } catch (err) {
        console.error('Error fetching recent transactions:', err);
        const errorMessage = err instanceof Error
          ? err.message
          : "Could not load recent transactions. Please try again later.";

        toast({
          title: "Error fetching transactions",
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchTransactions();
  }, [currentCompanyId, toast]);

  return { transactions, isLoading };
};
