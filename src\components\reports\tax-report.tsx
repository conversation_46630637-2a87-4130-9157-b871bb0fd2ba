
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TaxReportData } from '@/services/report-service';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';

interface TaxReportProps {
  data: TaxReportData;
  formatCurrency: (amount: number) => string;
  year: number;
  quarter?: number;
  isLoading?: boolean;
}

export const TaxReport = ({ 
  data, 
  formatCurrency, 
  year,
  quarter,
  isLoading = false 
}: TaxReportProps) => {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Tax Reports</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-40 flex items-center justify-center">
            <p className="text-muted-foreground">Loading tax report data...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const periodText = quarter 
    ? `Q${quarter} ${year}` 
    : `Year ${year}`;
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Tax Reports</CardTitle>
        <p className="text-sm text-muted-foreground">{periodText}</p>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="vat">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="vat">VAT (18%)</TabsTrigger>
            <TabsTrigger value="paye">PAYE</TabsTrigger>
            <TabsTrigger value="withholding">Withholding Tax</TabsTrigger>
          </TabsList>
          
          {/* VAT Tab */}
          <TabsContent value="vat">
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-700 mb-1">VAT Collected</h4>
                  <p className="text-lg font-bold">{formatCurrency(data.vat.collected)}</p>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-red-700 mb-1">VAT Paid</h4>
                  <p className="text-lg font-bold">{formatCurrency(data.vat.paid)}</p>
                </div>
                <div className={`${data.vat.net >= 0 ? 'bg-green-50' : 'bg-yellow-50'} p-4 rounded-lg`}>
                  <h4 className={`text-sm font-medium ${data.vat.net >= 0 ? 'text-green-700' : 'text-yellow-700'} mb-1`}>
                    {data.vat.net >= 0 ? 'VAT Payable' : 'VAT Refundable'}
                  </h4>
                  <p className="text-lg font-bold">{formatCurrency(Math.abs(data.vat.net))}</p>
                </div>
              </div>
              
              <h3 className="font-medium mt-4 mb-2">Monthly Breakdown</h3>
              <div className="border rounded-md overflow-hidden">
                <table className="w-full">
                  <thead className="bg-muted">
                    <tr>
                      <th className="px-4 py-2 text-left">Month</th>
                      <th className="px-4 py-2 text-right">Collected</th>
                      <th className="px-4 py-2 text-right">Paid</th>
                      <th className="px-4 py-2 text-right">Net</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.vat.items.map((item, index) => (
                      <tr key={index} className="border-t">
                        <td className="px-4 py-2">{item.month}</td>
                        <td className="px-4 py-2 text-right">{formatCurrency(item.collected)}</td>
                        <td className="px-4 py-2 text-right">{formatCurrency(item.paid)}</td>
                        <td className={`px-4 py-2 text-right ${item.net >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {formatCurrency(item.net)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="border-t font-medium bg-muted">
                      <td className="px-4 py-2">Total</td>
                      <td className="px-4 py-2 text-right">{formatCurrency(data.vat.collected)}</td>
                      <td className="px-4 py-2 text-right">{formatCurrency(data.vat.paid)}</td>
                      <td className={`px-4 py-2 text-right ${data.vat.net >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {formatCurrency(data.vat.net)}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
              
              <div className="text-sm text-muted-foreground mt-4">
                <p>Based on Uganda's standard VAT rate of 18%.</p>
                <p>VAT returns must be filed by the 15th day of the following month.</p>
              </div>
            </div>
          </TabsContent>
          
          {/* PAYE Tab */}
          <TabsContent value="paye">
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-blue-700 mb-1">Total PAYE</h4>
                <p className="text-lg font-bold">{formatCurrency(data.paye.total)}</p>
              </div>
              
              <h3 className="font-medium mt-4 mb-2">Monthly Breakdown</h3>
              <div className="border rounded-md overflow-hidden">
                <table className="w-full">
                  <thead className="bg-muted">
                    <tr>
                      <th className="px-4 py-2 text-left">Month</th>
                      <th className="px-4 py-2 text-right">Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.paye.items.map((item, index) => (
                      <tr key={index} className="border-t">
                        <td className="px-4 py-2">{item.month}</td>
                        <td className="px-4 py-2 text-right">{formatCurrency(item.amount)}</td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="border-t font-medium bg-muted">
                      <td className="px-4 py-2">Total</td>
                      <td className="px-4 py-2 text-right">{formatCurrency(data.paye.total)}</td>
                    </tr>
                  </tfoot>
                </table>
              </div>
              
              <div className="text-sm text-muted-foreground mt-4">
                <p>PAYE in Uganda uses progressive tax rates:</p>
                <ul className="list-disc ml-5 space-y-1 mt-2">
                  <li>UGX 0 - 235,000: 0%</li>
                  <li>UGX 235,001 - 335,000: 10%</li>
                  <li>UGX 335,001 - 410,000: 20%</li>
                  <li>UGX 410,001 - 10,000,000: 30%</li>
                  <li>Above UGX 10,000,000: 40%</li>
                </ul>
                <p className="mt-2">PAYE must be remitted by the 15th day of the following month.</p>
              </div>
            </div>
          </TabsContent>
          
          {/* Withholding Tax Tab */}
          <TabsContent value="withholding">
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-blue-700 mb-1">Total Withholding Tax</h4>
                <p className="text-lg font-bold">{formatCurrency(data.withholding.total)}</p>
              </div>
              
              <h3 className="font-medium mt-4 mb-2">Monthly Breakdown</h3>
              <div className="border rounded-md overflow-hidden">
                <table className="w-full">
                  <thead className="bg-muted">
                    <tr>
                      <th className="px-4 py-2 text-left">Month</th>
                      <th className="px-4 py-2 text-right">Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.withholding.items.map((item, index) => (
                      <tr key={index} className="border-t">
                        <td className="px-4 py-2">{item.month}</td>
                        <td className="px-4 py-2 text-right">{formatCurrency(item.amount)}</td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="border-t font-medium bg-muted">
                      <td className="px-4 py-2">Total</td>
                      <td className="px-4 py-2 text-right">{formatCurrency(data.withholding.total)}</td>
                    </tr>
                  </tfoot>
                </table>
              </div>
              
              <div className="text-sm text-muted-foreground mt-4">
                <p>Standard withholding tax rate in Uganda is 6% on payments to resident persons.</p>
                <p>Non-resident withholding tax rate is 15% on most payments.</p>
                <p>Withholding tax must be remitted by the 15th day of the following month.</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
