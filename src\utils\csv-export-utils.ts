/**
 * CSV Export Utilities
 * 
 * This module provides utility functions for exporting data to CSV format
 * and downloading CSV files in the browser.
 */

/**
 * Convert an array of objects to CSV format
 * 
 * @param data Array of objects to convert to CSV
 * @param headers Optional custom headers for the CSV file
 * @returns CSV formatted string
 */
export const convertToCSV = <T extends Record<string, string | number | boolean | Date | null | undefined>>(
  data: T[],
  headers?: Record<string, string>
): string => {
  if (data.length === 0) {
    return '';
  }

  // Get all unique keys from the data
  const allKeys = new Set<string>();
  data.forEach(item => {
    Object.keys(item).forEach(key => allKeys.add(key));
  });

  // Use provided headers or generate from keys
  const headerRow: string[] = [];
  const dataKeys: string[] = [];

  if (headers) {
    // Use custom headers
    Object.entries(headers).forEach(([key, label]) => {
      if (allKeys.has(key)) {
        headerRow.push(label);
        dataKeys.push(key);
      }
    });
  } else {
    // Use all keys as headers
    allKeys.forEach(key => {
      headerRow.push(key);
      dataKeys.push(key);
    });
  }

  // Create CSV rows
  const rows: string[] = [headerRow.join(',')];

  // Add data rows
  data.forEach(item => {
    const row = dataKeys.map(key => {
      const value = item[key];
      
      // Handle different value types
      if (value === null || value === undefined) {
        return '';
      } else if (typeof value === 'string') {
        // Escape quotes and wrap in quotes
        return `"${value.replace(/"/g, '""')}"`;
      } else if (typeof value === 'object' && value instanceof Date) {
        return `"${value.toISOString()}"`;
      } else {
        return String(value);
      }
    });
    
    rows.push(row.join(','));
  });

  return rows.join('\n');
};

/**
 * Download data as a CSV file
 * 
 * @param data CSV formatted string
 * @param filename Filename for the downloaded file
 */
export const downloadCSV = (data: string, filename: string): void => {
  // Create a blob with the CSV data
  const blob = new Blob([data], { type: 'text/csv;charset=utf-8;' });
  
  // Create a download link
  const link = document.createElement('a');
  
  // Set up the download link
  if (link.download !== undefined) {
    // Feature detection
    // Create a URL for the blob
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

/**
 * Export data to CSV and trigger download
 * 
 * @param data Array of objects to export
 * @param filename Filename for the downloaded file
 * @param headers Optional custom headers for the CSV file
 */
export const exportToCSV = <T extends Record<string, string | number | boolean | Date | null | undefined>>(
  data: T[],
  filename: string,
  headers?: Record<string, string>
): void => {
  const csv = convertToCSV(data, headers);
  downloadCSV(csv, filename);
};

/**
 * Format a date for CSV export
 * 
 * @param date Date to format
 * @returns Formatted date string (YYYY-MM-DD)
 */
export const formatDateForCSV = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toISOString().split('T')[0];
};

/**
 * Format a currency value for CSV export
 * 
 * @param amount Amount to format
 * @param currency Currency code (default: UGX)
 * @returns Formatted currency string without symbols
 */
export const formatCurrencyForCSV = (
  amount: number,
  currency: string = 'UGX'
): string => {
  return amount.toFixed(2);
};
